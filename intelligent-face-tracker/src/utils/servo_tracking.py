import logging
import threading
import time
from .pid_controller import PIDController
from src.config.settings import SERVO_TRACKING_CONFIG

# 导入配置
try:
    from src.config.settings import SERVO_TRACKING_CONFIG
except ImportError:
    # 如果无法导入配置，使用默认值
    SERVO_TRACKING_CONFIG = {
        'face_timeout': 0.8,
        'movement_threshold': 4,
        'angle_adjust_factor': 0.08,
        'min_servo_interval': 0.012,
        'sleep_interval': 0.03,
        'first_detection_factor': 0.3,  # 首次检测的调整因子
        'max_angle_change': 10.0,       # 单次最大角度变化限制
        'center_deadzone': 15,          # 中心死区半径
        'prediction_enabled': True,     # 是否启用运动预测
        'prediction_time': 0.2,         # 预测未来时间（秒）
        'prediction_weight': 0.7,       # 预测位置权重
        'max_prediction_distance': 200  # 最大预测距离（像素）
    }

logger = logging.getLogger("RDK_YOLO")

class ServoTracker:
    """
    舵机跟踪类，负责处理自动跟踪逻辑
    """
    def __init__(self, servo_controller):
        """
        初始化舵机跟踪器
        
        Args:
            servo_controller: 舵机控制器实例
        """
        self.servo_controller = servo_controller
        self.tracking_thread = None
        self.auto_tracking = False
        self.coord_calculator = None
        
        # 创建PID控制器
        self.pid_pan = PIDController()
        self.pid_tilt = PIDController()
        
        # 从配置文件读取跟踪参数
        self.face_timeout = SERVO_TRACKING_CONFIG['face_timeout']
        self.movement_threshold = SERVO_TRACKING_CONFIG['movement_threshold']
        self.angle_adjust_factor = SERVO_TRACKING_CONFIG['angle_adjust_factor']
        self.min_servo_interval = SERVO_TRACKING_CONFIG['min_servo_interval']
        self.sleep_interval = SERVO_TRACKING_CONFIG['sleep_interval']
        self.first_detection_factor = SERVO_TRACKING_CONFIG.get('first_detection_factor', 0.3)
        self.max_angle_change = SERVO_TRACKING_CONFIG.get('max_angle_change', 10.0)
        self.center_deadzone = SERVO_TRACKING_CONFIG.get('center_deadzone', 15)
        
        # 加载预测相关配置
        self.prediction_enabled = SERVO_TRACKING_CONFIG.get('prediction_enabled', True)
        self.prediction_time = SERVO_TRACKING_CONFIG.get('prediction_time', 0.2)
        self.prediction_weight = SERVO_TRACKING_CONFIG.get('prediction_weight', 0.7)
        self.max_prediction_distance = SERVO_TRACKING_CONFIG.get('max_prediction_distance', 200)
        
        self.last_servo_move_time = 0
        self.is_first_detection = True  # 首次检测标志
        self.last_bbox = None
        self.last_offset_x = 0
        self.last_offset_y = 0
        
    def start_tracking(self, coord_calculator):
        """
        开始自动跟踪
        """
        if self.auto_tracking:
            logger.info("自动跟踪已经在运行中")
            return True
            
        if not self.servo_controller.is_connected():
            logger.error("未连接到串口设备，无法启动自动跟踪")
            return False
            
        logger.info("开始自动跟踪")
        self.auto_tracking = True
        self.coord_calculator = coord_calculator
        
        # 重置坐标计算器和PID控制器
        if self.coord_calculator:
            self.coord_calculator.latest_bbox = None
            self.coord_calculator.latest_update_time = 0
        
        self.pid_pan.reset()
        self.pid_tilt.reset()
        self.is_first_detection = True  # 重置首次检测标志
        self.last_bbox = None
        self.last_offset_x = 0
        self.last_offset_y = 0
        
        # 创建自动跟踪线程
        self.tracking_thread = threading.Thread(target=self._tracking_task, daemon=True)
        self.tracking_thread.start()
        
        return True
    
    def stop_tracking(self):
        """
        停止自动跟踪
        """
        if not self.auto_tracking:
            logger.info("自动跟踪未在运行")
            return True
            
        logger.info("停止自动跟踪")
        self.auto_tracking = False
        
        if self.tracking_thread and self.tracking_thread.is_alive():
            self.tracking_thread.join(timeout=1.0)
            
        return True
        
    def _tracking_task(self):
        """自动跟踪任务"""
        logger.info("自动跟踪线程已启动")
        
        last_update_time = time.time()
        
        try:
            while self.auto_tracking:
                current_time = time.time()
                
                # 获取最新的边界框
                if self.coord_calculator and self.coord_calculator.latest_bbox is not None:
                    bbox_timestamp = self.coord_calculator.latest_update_time
                    
                    # 检查数据是否过期
                    if current_time - bbox_timestamp < self.face_timeout:
                        # 计算时间差
                        time_diff = current_time - last_update_time
                        last_update_time = current_time
                        
                        # 获取当前边界框
                        bbox = self.coord_calculator.latest_bbox
                        self.last_bbox = bbox  # 保存最后一个有效的边界框
                        
                        # 计算偏移量
                        offset_x, offset_y, distance = self.coord_calculator.calculate_offset(bbox)
                        self.last_offset_x = offset_x
                        self.last_offset_y = offset_y
                        
                        # 如果启用预测和目标ID存在
                        if (self.prediction_enabled and 
                            self.coord_calculator.current_target_id is not None and
                            self.coord_calculator.current_target_id in self.coord_calculator.target_history):
                            
                            target_id = self.coord_calculator.current_target_id
                            target_info = self.coord_calculator.target_history[target_id]
                            
                            # 检查是否有速度信息
                            if "velocity" in target_info and "velocity_magnitude" in target_info:
                                velocity = target_info["velocity"]
                                velocity_magnitude = target_info["velocity_magnitude"]
                                
                                # 如果速度足够大，进行提前量预测
                                if velocity_magnitude > 10:  # 速度阈值（像素/秒）
                                    # 预测目标在未来prediction_time秒后的位置偏移
                                    prediction_x = offset_x + velocity[0] * self.prediction_time
                                    prediction_y = offset_y + velocity[1] * self.prediction_time
                                    
                                    # 计算预测距离
                                    prediction_distance = ((prediction_x - offset_x)**2 + 
                                                        (prediction_y - offset_y)**2)**0.5
                                    
                                    # 限制预测距离，防止过大的预测导致过冲
                                    if prediction_distance > self.max_prediction_distance:
                                        scale = self.max_prediction_distance / prediction_distance
                                        prediction_x = offset_x + (prediction_x - offset_x) * scale
                                        prediction_y = offset_y + (prediction_y - offset_y) * scale
                                    
                                    # 动态调整预测权重，速度越大权重越高
                                    dynamic_weight = min(self.prediction_weight * velocity_magnitude / 50, 0.8)
                                    
                                    # 融合当前位置和预测位置
                                    offset_x = offset_x * (1 - dynamic_weight) + prediction_x * dynamic_weight
                                    offset_y = offset_y * (1 - dynamic_weight) + prediction_y * dynamic_weight
                                    
                                    logger.debug(f"应用预测偏移: 原始=({self.last_offset_x:.1f}, {self.last_offset_y:.1f}), "
                                               f"预测=({prediction_x:.1f}, {prediction_y:.1f}), "
                                               f"融合=({offset_x:.1f}, {offset_y:.1f}), "
                                               f"速度={velocity_magnitude:.1f}, 权重={dynamic_weight:.2f}")
                        
                        # 检查目标是否在中心死区内，如果是则不进行追踪
                        if distance < self.center_deadzone:
                            logger.debug(f"目标在中心死区内(距离={distance:.1f}像素)，暂停追踪")
                            time.sleep(self.sleep_interval)
                            continue
                        
                        # 如果偏移量小于阈值，不进行调整
                        if abs(offset_x) < self.movement_threshold and abs(offset_y) < self.movement_threshold:
                            time.sleep(self.sleep_interval)
                            continue
                        
                        # 使用PID控制器计算调整量
                        pan_delta = self.pid_pan.update(offset_x, time_diff)
                        tilt_delta = self.pid_tilt.update(offset_y, time_diff)
                        
                        # 对首次检测进行特殊处理
                        adjust_factor = self.angle_adjust_factor
                        if self.is_first_detection:
                            # 首次检测使用较小的调整因子减少过冲
                            adjust_factor *= self.first_detection_factor
                            logger.debug("首次检测，使用降低的调整因子")
                            self.is_first_detection = False
                        
                        # 计算目标角度
                        pan_delta_angle = pan_delta * adjust_factor
                        tilt_delta_angle = tilt_delta * adjust_factor
                        
                        # 限制单次角度变化幅度，防止过大调整
                        pan_delta_angle = max(-self.max_angle_change, min(self.max_angle_change, pan_delta_angle))
                        tilt_delta_angle = max(-self.max_angle_change, min(self.max_angle_change, tilt_delta_angle))
                        
                        pan_angle = self.servo_controller.current_pan - pan_delta_angle
                        tilt_angle = self.servo_controller.current_tilt - tilt_delta_angle
                        
                        # 移动舵机 - 修改为同时控制两个舵机
                        if current_time - self.last_servo_move_time >= self.min_servo_interval:
                            # 创建舵机角度字典，同时控制两个舵机
                            servo_angles = {
                                self.servo_controller.protocol.PAN_SERVO_PIN: pan_angle,
                                self.servo_controller.protocol.TILT_SERVO_PIN: tilt_angle
                            }
                            # 同时发送两个舵机的控制命令
                            self.servo_controller.move_servos_together(servo_angles)
                            self.last_servo_move_time = current_time
                            
                            logger.debug(f"舵机角度同步更新: pan={pan_angle:.1f}, tilt={tilt_angle:.1f}, 首次检测={self.is_first_detection}, 距离={distance:.1f}")
                    else:
                        # 数据过期，但检查是否可以使用预测位置
                        if (self.prediction_enabled and 
                            self.coord_calculator.current_target_id is not None and 
                            self.last_bbox is not None):
                            
                            expiration_time = current_time - bbox_timestamp
                            
                            # 如果过期时间不是太长，使用速度预测位置
                            if expiration_time < self.face_timeout * 2:
                                # 计算时间差
                                time_diff = current_time - last_update_time
                                last_update_time = current_time
                                
                                # 获取目标信息
                                target_id = self.coord_calculator.current_target_id
                                
                                if (target_id in self.coord_calculator.target_history and 
                                    "velocity" in self.coord_calculator.target_history[target_id]):
                                    
                                    velocity = self.coord_calculator.target_history[target_id]["velocity"]
                                    velocity_magnitude = self.coord_calculator.target_history[target_id]["velocity_magnitude"]
                                    
                                    # 计算预测偏移量
                                    pred_offset_x = self.last_offset_x + velocity[0] * expiration_time
                                    pred_offset_y = self.last_offset_y + velocity[1] * expiration_time
                                    
                                    # 使用PID控制器计算调整量
                                    pan_delta = self.pid_pan.update(pred_offset_x, time_diff)
                                    tilt_delta = self.pid_tilt.update(pred_offset_y, time_diff)
                                    
                                    # 使用较小的调整因子降低预测追踪的响应速度
                                    adjust_factor = self.angle_adjust_factor * 0.7
                                    
                                    # 计算目标角度
                                    pan_delta_angle = pan_delta * adjust_factor
                                    tilt_delta_angle = tilt_delta * adjust_factor
                                    
                                    # 限制单次角度变化幅度
                                    pan_delta_angle = max(-self.max_angle_change/2, min(self.max_angle_change/2, pan_delta_angle))
                                    tilt_delta_angle = max(-self.max_angle_change/2, min(self.max_angle_change/2, tilt_delta_angle))
                                    
                                    pan_angle = self.servo_controller.current_pan - pan_delta_angle
                                    tilt_angle = self.servo_controller.current_tilt - tilt_delta_angle
                                    
                                    # 移动舵机 - 修改为同时控制两个舵机
                                    if current_time - self.last_servo_move_time >= self.min_servo_interval * 1.5:
                                        # 创建舵机角度字典，同时控制两个舵机
                                        servo_angles = {
                                            self.servo_controller.protocol.PAN_SERVO_PIN: pan_angle,
                                            self.servo_controller.protocol.TILT_SERVO_PIN: tilt_angle
                                        }
                                        # 同时发送两个舵机的控制命令
                                        self.servo_controller.move_servos_together(servo_angles)
                                        self.last_servo_move_time = current_time
                                        
                                        logger.debug(f"使用预测位置更新舵机: pan={pan_angle:.1f}, tilt={tilt_angle:.1f}, "
                                                   f"数据过期时间={expiration_time:.3f}秒, 预测偏移=({pred_offset_x:.1f}, {pred_offset_y:.1f})")
                                    continue
                        
                        # 数据过期，重置PID控制器
                        self.pid_pan.reset()
                        self.pid_tilt.reset()
                        self.coord_calculator.latest_bbox = None
                        self.coord_calculator.latest_update_time = 0
                        self.is_first_detection = True  # 重置首次检测标志
                        self.last_bbox = None
                        self.last_offset_x = 0
                        self.last_offset_y = 0
                
                time.sleep(self.sleep_interval)
                
        except Exception as e:
            logger.error(f"自动跟踪线程出错: {str(e)}")
            self.auto_tracking = False
