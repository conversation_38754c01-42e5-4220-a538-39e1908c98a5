import numpy as np
import logging

logger = logging.getLogger("RDK_YOLO")

class KalmanFilter:
    """
    卡尔曼滤波器类，用于预测目标运动
    """
    def __init__(self, dt=0.1, process_noise_scale=0.01, measurement_noise_scale=0.1):
        """
        初始化卡尔曼滤波器
        
        Args:
            dt (float): 时间间隔（秒）
            process_noise_scale (float): 过程噪声比例系数
            measurement_noise_scale (float): 测量噪声比例系数
        """
        # 状态向量 [x, y, vx, vy]
        self.state = np.zeros((4, 1))
        self.initialized = False
        
        # 状态转移矩阵
        self.F = np.array([
            [1, 0, dt, 0],
            [0, 1, 0, dt],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ])
        
        # 测量矩阵 - 我们只能测量位置 (x,y)
        self.H = np.array([
            [1, 0, 0, 0],
            [0, 1, 0, 0]
        ])
        
        # 过程噪声协方差
        self.Q = np.array([
            [dt**4/4, 0, dt**3/2, 0],
            [0, dt**4/4, 0, dt**3/2],
            [dt**3/2, 0, dt**2, 0],
            [0, dt**3/2, 0, dt**2]
        ]) * process_noise_scale
        
        # 测量噪声协方差
        self.R = np.eye(2) * measurement_noise_scale
        
        # 状态估计协方差
        self.P = np.eye(4)
        
        # 预测状态和协方差
        self.x_pred = None
        self.P_pred = None
        
        # 卡尔曼增益
        self.K = None
        
        # 速度估计信息
        self.current_velocity = np.zeros(2)
        self.last_position = None
        self.last_time = None
        
        logger.debug(f"卡尔曼滤波器初始化: dt={dt}, 过程噪声={process_noise_scale}, 测量噪声={measurement_noise_scale}")
    
    def init(self, position, time_stamp):
        """
        初始化滤波器状态
        
        Args:
            position (tuple): 目标位置 (x, y)
            time_stamp (float): 时间戳
        """
        self.state = np.array([[position[0]], [position[1]], [0], [0]])
        self.last_position = position
        self.last_time = time_stamp
        self.initialized = True
        logger.debug(f"卡尔曼滤波器初始化状态: 位置=({position[0]}, {position[1]})")
    
    def update(self, position, time_stamp):
        """
        使用新的测量值更新卡尔曼滤波器
        
        Args:
            position (tuple): 目标位置 (x, y)
            time_stamp (float): 时间戳
            
        Returns:
            tuple: 估计的位置 (x, y) 和速度 (vx, vy)
        """
        # 如果还没有初始化，使用当前观测值初始化
        if not self.initialized:
            self.init(position, time_stamp)
            return position, (0, 0)
        
        # 计算时间间隔
        dt = time_stamp - self.last_time
        if dt <= 0:
            dt = 0.001  # 防止除零
            
        # 更新状态转移矩阵中的dt
        self.F[0, 2] = dt
        self.F[1, 3] = dt
        
        # 更新过程噪声协方差中的dt相关项
        self.Q[0, 0] = dt**4/4
        self.Q[0, 2] = dt**3/2
        self.Q[1, 1] = dt**4/4
        self.Q[1, 3] = dt**3/2
        self.Q[2, 0] = dt**3/2
        self.Q[2, 2] = dt**2
        self.Q[3, 1] = dt**3/2
        self.Q[3, 3] = dt**2
        
        # 预测步骤
        self.x_pred = self.F @ self.state
        self.P_pred = self.F @ self.P @ self.F.T + self.Q
        
        # 计算卡尔曼增益
        S = self.H @ self.P_pred @ self.H.T + self.R
        self.K = self.P_pred @ self.H.T @ np.linalg.inv(S)
        
        # 更新步骤
        measurement = np.array([[position[0]], [position[1]]])
        y = measurement - self.H @ self.x_pred
        self.state = self.x_pred + self.K @ y
        self.P = (np.eye(4) - self.K @ self.H) @ self.P_pred
        
        # 更新速度信息
        self.current_velocity = (self.state[2, 0], self.state[3, 0])
        
        # 保存上一次的位置和时间
        self.last_position = position
        self.last_time = time_stamp
        
        # 返回估计的位置和速度
        estimated_position = (self.state[0, 0], self.state[1, 0])
        estimated_velocity = self.current_velocity
        
        logger.debug(f"卡尔曼滤波器更新: 测量=({position[0]:.1f}, {position[1]:.1f}), "
                    f"估计=({estimated_position[0]:.1f}, {estimated_position[1]:.1f}), "
                    f"速度=({estimated_velocity[0]:.1f}, {estimated_velocity[1]:.1f}), dt={dt:.3f}")
        
        return estimated_position, estimated_velocity
    
    def predict(self, dt=None):
        """
        预测未来位置
        
        Args:
            dt (float, optional): 预测时间（秒）。默认使用上次更新的时间间隔。
            
        Returns:
            tuple: 预测的位置 (x, y)
        """
        if not self.initialized:
            return None
        
        # 如果未指定dt，使用默认的dt
        if dt is None:
            dt = self.F[0, 2]
        
        # 创建预测用的状态转移矩阵
        F_pred = np.array([
            [1, 0, dt, 0],
            [0, 1, 0, dt],
            [0, 0, 1, 0],
            [0, 0, 0, 1]
        ])
        
        # 预测未来状态
        future_state = F_pred @ self.state
        
        # 返回预测的位置
        predicted_position = (future_state[0, 0], future_state[1, 0])
        
        logger.debug(f"卡尔曼滤波器预测: dt={dt:.3f}, 预测位置=({predicted_position[0]:.1f}, {predicted_position[1]:.1f})")
        
        return predicted_position
    
    def reset(self):
        """重置滤波器状态"""
        self.state = np.zeros((4, 1))
        self.P = np.eye(4)
        self.initialized = False
        self.last_position = None
        self.last_time = None
        self.current_velocity = np.zeros(2)
        logger.debug("卡尔曼滤波器重置") 