<!DOCTYPE html>
<html>
    <head>
        <title>RDK YOLO人脸检测与舵机控制</title>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
        <meta name="description" content="RDK YOLO人脸检测与舵机控制系统">
        <meta name="theme-color" content="#2196F3">
        
        <!-- 从后端获取舵机引脚号 -->
        <script>
            let PAN_PIN = {{ pan_pin }};
            let TILT_PIN = {{ tilt_pin }};
        </script>
    </head>
    <body>
        <div class="container">
            <h1>RDK YOLO人脸检测与舵机控制</h1>
            
            <div class="video-container">
                <img src="{{ url_for('video_feed') }}" alt="视频流">
            </div>
            
            <div class="status" id="status">
                系统就绪，使用滑块控制舵机位置
            </div>
            
            <div class="content-wrapper">
                <div class="controls">
                    <div class="control-group">
                        <h3>舵机控制</h3>
                        <div class="slider-container">
                            <label for="pan-slider">水平控制 (左右)</label>
                            <div class="slider-info">
                                <span>0°</span>
                                <span class="slider-value">当前角度: <span id="pan-value">135</span>°</span>
                                <span>270°</span>
                            </div>
                            <input type="range" min="0" max="270" value="135" id="pan-slider" 
                                   oninput="updateSliderValue('pan-slider', 'pan-value')" 
                                   onchange="sendSliderValue(PAN_PIN, 'pan-slider')"
                                   aria-label="水平舵机控制">
                        </div>
                        
                        <div class="slider-container">
                            <label for="tilt-slider">垂直控制 (上下)</label>
                            <div class="slider-info">
                                <span>0°</span>
                                <span class="slider-value">当前角度: <span id="tilt-value">135</span>°</span>
                                <span>270°</span>
                            </div>
                            <input type="range" min="0" max="270" value="135" id="tilt-slider" 
                                   oninput="updateSliderValue('tilt-slider', 'tilt-value')" 
                                   onchange="sendSliderValue(TILT_PIN, 'tilt-slider')"
                                   aria-label="垂直舵机控制">
                        </div>
                        
                        <div class="buttons">
                            <button onclick="resetPosition()" aria-label="重置位置">重置位置</button>
                            <button onclick="runSelfTest()" class="primary" aria-label="云台自检">云台自检</button>
                            <button id="track-face-btn" onclick="toggleFaceTracking()" class="tracking-btn" aria-label="切换人脸跟踪">启用人脸跟踪</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script src="{{ url_for('static', filename='js/servo-control.js') }}"></script>
    </body>
</html> 