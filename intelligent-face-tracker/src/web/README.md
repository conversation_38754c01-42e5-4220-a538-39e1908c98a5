### Web应用模块 (`web/`)

基于Flask的Web应用模块，提供实时视频流显示和舵机控制界面。

#### 目录结构
```
web/
├── app.py              # Flask应用主程序
├── templates/          # HTML模板目录
│   ├── index.html      # 主页面模板
│   └── disabled.html   # 舵机禁用状态页面
└── static/             # 静态资源目录
    ├── css/            # 样式文件
    ├── js/             # JavaScript文件
    ├── models/         # 模型文件
    ├── debug.html      # 调试页面
    └── display.html    # 显示页面
```

#### 核心功能
- 实时视频流显示
- 舵机控制接口
- 自动跟踪控制
- 云台自检功能
- 调试信息显示
- 多目标跟踪管理
- 目标可视化显示

#### 技术特点
- 基于Flask的RESTful API
- 实时视频流处理
- 异步舵机控制
- 自适应图像压缩
- 完整的错误处理
- 多目标跟踪支持
- 智能目标选择

#### 主要接口
- `/`: 主页面
- `/debug`: 调试页面
- `/video_feed`: 实时视频流
- `/move_servo`: 舵机控制
- `/reset_position`: 重置舵机位置
- `/send_test_data`: 云台自检
- `/toggle_tracking`: 切换自动跟踪

#### 视频处理流程
1. 图像采集
   - 从摄像头读取视频帧
   - 错误处理和状态检查

2. 目标检测
   - YOLO模型推理
   - 多目标检测结果处理
   - 目标信息更新

3. 目标跟踪
   - 多目标优先级计算
   - 最优目标选择
   - 目标切换管理

4. 可视化处理
   - 检测框绘制
   - 跟踪目标标记
   - 中心点和偏移信息显示
   - 状态信息叠加

5. 图像优化
   - 分辨率自适应调整
   - JPEG压缩质量控制
   - 实时流传输优化

#### 舵机控制功能
1. 基础控制
   - 舵机连接管理
   - 角度精确控制
   - 位置重置功能

2. 自动跟踪
   - 目标偏移计算
   - 平滑舵机控制
   - 自适应响应速度

3. 自检功能
   - 云台运动测试
   - 舵机范围检查
   - 位置校准

#### 性能优化
- 图像分辨率自适应
- JPEG压缩质量控制
- 异步舵机控制
- 实时状态反馈
- 多目标处理优化
- 内存使用优化

#### 安全特性
- 舵机控制权限验证
- 输入参数验证
- 异常状态处理
- 资源自动释放
- 目标切换保护
- 舵机运动限制

#### 调试功能
- 实时状态监控
- 目标跟踪可视化
- 性能指标显示
- 错误日志记录
- 参数实时调整

#### 使用说明
1. 启动应用
   ```bash
   python app.py [参数]
   ```

2. 参数配置
   - `--model-path`: 模型文件路径
   - `--serial-port`: 串口设备路径
   - `--baudrate`: 串口波特率
   - `--no-servo`: 禁用舵机控制

3. 访问界面
   - 主界面：`http://<设备IP>:5000`
   - 调试界面：`http://<设备IP>:5000/debug`

4. 注意事项
   - 确保摄像头可用
   - 正确配置串口参数
   - 检查舵机连接状态
   - 监控系统资源使用 