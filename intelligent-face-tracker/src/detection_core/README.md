# detection_core 目录说明

本目录主要用于实现目标检测相关的核心模型逻辑，包含模型的基础加载、推理、后处理等功能模块。

## 目录结构
- `base_model.py`：基础模型类，封装了模型的加载、输入输出张量管理、图像预处理（如resize、格式转换）、推理等通用功能。所有具体检测模型建议继承此类。
- `yolo_detect.py`：YOLOv8检测模型实现，继承自BaseModel，负责模型推理输出的解码、后处理（如阈值筛选、NMS、坐标还原）、检测框绘制等。

## 推荐使用方式
1. **模型加载与推理**：
   - 先通过`YOLO11_Detect`类加载模型文件。
   - 使用`forward`方法进行推理，得到原始输出。
   - 使用`postProcess`方法对输出进行后处理，获得最终的检测结果（类别、置信度、边界框）。

2. **典型用法示例**：
```python
from detection_core.yolo_detect import YOLO11_Detect

detector = YOLO11_Detect(model_file='your_model.bin', conf=0.5, iou=0.45)
input_tensor = ...  # 预处理后的输入张量
outputs = detector.forward(input_tensor)
ids, scores, bboxes = detector.postProcess(outputs)
```

3. **检测框绘制**：
可调用`draw_detection`函数在原图上绘制检测结果。

## 适用场景
- 适用于需要集成YOLOv8等深度学习目标检测模型的项目。
- 可作为更复杂检测/跟踪系统的基础模块。

## 其他说明
- 本目录仅包含核心检测模型相关代码，具体的业务逻辑、数据流、可视化等请在上层模块实现。
- `__pycache__`目录为Python自动生成，无需手动管理。
