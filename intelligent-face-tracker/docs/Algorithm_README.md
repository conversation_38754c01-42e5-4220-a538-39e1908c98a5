# 目标追踪与坐标计算算法文档

## 目录
- [1. 算法概述](#1-算法概述)
- [2. 多目标追踪系统](#2-多目标追踪系统)
  - [2.1 目标选择算法](#21-目标选择算法)
  - [2.2 目标关联算法](#22-目标关联算法)
  - [2.3 目标切换策略](#23-目标切换策略)
  - [2.4 目标超时清理](#24-目标超时清理)
- [3. 坐标计算](#3-坐标计算)
  - [3.1 偏移量计算](#31-偏移量计算)
  - [3.2 方向判断](#32-方向判断)
- [4. 运动预测系统](#4-运动预测系统)
  - [4.1 卡尔曼滤波器原理](#41-卡尔曼滤波器原理)
  - [4.2 目标位置预测](#42-目标位置预测)
  - [4.3 舵机预测控制](#43-舵机预测控制)
- [5. 算法参数调优](#5-算法参数调优)

## 1. 算法概述

`CoordinateCalculator` 类实现了一套完整的多目标追踪与坐标计算系统，主要用于在视频中追踪多个检测目标并保持稳定性。该系统能够在复杂场景中准确地选择并持续追踪最优目标，避免目标频繁切换，提高追踪稳定性。

核心功能包括：
- 基于多种因素的目标评分与选择
- 目标ID关联与持久化
- 目标位置跟踪与平滑切换
- 坐标偏移计算和方向判断
- 基于卡尔曼滤波的运动预测
- 高速目标追踪优化

## 2. 多目标追踪系统

### 2.1 目标选择算法

目标选择算法基于多因素综合评分机制，权衡多个目标的不同特性，选择最优目标进行追踪。

#### 评分因素

评分系统综合考虑以下四个关键因素：

1. **目标置信度**：目标检测算法提供的置信度分数，反映目标被正确检测的可能性
2. **目标大小**：目标边界框的面积（归一化处理），通常较大的目标更可能是主要关注对象
3. **目标持久性**：目标连续被检测到的次数，长时间稳定存在的目标更可能是需要追踪的目标
4. **位置连续性**：目标位置与历史位置的匹配度，反映目标运动的平滑性和一致性

#### 评分计算公式

目标的最终评分通过以下公式计算：

```
最终评分 = 基础评分 + 持久性评分 × 持久性权重 + 位置连续性评分 × 位置权重 + 当前目标加成
```

其中：
- 基础评分 = 置信度权重 × 置信度 + 大小权重 × 归一化面积
- 持久性评分 = 目标连续检测次数 / 最大历史计数
- 位置连续性评分 = 1.0 - min(当前位置与上次位置的距离 / 最大位置距离, 1.0)
- 当前目标加成：如果是当前正在追踪的目标，给予额外的评分加成（等于目标切换阈值）

### 2.2 目标关联算法

目标关联算法负责将当前帧检测到的目标与历史记录中的目标建立对应关系，实现目标ID的持久化跟踪。

#### 关联步骤

1. **距离矩阵计算**：
   - 计算当前帧每个目标与历史记录中每个目标之间的欧氏距离
   - 距离矩阵 D[i,j] 表示当前帧第i个目标与历史记录中第j个目标的距离

2. **最近邻匹配**：
   - 对于当前帧的每个目标，在未分配的历史目标中寻找距离最近的目标
   - 如果找到的最近距离小于最大位置距离阈值，则认为匹配成功
   - 匹配成功后，更新该历史目标的位置、时间戳和其他属性
   - 计算位置连续性评分：`position_score = 1.0 - min(min_distance / max_position_distance, 1.0)`

3. **新目标创建**：
   - 如果当前帧的目标无法与任何历史目标匹配，则创建新的目标ID
   - 为新目标初始化计数、位置、时间戳等属性

### 2.3 目标切换策略

为了避免因短暂的评分波动导致频繁切换追踪目标，算法实现了一套目标切换策略：

1. 当新的最高评分目标出现时，不会立即切换，而是与当前正在追踪的目标进行评分比较
2. 只有当新目标的评分超过当前目标评分达到切换阈值（`target_switch_threshold`）时，才切换目标
3. 否则，即使新目标评分略高，仍继续追踪当前目标，保持追踪的稳定性

这种策略类似于"滞后比较器"(hysteresis comparator)，能有效避免因评分的微小波动导致的目标频繁切换问题。

### 2.4 目标超时清理

为了防止目标列表无限增长，算法实现了目标超时清理机制：

1. 定期检查每个目标的最后出现时间
2. 如果当前时间与目标最后出现时间的差值超过超时阈值（`target_timeout`），则将该目标从历史记录中删除
3. 如果当前正在追踪的目标被移除，则重置当前目标ID

这种机制确保了系统只追踪最近活跃的目标，减少了内存占用和计算负担。

## 3. 坐标计算

### 3.1 偏移量计算

偏移量计算用于确定目标相对于画面中心的位置关系：

1. 计算目标边界框的中心点坐标：
   ```
   target_center_x = (x1 + x2) // 2
   target_center_y = (y1 + y2) // 2
   ```

2. 计算中心点与画面中心的偏移量：
   ```
   offset_x = target_center_x - center_x
   offset_y = target_center_y - center_y
   ```

3. 计算偏移距离：
   ```
   distance = sqrt(offset_x² + offset_y²)
   ```

偏移量的符号具有明确的方向含义：
- `offset_x > 0`：目标在画面中心右侧
- `offset_x < 0`：目标在画面中心左侧
- `offset_y > 0`：目标在画面中心下方
- `offset_y < 0`：目标在画面中心上方

### 3.2 方向判断

方向判断算法根据偏移量将目标相对于中心的位置转换为直观的方向描述：

1. 如果 x 和 y 的偏移量都小于方向阈值（`direction_threshold`），则判定为"center"（中心）
2. 否则根据 x 和 y 的偏移量判断方向：
   - `offset_x > threshold`：right（右）
   - `offset_x < -threshold`：left（左）
   - `offset_y > threshold`：down（下）
   - `offset_y < -threshold`：up（上）
3. 如果同时满足水平和垂直方向的条件，则组合方向描述，如"left-up"（左上）

## 4. 运动预测系统

### 4.1 卡尔曼滤波器原理

卡尔曼滤波器是一种递归状态估计器，通过融合测量值和预测值来估计系统的真实状态。在目标追踪中，卡尔曼滤波器用于预测目标的运动，尤其适合处理快速运动的目标。

#### 状态空间模型

本系统使用的卡尔曼滤波器采用二阶系统模型，状态向量包含目标的位置和速度：

```
状态向量 X = [x, y, vx, vy]^T
```

其中：
- (x, y) 是目标中心坐标
- (vx, vy) 是目标在x和y方向的速度

#### 滤波器更新流程

卡尔曼滤波器的更新包含两个主要步骤：预测和测量更新

1. **预测步骤**：
   - 根据上一时刻的状态和状态转移矩阵预测当前状态
   - 更新状态协方差矩阵
   ```
   X_pred = F * X
   P_pred = F * P * F^T + Q
   ```

2. **测量更新步骤**：
   - 计算卡尔曼增益
   - 根据测量值更新状态估计
   - 更新协方差矩阵
   ```
   K = P_pred * H^T * (H * P_pred * H^T + R)^-1
   X = X_pred + K * (Z - H * X_pred)
   P = (I - K * H) * P_pred
   ```

其中：
- F: 状态转移矩阵，描述系统如何从一个状态转移到下一个状态
- H: 测量矩阵，描述如何从状态变量得到测量值
- Q: 过程噪声协方差矩阵，表示预测过程中的不确定性
- R: 测量噪声协方差矩阵，表示测量过程中的不确定性
- K: 卡尔曼增益，决定了预测值和测量值的融合权重

### 4.2 目标位置预测

目标位置预测功能允许系统在目标短暂消失或快速运动时，根据历史运动模式预测目标的未来位置，提高追踪的连续性和稳定性。

#### 预测机制

1. **速度估计**：
   - 卡尔曼滤波器持续估计目标的速度向量 (vx, vy)
   - 速度信息存储在目标历史记录中，用于后续预测

2. **位置预测**：
   - 当目标丢失时，使用最后估计的速度和位置预测目标的当前位置
   - 预测公式：predicted_position = last_position + velocity * dt
   - 根据预测位置重建目标边界框

3. **预测置信度衰减**：
   - 每次使用预测位置更新目标，降低预测置信度
   - 当置信度低于阈值时，停止预测，避免长时间跟踪不存在的目标

4. **预测距离限制**：
   - 限制预测距离不超过最大预测距离，防止过大预测导致追踪偏离
   - 超过距离限制时按比例缩放预测位置

### 4.3 舵机预测控制

舵机预测控制将目标运动预测与舵机控制相结合，实现对快速运动目标的提前追踪，减少系统延迟的影响。

#### 控制策略

1. **提前量控制**：
   - 对于快速运动的目标，根据速度大小计算提前量
   - 舵机瞄准目标预测的未来位置，而非当前位置

2. **动态权重调整**：
   - 速度越大，预测位置的权重越高
   - 动态权重计算：dynamic_weight = min(base_weight * velocity_magnitude / threshold, max_weight)

3. **位置融合**：
   - 将当前测量位置与预测位置进行加权融合
   - 融合公式：final_position = current_position * (1 - weight) + predicted_position * weight

4. **预测追踪持续**：
   - 当目标暂时丢失但数据过期时间不长时，继续使用速度信息进行预测追踪
   - 使用降低的响应速度和更严格的角度限制，保证稳定性

## 5. 算法参数调优

系统性能很大程度上取决于参数配置，关键参数包括：

### 目标追踪参数

| 参数 | 说明 | 调整建议 |
|------|------|----------|
| `confidence_weight` | 置信度权重 | 增大该值使系统更倾向于选择高置信度目标 |
| `size_weight` | 目标大小权重 | 增大该值使系统更倾向于选择较大目标 |
| `persistence_weight` | 持久性权重 | 增大该值提高已追踪目标的稳定性 |
| `position_weight` | 位置连续性权重 | 增大该值减少因位置跳变导致的目标切换 |
| `target_switch_threshold` | 目标切换阈值 | 增大该值减少目标切换频率，提高稳定性 |
| `target_timeout` | 目标超时时间 | 增大该值延长目标在视野外后仍被记忆的时间 |
| `max_history_count` | 最大历史计数 | 增大该值提高长期存在目标的权重上限 |
| `max_position_distance` | 最大位置距离 | 调整该值以适应目标运动速度，过大会导致ID混淆，过小会导致频繁创建新ID |
| `direction_threshold` | 方向判断阈值 | 调整该值改变方向判定的灵敏度 |

### 卡尔曼滤波器参数

| 参数 | 说明 | 调整建议 |
|------|------|----------|
| `dt` | 时间步长 | 根据系统帧率调整，通常为1/帧率 |
| `process_noise_scale` | 过程噪声系数 | 增大该值使滤波器更快适应目标变化，但会增加噪声敏感性 |
| `measurement_noise_scale` | 测量噪声系数 | 增大该值减少对测量噪声的敏感性，但会降低响应速度 |
| `prediction_time` | 预测未来时间 | 增大该值增加预测提前量，适用于更快速运动的目标 |
| `prediction_weight` | 预测位置权重 | 增大该值增强预测的影响，减小该值增强当前测量的影响 |
| `max_prediction_distance` | 最大预测距离 | 调整该值限制预测距离，防止过度预测 |

### PID控制器参数

| 参数 | 说明 | 调整建议 |
|------|------|----------|
| `kp` | 比例系数 | 增大该值提高响应速度，但过大会导致振荡 |
| `ki` | 积分系数 | 增大该值减少静态误差，但过大会导致积分饱和和过冲 |
| `kd` | 微分系数 | 增大该值增加阻尼，减少振荡，但过大会放大噪声 |
| `max_integral` | 积分上限 | 调整该值限制积分作用的最大影响 |
| `first_update_factor` | 首次更新缩减因子 | 减小该值降低首次探测的响应强度，减少过冲 |

参数调优应针对具体应用场景，综合考虑目标运动特性、相机移动情况、画面尺寸等因素。对于快速运动的目标场景，建议增大预测权重和提前时间；对于稳定场景，则可减小这些值以获得更平稳的追踪效果。
