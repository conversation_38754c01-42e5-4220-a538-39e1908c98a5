#!/bin/bash

# 检查虚拟环境是否存在，不存在则创建
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
    echo "Virtual environment created."
    
    # 激活虚拟环境并安装依赖
    echo "Activating virtual environment and installing dependencies..."
    source venv/bin/activate
    pip install -r requirements.txt
    echo "Dependencies installed."
else
    # 如果虚拟环境已存在，直接激活
    source venv/bin/activate
fi

# 执行主程序并传递所有参数
echo "Starting the application..."
python main.py "$@"