import httpx
from mcp.server.fastmcp import FastMCP

# 创建一个名为 "FaceTracker" 的 MCP 服务器
mcp = FastMCP("FaceTracker")

# 封装 IntelligentFaceTracker 的 RESTful API
# 注意: 这里的 API URL 是硬编码的，后续可以考虑通过环境变量传入
FACE_TRACKER_API_URL = "http://192.168.1.9:5000"

async def _make_request(method: str, endpoint: str, **kwargs) -> dict:
    """Helper function to make HTTP requests to the FaceTracker API."""
    url = f"{FACE_TRACKER_API_URL}{endpoint}"
    async with httpx.AsyncClient() as client:
        try:
            response = await client.request(method, url, **kwargs)
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            return {"success": False, "message": f"Request Error: {e}"}
        except htt.HTTPStatusError as e:
            return {"success": False, "message": f"HTTP Error: {e.response.status_code} - {e.response.text}"}
        except Exception as e:
            return {"success": False, "message": f"An unexpected error occurred: {e}"}

@mcp.tool()
async def move_servo(pin: int, angle: float) -> dict:
    """Moves a servo to an absolute angle."""
    return await _make_request("POST", "/move_servo", json={"pin": pin, "angle": angle})

@mcp.tool()
async def get_servo_angles() -> dict:
    """Gets the current angles of the servos."""
    return await _make_request("GET", "/get_servo_angles")

@mcp.tool()
async def move_servo_relative(axis: str, delta: float) -> dict:
    """Moves a servo by a relative amount."""
    return await _make_request("POST", "/move_servo_relative", json={"axis": axis, "delta": delta})

@mcp.tool()
async def get_face_count() -> dict:
    """Gets the number of detected faces."""
    return await _make_request("GET", "/get_face_count")

@mcp.tool()
async def reset_servo_position() -> dict:
    """Resets the servos to their initial position."""
    return await _make_request("POST", "/reset_position")

@mcp.tool()
async def toggle_face_tracking(enabled: bool) -> dict:
    """Toggles automatic face tracking."""
    return await _make_request("POST", "/toggle_tracking", json={"enabled": enabled})

if __name__ == "__main__":
    mcp.run(transport="sse", port=8001)