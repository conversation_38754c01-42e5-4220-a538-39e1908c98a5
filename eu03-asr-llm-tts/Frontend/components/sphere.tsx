"use client"

import { useRef, useEffect, useState, useCallback } from "react"
import { useAppState } from "@/components/app-state-provider"
import * as THREE from "three"

// 定义动画参数接口
interface AnimationParams {
  amplitude: number;
  frequency: number;
  color1: THREE.Color;
  color2: THREE.Color;
  timeIncrement: number;
  rotationX: number;
  rotationY: number;
}

// 定义各状态的动画参数
const ANIMATION_PARAMS: Record<string, AnimationParams> = {
  idle: {
    amplitude: 0.02,
    frequency: 0.3,
    color1: new THREE.Color("#1A1A1A"),
    color2: new THREE.Color("#333333"),
    timeIncrement: 0.05, // Increase speed from 0.02 to 0.05
    rotationX: 0.001,
    rotationY: 0.002
  },
  listening: {
    amplitude: 0.15,
    frequency: 2.0,
    color1: new THREE.Color("#00E0A0"),
    color2: new THREE.Color("#00C0E0"),
    timeIncrement: 0.05,
    rotationX: 0.003,
    rotationY: 0.005
  },
  speaking: {
    amplitude: 0.18,
    frequency: 4.0,
    color1: new THREE.Color("#1E00FF"),
    color2: new THREE.Color("#9D00FF"),
    timeIncrement: 0.08,
    rotationX: 0.005,
    rotationY: 0.008
  }
};

// 新增皮肤配色方案
const SKIN_THEMES: Record<string, Record<string, AnimationParams>> = {
  default: ANIMATION_PARAMS,
  // 新增皮肤配色方案，参考用户提供的图片
  alternative: {
    idle: {
      amplitude: 0.02,
      frequency: 0.3,
      color1: new THREE.Color("#1A1A1A"), // 与默认皮肤保持一致
      color2: new THREE.Color("#333333"), // 与默认皮肤保持一致
      timeIncrement: 0.05,
      rotationX: 0.001,
      rotationY: 0.002
    },
    listening: {
      amplitude: 0.15,
      frequency: 2.0,
      color1: new THREE.Color("#00C896"),
      color2: new THREE.Color("#00A676"),
      timeIncrement: 0.05,
      rotationX: 0.003,
      rotationY: 0.005
    },
    speaking: {
      amplitude: 0.18,
      frequency: 4.0,
      color1: new THREE.Color("#9B5DE5"),
      color2: new THREE.Color("#F15BB5"),
      timeIncrement: 0.08,
      rotationX: 0.005,
      rotationY: 0.008
    }
  },
  happy: {
    idle: {
      amplitude: 0.02,
      frequency: 0.3,
      color1: new THREE.Color("#1A1A1A"),
      color2: new THREE.Color("#333333"),
      timeIncrement: 0.05,
      rotationX: 0.001,
      rotationY: 0.002
    },
    listening: {
      amplitude: 0.15,
      frequency: 2.0,
      color1: new THREE.Color("#00A896"),
      color2: new THREE.Color("#02C39A"),
      timeIncrement: 0.05,
      rotationX: 0.003,
      rotationY: 0.005
    },
    speaking: {
      amplitude: 0.18,
      frequency: 4.0,
      color1: new THREE.Color("#028090"),
      color2: new THREE.Color("#05668D"),
      timeIncrement: 0.08,
      rotationX: 0.005,
      rotationY: 0.008
    }
  }
};

// 缓动函数 - 平滑过渡
function easeInOutCubic(t: number): number {
  return t < 0.5 ? 4 * t * t * t : 1 - Math.pow(-2 * t + 2, 3) / 2;
}

export function Sphere({ skinTheme: initialSkinTheme }: { skinTheme: "default" | "alternative" | "happy" }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const { state } = useAppState()
  const [animation, setAnimation] = useState<"idle" | "listening" | "speaking">("idle")
  const [skinTheme, setSkinTheme] = useState<"default" | "alternative" | "happy">(initialSkinTheme)

  useEffect(() => {
    setSkinTheme(initialSkinTheme);
  }, [initialSkinTheme]);

  // 添加过渡状态管理
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionProgress, setTransitionProgress] = useState(0)
  const [previousAnimation, setPreviousAnimation] = useState<"idle" | "listening" | "speaking">("idle")
  const [targetAnimation, setTargetAnimation] = useState<"idle" | "listening" | "speaking">("idle")

  // 添加动画参数引用，用于在动画循环中访问
  const animationRef = useRef(animation)
  const transitionProgressRef = useRef(transitionProgress)
  const isTransitioningRef = useRef(isTransitioning)
  const previousAnimationRef = useRef(previousAnimation)
  const targetAnimationRef = useRef(targetAnimation)

  // 更新引用值
  useEffect(() => {
    animationRef.current = animation;
  }, [animation]);

  useEffect(() => {
    transitionProgressRef.current = transitionProgress;
  }, [transitionProgress]);

  useEffect(() => {
    isTransitioningRef.current = isTransitioning;
  }, [isTransitioning]);

  useEffect(() => {
    previousAnimationRef.current = previousAnimation;
  }, [previousAnimation]);

  useEffect(() => {
    targetAnimationRef.current = targetAnimation;
  }, [targetAnimation]);



  // 开始过渡动画的函数
  const startTransition = useCallback((from: "idle" | "listening" | "speaking", to: "idle" | "listening" | "speaking") => {
    if (from === to) return;

    setPreviousAnimation(from);
    setTargetAnimation(to);
    setTransitionProgress(0);
    setIsTransitioning(true);

    // 创建过渡动画
    const transitionDuration = 400; // 过渡时间（毫秒）
    const startTime = performance.now();

    const animateTransition = () => {
      const currentTime = performance.now();
      const elapsed = currentTime - startTime;
      const progress = Math.min(elapsed / transitionDuration, 1);

      setTransitionProgress(progress);

      if (progress < 1) {
        requestAnimationFrame(animateTransition);
      } else {
        // 过渡完成
        setIsTransitioning(false);
        setAnimation(to);
      }
    };

    requestAnimationFrame(animateTransition);
  }, []);

  useEffect(() => {
    // 从新的状态管理器获取状态
    const { ui, system, conversation } = state

    // 确定目标动画状态
    let targetState: "idle" | "listening" | "speaking" = "idle"

    // 检查是否正在监听
    if (ui.isListening) {
      targetState = "listening"
    }
    // 检查是否正在说话（TTS播放或助手正在输入）
    else if (conversation.isAssistantTyping || system.statuses.tts_player?.status === "speaking") {
      targetState = "speaking"
    }
    // 默认为空闲状态
    else {
      targetState = "idle"
    }



    // 如果状态发生变化，开始过渡动画
    if (targetState !== animation) {
      startTransition(animation, targetState)
    }
  }, [state.ui.isListening, state.conversation.isAssistantTyping, state.system.statuses.tts_player, animation, startTransition])

  useEffect(() => {
    if (!containerRef.current) return

    // Scene setup
    const scene = new THREE.Scene()
    const camera = new THREE.PerspectiveCamera(75, 1, 0.1, 1000)
    camera.position.z = 5

    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true })
    renderer.setSize(300, 300)
    containerRef.current.appendChild(renderer.domElement)

    // Create sphere
    const geometry = new THREE.SphereGeometry(2, 64, 64)

    // Create shader material for dynamic effects
    const material = new THREE.ShaderMaterial({
      uniforms: {
        time: { value: 0 },
        color1: { value: new THREE.Color("#4A00E0") },
        color2: { value: new THREE.Color("#8E2DE2") },
        amplitude: { value: 0.1 },
        frequency: { value: 1.0 },
      },
      vertexShader: `
        uniform float time;
        uniform float amplitude;
        uniform float frequency;

        varying vec2 vUv;
        varying vec3 vNormal;

        void main() {
          vUv = uv;
          vNormal = normal;

          // 计算更复杂的位移效果，使形变更加有趣
          float noise = sin(position.x * frequency + time) *
                        sin(position.y * frequency + time) *
                        sin(position.z * frequency + time);

          // 添加第二层噪声，使形变更加自然
          float noise2 = sin(position.x * frequency * 1.5 + time * 0.7) *
                         sin(position.y * frequency * 1.5 + time * 0.7) *
                         cos(position.z * frequency * 1.5 + time * 0.7) * 0.5;

          // 组合两层噪声
          float displacement = (noise + noise2) * amplitude;

          // 应用位移
          vec3 newPosition = position + normal * displacement;

          gl_Position = projectionMatrix * modelViewMatrix * vec4(newPosition, 1.0);
        }
      `,
      fragmentShader: `
        uniform vec3 color1;
        uniform vec3 color2;
        uniform float time;

        varying vec2 vUv;
        varying vec3 vNormal;

        void main() {
          // 创建更动态的渐变效果，使用时间变量增加动态性
          float mixFactor = vUv.y + sin(time * 0.5) * 0.2 + cos(vUv.x * 3.0 + time * 0.3) * 0.1;
          vec3 color = mix(color1, color2, mixFactor);

          // 增强光照效果，使球体更有立体感
          vec3 lightDir = normalize(vec3(sin(time * 0.2) * 0.5 + 0.5,
                                        cos(time * 0.3) * 0.5 + 0.5,
                                        1.0));
          float diffuse = max(0.0, dot(vNormal, lightDir));

          // 添加高光效果，使球体在响应状态下更加明亮
          vec3 viewDir = vec3(0.0, 0.0, 1.0);
          vec3 halfDir = normalize(lightDir + viewDir);
          float specular = pow(max(0.0, dot(vNormal, halfDir)), 32.0) * 0.5;

          // 组合基础颜色、漫反射和高光
          color = color + color * diffuse * 0.3 + vec3(specular);

          gl_FragColor = vec4(color, 1.0);
        }
      `,
    })

    const sphere = new THREE.Mesh(geometry, material)
    scene.add(sphere)

    // Animation loop
    let frameId: number
    const animate = () => {
      frameId = requestAnimationFrame(animate)

      // 获取当前动画状态和过渡进度
      const currentAnimation = animationRef.current;
      const currentSkinTheme = skinTheme;
      const isInTransition = isTransitioningRef.current;
      const progress = transitionProgressRef.current;
      const fromState = previousAnimationRef.current;
      const toState = targetAnimationRef.current;

      // 应用缓动函数使过渡更自然
      const easedProgress = isInTransition ? easeInOutCubic(progress) : 1;

      // 获取当前皮肤主题的动画参数
      const currentThemeParams = SKIN_THEMES[currentSkinTheme];

      // 更新着色器参数
      if (isInTransition) {
        // 在过渡期间，在两种状态之间进行插值
        const fromParams = currentThemeParams[fromState];
        const toParams = currentThemeParams[toState];

        // 线性插值辅助函数
        const lerp = (a: number, b: number, t: number) => a + (b - a) * t;
        const lerpColor = (a: THREE.Color, b: THREE.Color, t: number) => {
          return new THREE.Color(
            lerp(a.r, b.r, t),
            lerp(a.g, b.g, t),
            lerp(a.b, b.b, t)
          );
        };

        // 应用插值后的参数
        (material.uniforms.amplitude.value as number) = lerp(fromParams.amplitude, toParams.amplitude, easedProgress);
        (material.uniforms.frequency.value as number) = lerp(fromParams.frequency, toParams.frequency, easedProgress);

        // 颜色插值
        const color1 = lerpColor(fromParams.color1, toParams.color1, easedProgress);
        const color2 = lerpColor(fromParams.color2, toParams.color2, easedProgress);
        material.uniforms.color1.value.copy(color1);
        material.uniforms.color2.value.copy(color2);

        // 时间增量和旋转速度插值
        const timeIncrement = lerp(fromParams.timeIncrement, toParams.timeIncrement, easedProgress);
        const rotX = lerp(fromParams.rotationX, toParams.rotationX, easedProgress);
        const rotY = lerp(fromParams.rotationY, toParams.rotationY, easedProgress);

        // 应用时间和旋转更新
        (material.uniforms.time.value as number) += timeIncrement;
        sphere.rotation.x += rotX;
        sphere.rotation.y += rotY;
      } else {
        // 非过渡状态，直接使用当前状态的参数
        const params = currentThemeParams[currentAnimation];

        // 应用参数
        (material.uniforms.amplitude.value as number) = params.amplitude;
        (material.uniforms.frequency.value as number) = params.frequency;
        material.uniforms.color1.value.copy(params.color1);
        material.uniforms.color2.value.copy(params.color2);

        // 应用时间和旋转更新
        (material.uniforms.time.value as number) += params.timeIncrement;
        sphere.rotation.x += params.rotationX;
        sphere.rotation.y += params.rotationY;
      }

      renderer.render(scene, camera)
    }

    animate()

    // Handle window resize
    const handleResize = () => {
      if (!containerRef.current) return
      const size = Math.min(containerRef.current.clientWidth, 300)
      renderer.setSize(size, size)
    }

    window.addEventListener("resize", handleResize)
    handleResize()

    return () => {
      cancelAnimationFrame(frameId)
      window.removeEventListener("resize", handleResize)
      if (containerRef.current && renderer.domElement) {
        containerRef.current.removeChild(renderer.domElement)
      }
    }
  }, [animation, skinTheme])

  // 根据当前状态和过渡进度确定要显示的文本和样式
  const getStatusText = () => {
    if (!isTransitioning) {
      // 非过渡状态，直接显示当前状态文本
      if (animation === "idle") {
        return <span className="text-gray-500 transition-opacity duration-300">待命中</span>;
      } else if (animation === "listening") {
        return <span className="text-emerald-500 animate-pulse transition-opacity duration-300">正在聆听...</span>;
      } else if (animation === "speaking") {
        return <span className="text-indigo-500 animate-pulse transition-opacity duration-300">正在回应...</span>;
      }
    } else {
      // 过渡状态，根据过渡方向和进度决定显示内容
      const fromText = previousAnimation === "idle"
        ? <span className="text-gray-500 transition-opacity duration-300" style={{ opacity: 1 - transitionProgress }}>待命中</span>
        : previousAnimation === "listening"
          ? <span className="text-emerald-500 transition-opacity duration-300" style={{ opacity: 1 - transitionProgress }}>正在聆听...</span>
          : <span className="text-indigo-500 transition-opacity duration-300" style={{ opacity: 1 - transitionProgress }}>正在回应...</span>;

      const toText = targetAnimation === "idle"
        ? <span className="text-gray-500 transition-opacity duration-300" style={{ opacity: transitionProgress }}>待命中</span>
        : targetAnimation === "listening"
          ? <span className="text-emerald-500 transition-opacity duration-300" style={{ opacity: transitionProgress }}>正在聆听...</span>
          : <span className="text-indigo-500 transition-opacity duration-300" style={{ opacity: transitionProgress }}>正在回应...</span>;

      return (
        <div className="relative">
          <div className="absolute inset-0">{fromText}</div>
          <div className="relative">{toText}</div>
        </div>
      );
    }
  };

  return (
    <div ref={containerRef} className="w-[300px] h-[300px] flex items-center justify-center relative">
      <div className="absolute bottom-0 text-sm font-medium">
        {getStatusText()}
      </div>
    </div>
  )
}
