"use client"

/**
 * 语言切换组件
 */

import { useState } from 'react'
import { Globe, Check, ChevronDown } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useI18n, useLocaleSwitch } from '@/lib/i18n/context'
import type { SupportedLocale } from '@/lib/i18n/config'

interface LanguageSwitcherProps {
  variant?: 'default' | 'compact' | 'icon-only'
  className?: string
}

export function LanguageSwitcher({ 
  variant = 'default', 
  className = '' 
}: LanguageSwitcherProps) {
  const { t } = useI18n()
  const { currentLocale, supportedLocales, switchTo } = useLocaleSwitch()
  const [isOpen, setIsOpen] = useState(false)

  const currentLocaleConfig = supportedLocales[currentLocale]

  const handleLocaleChange = (locale: SupportedLocale) => {
    switchTo(locale)
    setIsOpen(false)
  }

  // 紧凑版本 - 只显示图标和当前语言
  if (variant === 'compact') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 px-2 ${className}`}
          >
            <Globe className="h-4 w-4 mr-1" />
            <span className="text-sm">{currentLocaleConfig.nativeName}</span>
            <ChevronDown className={`h-3 w-3 ml-1 transform transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          {Object.values(supportedLocales).map((locale) => (
            <DropdownMenuItem
              key={locale.code}
              onClick={() => handleLocaleChange(locale.code)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center">
                <span className="mr-2">{locale.flag}</span>
                <span>{locale.nativeName}</span>
              </div>
              {currentLocale === locale.code && (
                <Check className="h-4 w-4" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // 仅图标版本
  if (variant === 'icon-only') {
    return (
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className={`h-8 w-8 p-0 ${className}`}
            title={t.language.switch}
          >
            <Globe className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          {Object.values(supportedLocales).map((locale) => (
            <DropdownMenuItem
              key={locale.code}
              onClick={() => handleLocaleChange(locale.code)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center">
                <span className="mr-2">{locale.flag}</span>
                <span>{locale.nativeName}</span>
              </div>
              {currentLocale === locale.code && (
                <Check className="h-4 w-4" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // 默认版本 - 完整显示
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={`flex items-center space-x-2 ${className}`}
        >
          <Globe className="h-4 w-4" />
          <span>{currentLocaleConfig.flag}</span>
          <span>{currentLocaleConfig.nativeName}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        <div className="px-2 py-1.5 text-sm font-medium text-gray-500 dark:text-gray-400">
          {t.language.switch}
        </div>
        {Object.values(supportedLocales).map((locale) => (
          <DropdownMenuItem
            key={locale.code}
            onClick={() => handleLocaleChange(locale.code)}
            className="flex items-center justify-between"
          >
            <div className="flex items-center">
              <span className="mr-3">{locale.flag}</span>
              <div>
                <div className="font-medium">{locale.nativeName}</div>
                <div className="text-xs text-gray-500">{locale.name}</div>
              </div>
            </div>
            {currentLocale === locale.code && (
              <Check className="h-4 w-4 text-green-600" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

/**
 * 简单的语言切换按钮（用于快速切换）
 */
export function QuickLanguageSwitch({ className = '' }: { className?: string }) {
  const { currentLocale, switchToNext } = useLocaleSwitch()
  const supportedLocales = Object.values(useLocaleSwitch().supportedLocales)
  
  const currentConfig = supportedLocales.find(l => l.code === currentLocale)
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={switchToNext}
      className={`h-8 px-2 ${className}`}
      title="Switch Language"
    >
      <span className="mr-1">{currentConfig?.flag}</span>
      <span className="text-sm">{currentConfig?.nativeName}</span>
    </Button>
  )
}
