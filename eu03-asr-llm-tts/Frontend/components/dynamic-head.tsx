"use client"

/**
 * 动态Head组件
 * 用于根据当前语言动态更新页面标题和元数据
 */

import { useEffect } from 'react'
import { useI18n } from '@/lib/i18n/context'

export function DynamicHead() {
  const { t, locale } = useI18n()

  useEffect(() => {
    // 更新页面标题
    document.title = t.meta.title
    
    // 更新meta description
    const metaDescription = document.querySelector('meta[name="description"]')
    if (metaDescription) {
      metaDescription.setAttribute('content', t.meta.description)
    } else {
      const meta = document.createElement('meta')
      meta.name = 'description'
      meta.content = t.meta.description
      document.head.appendChild(meta)
    }
    
    // 更新HTML lang属性
    document.documentElement.lang = locale
  }, [t.meta.title, t.meta.description, locale])

  return null
}
