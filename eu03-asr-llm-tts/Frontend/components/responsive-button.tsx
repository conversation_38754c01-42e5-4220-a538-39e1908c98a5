"use client"

import React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface ResponsiveButtonProps extends React.ComponentProps<typeof Button> {
  icon: React.ReactNode
  text: string
  showTextBreakpoint?: string // 例如: 'md', 'lg'
}

/**
 * 响应式按钮组件，在小屏幕上只显示图标，在大屏幕上显示图标和文本
 */
export function ResponsiveButton({
  icon,
  text,
  showTextBreakpoint = "md",
  className,
  ...props
}: ResponsiveButtonProps) {
  // 根据断点生成类名
  const textClassName = {
    'sm': 'hidden sm:inline-block',
    'md': 'hidden md:inline-block',
    'lg': 'hidden lg:inline-block',
    'xl': 'hidden xl:inline-block',
  }[showTextBreakpoint] || 'hidden md:inline-block';

  return (
    <Button className={cn("whitespace-nowrap", className)} {...props}>
      {icon}
      <span className={textClassName}>{text}</span>
    </Button>
  )
}
