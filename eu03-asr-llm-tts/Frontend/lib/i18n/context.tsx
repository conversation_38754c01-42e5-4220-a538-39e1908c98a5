"use client"

/**
 * 国际化Context和Provider
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { 
  SupportedLocale, 
  getInitialLocale, 
  saveLocale,
  SUPPORTED_LOCALES 
} from './config'
import { getTranslation, type TranslationKeys } from './locales'

interface I18nContextType {
  locale: SupportedLocale
  setLocale: (locale: SupportedLocale) => void
  t: TranslationKeys
  isLoading: boolean
}

const I18nContext = createContext<I18nContextType | undefined>(undefined)

interface I18nProviderProps {
  children: ReactNode
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [locale, setLocaleState] = useState<SupportedLocale>('zh')
  const [isLoading, setIsLoading] = useState(true)

  // 初始化语言设置
  useEffect(() => {
    const initialLocale = getInitialLocale()
    setLocaleState(initialLocale)
    setIsLoading(false)
  }, [])

  // 切换语言
  const setLocale = (newLocale: SupportedLocale) => {
    setLocaleState(newLocale)
    saveLocale(newLocale)
    
    // 更新HTML lang属性
    if (typeof document !== 'undefined') {
      document.documentElement.lang = newLocale
    }
  }

  // 获取当前语言的翻译
  const t = getTranslation(locale)

  const value: I18nContextType = {
    locale,
    setLocale,
    t,
    isLoading
  }

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  )
}

/**
 * 使用国际化的Hook
 */
export function useI18n() {
  const context = useContext(I18nContext)
  if (context === undefined) {
    throw new Error('useI18n must be used within an I18nProvider')
  }
  return context
}

/**
 * 获取支持的语言列表
 */
export function useSupportedLocales() {
  return SUPPORTED_LOCALES
}

/**
 * 语言切换Hook
 */
export function useLocaleSwitch() {
  const { locale, setLocale } = useI18n()
  const supportedLocales = useSupportedLocales()

  const switchToNext = () => {
    const locales = Object.keys(supportedLocales) as SupportedLocale[]
    const currentIndex = locales.indexOf(locale)
    const nextIndex = (currentIndex + 1) % locales.length
    setLocale(locales[nextIndex])
  }

  const switchTo = (targetLocale: SupportedLocale) => {
    setLocale(targetLocale)
  }

  return {
    currentLocale: locale,
    supportedLocales,
    switchToNext,
    switchTo
  }
}
