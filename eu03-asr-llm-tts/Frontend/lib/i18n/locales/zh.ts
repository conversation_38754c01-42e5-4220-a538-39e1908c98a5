/**
 * 中文翻译文件
 */

export const zh = {
  // 页面标题和元数据
  meta: {
    title: '智能语音助手',
    description: '现代化的智能语音对话助手'
  },

  // 主页面
  page: {
    title: '智能语音助手'
  },

  // 聊天界面
  chat: {
    title: '对话',
    stopGenerating: '停止生成',
    emptyMessage: '开始对话，请输入消息或使用语音输入',
    inputPlaceholder: '输入消息... (Ctrl+Enter发送)',
    keyboardModeHint: '键盘输入模式：Enter换行，Ctrl+Enter发送',
    voiceModeHint: {
      listening: '正在监听，完成后系统将自动识别并发送...',
      idle: '点击麦克风按钮开始语音输入'
    },
    connectionStatus: {
      disconnected: '未连接到服务器...',
      connecting: '正在连接...',
      connected: '已连接'
    },
    inputMode: {
      voice: '语音对话模式',
      keyboard: '键盘对话模式',
      voiceButton: '语音模式',
      keyboardButton: '键盘模式',
      switchToVoice: '切换到语音对话模式',
      switchToKeyboard: '切换到键盘对话模式'
    },
    toast: {
      switchedToVoice: '已切换到语音对话模式',
      switchedToKeyboard: '已切换到键盘对话模式',
      voiceModeDescription: '语音识别结果将自动发送。点击麦克风按钮开始语音输入。',
      keyboardModeDescription: '请手动点击发送按钮发送消息。'
    },
    sendButton: {
      disconnected: '未连接到服务器',
      voiceListening: '语音模式下正在监听，将自动发送',
      send: '发送消息'
    },
    confirm: {
      switchModeWithInput: '您有未发送的输入内容，是否在切换模式前发送？'
    }
  },

  // 工具调用
  toolCall: {
    title: '工具',
    status: {
      running: '调用中',
      success: '成功',
      error: '失败',
      waiting: '等待中'
    },
    labels: {
      parameters: '调用参数',
      result: '执行结果',
      scrollHint: '可滚动查看更多'
    },
    messages: {
      executing: '正在执行工具调用，请稍候...'
    },
    summary: {
      toolCalls: '个工具调用',
      executing: '个执行中',
      completed: '个完成'
    }
  },

  // Sphere动画状态
  sphere: {
    status: {
      idle: '待命中',
      listening: '正在聆听...',
      speaking: '正在回应...'
    }
  },

  // 错误消息
  errors: {
    websocket: {
      parseMessage: '解析WebSocket消息失败',
      connectionError: 'WebSocket连接错误',
      createConnection: '创建WebSocket连接失败',
      maxReconnect: '已达到最大重连次数',
      notConnected: 'WebSocket未连接',
      sendMessage: '发送消息失败'
    },
    toolCall: {
      parseArguments: '解析参数失败'
    },
    context: {
      appStateProvider: 'useAppState必须在AppStateProvider内部使用',
      webSocketProvider: 'useWebSocket必须在WebSocketProvider内部使用'
    },
    deprecated: {
      setMessageHandler: 'setMessageHandler已弃用，消息处理现在是自动的'
    }
  },

  // 语言切换
  language: {
    switch: '切换语言',
    current: '当前语言',
    chinese: '中文',
    english: 'English'
  },

  // 通用
  common: {
    send: '发送',
    cancel: '取消',
    confirm: '确认',
    close: '关闭',
    loading: '加载中...',
    error: '错误',
    success: '成功',
    warning: '警告',
    info: '信息'
  }
} as const

export type TranslationKeys = {
  meta: {
    title: string
    description: string
  }
  page: {
    title: string
  }
  chat: {
    title: string
    stopGenerating: string
    emptyMessage: string
    inputPlaceholder: string
    keyboardModeHint: string
    voiceModeHint: {
      listening: string
      idle: string
    }
    connectionStatus: {
      disconnected: string
      connecting: string
      connected: string
    }
    inputMode: {
      voice: string
      keyboard: string
      voiceButton: string
      keyboardButton: string
      switchToVoice: string
      switchToKeyboard: string
    }
    toast: {
      switchedToVoice: string
      switchedToKeyboard: string
      voiceModeDescription: string
      keyboardModeDescription: string
    }
    sendButton: {
      disconnected: string
      voiceListening: string
      send: string
    }
    confirm: {
      switchModeWithInput: string
    }
  }
  toolCall: {
    title: string
    status: {
      running: string
      success: string
      error: string
      waiting: string
    }
    labels: {
      parameters: string
      result: string
      scrollHint: string
    }
    messages: {
      executing: string
    }
    summary: {
      toolCalls: string
      executing: string
      completed: string
    }
  }
  sphere: {
    status: {
      idle: string
      listening: string
      speaking: string
    }
  }
  errors: {
    websocket: {
      parseMessage: string
      connectionError: string
      createConnection: string
      maxReconnect: string
      notConnected: string
      sendMessage: string
    }
    toolCall: {
      parseArguments: string
    }
    context: {
      appStateProvider: string
      webSocketProvider: string
    }
    deprecated: {
      setMessageHandler: string
    }
  }
  language: {
    switch: string
    current: string
    chinese: string
    english: string
  }
  common: {
    send: string
    cancel: string
    confirm: string
    close: string
    loading: string
    error: string
    success: string
    warning: string
    info: string
  }
}
