/**
 * 国际化配置文件
 * 定义支持的语言、默认语言等配置
 */

export type SupportedLocale = 'zh' | 'en'

export interface LocaleConfig {
  code: SupportedLocale
  name: string
  nativeName: string
  flag: string
}

export const SUPPORTED_LOCALES: Record<SupportedLocale, LocaleConfig> = {
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳'
  },
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸'
  }
}

export const DEFAULT_LOCALE: SupportedLocale = 'zh'

export const LOCALE_STORAGE_KEY = 'preferred-locale'

/**
 * 获取浏览器首选语言
 */
export function getBrowserLocale(): SupportedLocale {
  if (typeof window === 'undefined') return DEFAULT_LOCALE
  
  const browserLang = navigator.language.toLowerCase()
  
  // 检查是否匹配支持的语言
  if (browserLang.startsWith('zh')) return 'zh'
  if (browserLang.startsWith('en')) return 'en'
  
  return DEFAULT_LOCALE
}

/**
 * 从本地存储获取保存的语言偏好
 */
export function getSavedLocale(): SupportedLocale | null {
  if (typeof window === 'undefined') return null
  
  try {
    const saved = localStorage.getItem(LOCALE_STORAGE_KEY)
    if (saved && Object.keys(SUPPORTED_LOCALES).includes(saved)) {
      return saved as SupportedLocale
    }
  } catch (error) {
    console.warn('Failed to read locale from localStorage:', error)
  }
  
  return null
}

/**
 * 保存语言偏好到本地存储
 */
export function saveLocale(locale: SupportedLocale): void {
  if (typeof window === 'undefined') return
  
  try {
    localStorage.setItem(LOCALE_STORAGE_KEY, locale)
  } catch (error) {
    console.warn('Failed to save locale to localStorage:', error)
  }
}

/**
 * 获取初始语言设置
 * 优先级：本地存储 > 浏览器语言 > 默认语言
 */
export function getInitialLocale(): SupportedLocale {
  return getSavedLocale() || getBrowserLocale()
}
