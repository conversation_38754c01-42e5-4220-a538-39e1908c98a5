# LLM模块技术文档

## 概述

LLM（大语言模型）模块基于火山引擎Doubao系列API实现对话生成和工具调用功能，支持流式响应和Function Calling，为语音助手提供智能对话能力。

## 技术架构

### 核心技术栈
- **LLM服务**: 火山引擎 Doubao 系列
- **通信协议**: HTTP + Server-Sent Events (SSE)
- **HTTP客户端**: httpx (异步)
- **工具调用**: Function Calling框架
- **错误处理**: 智能错误映射和重试

### 架构流程
```
┌─────────────────────────────────────────────────────────────┐
│                    turn_manager.py                          │
│  ┌─────────────────┐                ┌─────────────────────┐  │
│  │  对话协调器      │                │   工具调用管理器     │  │
│  │                 │◄──────────────►│                     │  │
│  └─────────────────┘                └─────────────────────┘  │
│           │                                   ▲              │
│           ▼                                   │              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              LLM Client (llm/client.py)                 │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                火山引擎 LLM API 服务                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   流式响应       │  │   Function      │  │   错误处理    │ │
│  │   (SSE)         │  │   Calling       │  │   & 重试     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
│           │                     │                   │       │
│           ▼                     ▼                   ▼       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              响应数据流处理                              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件

### 1. LLMClient 主客户端类
负责与火山引擎API通信，处理流式和非流式对话。

**主要功能**:
- 初始化API连接和配置
- 流式对话处理 (`stream_chat`)
- 非流式对话处理 (`chat`)
- 错误处理和重试机制

**配置参数**:
```python
{
    "api_url": "https://ark.cn-beijing.volces.com",
    "api_key": "${VOLCENGINE_LLM_API_KEY}",
    "model": "doubao-pro-4k",
    "parameters": {
        "temperature": 0.7,
        "max_tokens": 4000
    }
}
```

### 2. 流式响应处理
支持实时流式对话，边接收边处理LLM输出内容。

**核心特性**:
- 低延迟流式响应
- 工具调用检测
- Token使用统计
- 错误状态处理

### 3. 错误处理系统
提供智能的错误码映射和用户友好的错误提示。

**常见错误码**:
- `AuthenticationError`: API密钥无效
- `AccessDenied`: 权限不足
- `RateLimitExceeded`: 请求频率超限
- `QuotaExceeded`: 额度耗尽
- `ModelLoadingError`: 模型加载中

**错误处理**:
- 自动错误码映射
- 用户友好的错误提示
- 智能重试机制
- 降级处理策略

### 4. Function Calling 系统
支持本地工具和MCP工具的统一调用框架。

**核心特性**:
- 统一工具注册机制
- 异步工具调用支持
- 多轮工具调用（最大5轮）
- V1.5.0静默调用模式（工具调用期间不启动TTS）

**支持的工具类型**:
- 计算器工具
- 音乐播放器
- 远程摄像头控制
- MCP协议工具
## 配置参数

```yaml
llm:
  volcengine:
    api_url: "https://ark.cn-beijing.volces.com"
    api_key: "${VOLCENGINE_LLM_API_KEY}"
  model:
    name: "doubao-pro-4k"
  parameters:
    temperature: 0.7
    max_tokens: 4000
    top_p: 0.9
  timeout: 30.0
  tools_enabled: true
  max_tool_iterations: 5
```

### 环境变量
```bash
export VOLCENGINE_LLM_API_KEY="your_api_key_here"
export LLM_MODEL_NAME="doubao-pro-4k"
export LLM_TEMPERATURE="0.7"
```

## 使用方法

### 基本使用
- 支持流式和非流式对话
- 异步API调用
- 自动错误处理和重试

### Function Calling
- 工具定义和注册
- 多轮工具调用
- 结果整合和返回

## 性能指标

- **流式延迟**: < 500ms
- **并发支持**: 多轮对话
- **错误恢复**: 自动重试
- **工具生态**: 丰富的Function Calling

## 故障排除

### 常见问题

| 问题 | 解决方案 |
|------|----------|
| API密钥认证失败 | 1. 检查环境变量设置<br>2. 确认密钥有效性<br>3. 检查权限范围 |
| 请求频率限制 | 1. 降低请求频率<br>2. 实现限流机制<br>3. 联系技术支持 |
| 模型响应超时 | 1. 增加timeout配置<br>2. 检查网络连接<br>3. 减少输入长度 |
| 工具调用失败 | 1. 检查工具注册<br>2. 验证参数格式<br>3. 查看错误日志 |

### 调试命令
```bash
# 测试LLM连接
python -c "from llm.client import LLMClient; print('LLM可用')"
```

## 部署和扩展

### 环境要求
- Python 3.8+
- 稳定网络连接
- 火山引擎API密钥

### 最佳实践
- **性能**: 连接池和批处理优化
- **错误处理**: 重试和降级机制
- **安全**: 输入验证和密钥管理
- **监控**: 性能监控和日志记录






