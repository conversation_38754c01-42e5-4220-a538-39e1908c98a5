# Function Calling & MCP 集成指南

## 概述

eu03-asr-llm-tts系统实现了完整的Function Calling (FC) 和 Model Context Protocol (MCP) 集成方案，提供统一的工具调用接口，支持本地工具和远程MCP工具的混合使用。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    LLM 对话引擎                              │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   工具检测      │    │   工具调用      │                │
│  │  Tool Detection │    │  Tool Calling   │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────────────────────┬───────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│              MCP Function Adapter                           │
│                   统一工具调用接口                           │
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   本地工具路由   │    │   MCP工具路由   │                │
│  │  Local Tools    │    │   MCP Tools     │                │
│  └─────────────────┘    └─────────────────┘                │
└─────────────┬───────────────────┬───────────────────────────┘
              │                   │
              ▼                   ▼
┌─────────────────────┐    ┌─────────────────────┐
│  Function Registry  │    │   MCP Integration   │
│     本地工具注册     │    │     MCP集成器       │
│                     │    │                     │
│ ┌─────────────────┐ │    │ ┌─────────────────┐ │
│ │   计算器工具    │ │    │ │  MCP客户端管理  │ │
│ │   音乐播放器    │ │    │ │  多服务器连接   │ │
│ │   远程控制      │ │    │ │  工具发现       │ │
│ │   音频设备      │ │    │ │  协议适配       │ │
│ └─────────────────┘ │    │ └─────────────────┘ │
└─────────────────────┘    └─────────────────────┘
```

### 核心组件

#### 1. Function Registry (本地工具注册器)
- **位置**: `Backend/utils/function_call/function_call_tools.py`
- **功能**: 本地工具的注册、管理和调用
- **特性**: 
  - 装饰器模式注册
  - 异步/同步函数自动适配
  - 统一的调用接口

#### 2. MCP Integration (MCP集成器)
- **位置**: `Backend/utils/mcp/client/mcp_integration.py`
- **功能**: MCP协议的客户端实现和工具集成
- **特性**:
  - 多MCP服务器连接管理
  - 工具发现和注册
  - 协议适配和错误处理

#### 3. MCP Function Adapter (统一适配器)
- **位置**: `Backend/utils/mcp/client/mcp_function_adapter.py`
- **功能**: 统一本地工具和MCP工具的调用接口
- **特性**:
  - 透明的工具路由
  - 统一的错误处理
  - 向后兼容性

## Function Calling 实现

### 工具注册机制

```python
# 装饰器注册方式
@FunctionRegistry.register_function('calculator', '加减乘除计算器')
def calculator_func(op, a, b):
    try:
        result = Calculator.calculate(op, a, b)
        return f"计算结果：{result:.2f}"
    except Exception as e:
        return f"计算错误：{str(e)}"

# 异步工具注册
@FunctionRegistry.register_function('yolo_move_servo_relative', '控制远程摄像头云台相对转动')
async def yolo_move_servo_relative_func(axis: str, delta: float) -> str:
    return await yolo_move_servo_relative(axis, delta)
```

### 已注册的本地工具

| 工具名称 | 功能描述 | 类型 |
|---------|---------|------|
| `calculator` | 基本数学计算 | 同步 |
| `music_player_list` | 获取音乐列表 | 同步 |
| `music_player_play` | 播放音乐 | 异步 |
| `music_player_stop` | 停止播放 | 异步 |
| `music_player_pause` | 暂停播放 | 异步 |
| `music_player_resume` | 恢复播放 | 异步 |
| `music_player_volume` | 调节音量 | 异步 |
| `audio_device_list` | 列出音频设备 | 同步 |
| `audio_device_switch` | 切换音频设备 | 异步 |
| `yolo_move_servo_relative` | 控制摄像头云台 | 异步 |
| `yolo_reset_servo` | 重置云台位置 | 异步 |
| `yolo_toggle_tracking` | 切换人脸追踪 | 异步 |
| `yolo_get_face_count` | 获取人脸数量 | 异步 |

### 工具调用流程

```python
# 统一调用接口
async def call_tool(tool_name: str, **kwargs):
    entry = FunctionRegistry._registry.get(tool_name)
    if not entry:
        raise ValueError(f'未注册的函数: {tool_name}')
    
    func = entry['func']
    if entry['is_async']:
        return await func(**kwargs)
    else:
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, lambda: func(**kwargs))
```

## MCP 实现

### MCP服务器架构

#### 核心工具服务器
- **位置**: `Backend/utils/mcp/server/mcp_core_tools.py`
- **功能**: 提供基础的MCP工具服务
- **工具列表**:
  - `calculator`: 基本数学运算
  - `advanced_calculator`: 复杂表达式计算
  - `get_system_status`: 系统状态查询
  - `check_service_health`: 服务健康检查
  - `text_processor`: 文本处理
  - `datetime_tool`: 时间日期处理

#### 环境适配
- **WSL2环境**: 提供核心功能，模拟硬件接口
- **Linux板卡**: 提供完整功能，包括音频和GPIO控制
- **自动检测**: 根据运行环境自动启用相应功能

### MCP客户端管理

```python
# MCP客户端连接
class MCPClient:
    async def connect_to_server(self, config: dict, name: str):
        """支持多种传输方式：
        1. URL + SSE: Server-Sent Events连接
        2. URL + HTTP: HTTP流连接  
        3. Command + Args: stdio连接本地进程
        """
        
    async def list_tools_and_save(self):
        """列出并保存MCP工具"""
        
    async def call_tool(self, tool_name: str, arguments: dict):
        """调用MCP工具"""
```

### MCP工具发现和注册

```python
# 工具发现流程
async def _load_mcp_tools(self):
    mcp_tools = self.mcp_manager.get_available_tools()
    
    for mcp_tool in mcp_tools:
        tool = IntegratedTool(
            name=f"mcp:{mcp_tool.server_name}:{mcp_tool.name}",
            description=mcp_tool.description,
            parameters=mcp_tool.parameters,
            origin="mcp",
            server_name=mcp_tool.server_name
        )
        self.integrated_tools.append(tool)
```

## 统一调用接口

### MCP Function Adapter

```python
class MCPFunctionAdapter:
    async def call_tool_enhanced(self, tool_name: str, **kwargs):
        """增强的工具调用，支持本地工具和MCP工具"""
        
        # 检查是否为MCP工具（以mcp:开头）
        if tool_name.startswith("mcp:"):
            return await self.integrator.call_tool(tool_name, kwargs)
        else:
            # 调用本地工具
            return await FunctionRegistry.call(tool_name, **kwargs)
    
    async def get_enhanced_tools_schema(self):
        """获取合并的工具schema"""
        local_schemas = self._get_local_tools_schema()
        mcp_schemas = await self._get_mcp_tools_schema()
        return local_schemas + mcp_schemas
```

### 在turn_manager中的集成

```python
# turn_manager.py中的工具调用
async def handle_tool_calls(tool_calls):
    for call in tool_calls:
        function_name = call["function"]["name"]
        arguments = json.loads(call["function"]["arguments"])
        
        # 使用MCP增强的函数调用
        if MCP_ENHANCED_AVAILABLE:
            result = await compatible_function_call(function_name, **arguments)
        else:
            result = await FunctionRegistry.call(function_name, **arguments)
```

## V1.5.0 静默调用模式

### 智能TTS控制

系统实现了智能的TTS控制机制，在工具调用期间自动禁用TTS播放：

```python
# 工具调用检测
has_tool_calls_this_iteration = bool(chunk.get("tool_calls"))

# 只有在没有工具调用时才启动TTS
if not has_tool_calls_this_iteration and accumulated_content_this_iteration:
    playback_task = asyncio.create_task(
        stream_tts_and_play(tts, player, text_queue, stop_event, tts_config)
    )
```

### 多轮工具调用支持

- **最大轮次**: 5轮工具调用
- **状态跟踪**: 实时跟踪工具调用状态
- **智能切换**: 工具调用完成后自动恢复TTS

## 配置和部署

### MCP配置文件

```yaml
# Backend/utils/mcp/config/mcp_config.yaml
environment:
  auto_detect: true
  type: "auto"  # auto, development, production, testing
  
  development:  # WSL2开发环境
    enable_audio: false
    enable_hardware: false
    enable_remote_services: true
    
  production:   # Linux板卡生产环境
    enable_audio: true
    enable_hardware: true
    enable_remote_services: true

mcp_server:
  name: "ASR-LLM-TTS MCP Server"
  transport:
    default: "stdio"
    http:
      host: "0.0.0.0"
      port: 8001
```

### 环境适配

系统自动检测运行环境并适配相应功能：

- **WSL2**: 核心功能 + 模拟接口
- **Linux板卡**: 完整功能 + 硬件控制
- **Docker**: 容器化部署支持

## 错误处理和监控

### 统一错误处理

```python
async def enhanced_function_call(tool_name: str, **kwargs):
    try:
        adapter = await get_mcp_adapter()
        return await adapter.call_tool_enhanced(tool_name, **kwargs)
    except Exception as e:
        logger.warning(f"MCP增强调用失败，回退到原始调用: {e}")
        return await FunctionRegistry.call(tool_name, **kwargs)
```

### 健康检查

- **连接监控**: 实时监控MCP服务器连接状态
- **工具可用性**: 定期检查工具可用性
- **自动重连**: 连接断开时自动重连

## 最佳实践

### 工具开发建议

1. **本地工具**: 适用于系统核心功能，性能要求高
2. **MCP工具**: 适用于扩展功能，便于模块化管理
3. **错误处理**: 提供用户友好的错误信息
4. **异步支持**: 长时间运行的操作使用异步实现

### 性能优化

1. **连接池**: MCP客户端连接复用
2. **缓存机制**: 工具schema缓存
3. **并发控制**: 合理控制并发工具调用数量
4. **资源管理**: 及时清理不用的连接和资源

## 使用示例

### 本地工具调用示例

```python
# 计算器工具
user_input = "帮我计算 15 + 27"
# LLM检测到需要调用calculator工具
tool_call = {
    "function": {
        "name": "calculator",
        "arguments": '{"op": "add", "a": 15, "b": 27}'
    }
}
result = await FunctionRegistry.call("calculator", op="add", a=15, b=27)
# 返回: "计算结果：42.00"

# 音乐播放工具
user_input = "播放一首轻松的音乐"
# 1. 先获取音乐列表
music_list = await FunctionRegistry.call("music_player_list")
# 2. 播放指定音乐
result = await FunctionRegistry.call("music_player_play", filename="relaxing_music.mp3")
```

### MCP工具调用示例

```python
# MCP系统状态查询
user_input = "查看系统状态"
# LLM调用MCP工具
tool_call = {
    "function": {
        "name": "mcp:core_tools:get_system_status",
        "arguments": '{}'
    }
}
result = await compatible_function_call("mcp:core_tools:get_system_status")
# 返回系统详细状态信息

# MCP复杂表达式计算
user_input = "计算 (15 + 27) * 3 / 2"
result = await compatible_function_call(
    "mcp:core_tools:advanced_calculator",
    expression="(15 + 27) * 3 / 2"
)
```

### 混合工具调用示例

```python
# 在一次对话中同时使用本地工具和MCP工具
async def handle_complex_request():
    # 1. 使用MCP工具获取系统状态
    system_status = await compatible_function_call("mcp:core_tools:get_system_status")

    # 2. 使用本地工具播放提示音
    await FunctionRegistry.call("music_player_play", filename="notification.mp3")

    # 3. 使用本地工具控制摄像头
    await FunctionRegistry.call("yolo_reset_servo")

    return f"系统检查完成: {system_status}"
```

## 开发指南

### 添加新的本地工具

```python
# 1. 实现工具函数
@FunctionRegistry.register_function('weather_query', '查询天气信息')
async def weather_query_func(city: str) -> str:
    """查询指定城市的天气信息"""
    try:
        # 实现天气查询逻辑
        weather_data = await get_weather_data(city)
        return f"{city}的天气：{weather_data['description']}，温度{weather_data['temperature']}°C"
    except Exception as e:
        return f"天气查询失败：{str(e)}"

# 2. 工具会自动注册到Function Registry
# 3. LLM可以直接调用该工具
```

### 添加新的MCP服务器

```python
# 1. 创建MCP服务器文件
# Backend/utils/mcp/server/weather_mcp_server.py

from fastmcp import FastMCP

mcp = FastMCP(name="Weather Service")

@mcp.tool
def get_weather(city: str, units: str = "celsius") -> str:
    """获取天气信息"""
    # 实现天气API调用
    return f"{city}的天气信息"

if __name__ == "__main__":
    mcp.run()

# 2. 配置MCP客户端连接
mcp_config = {
    "command": "python",
    "args": ["Backend/utils/mcp/server/weather_mcp_server.py"]
}

# 3. 添加到MCP管理器
await mcp_manager.add_mcp_server("weather_service", mcp_config)
```

### 工具Schema定义

```python
# Function Calling工具Schema
{
    "type": "function",
    "function": {
        "name": "calculator",
        "description": "执行基本数学计算",
        "parameters": {
            "type": "object",
            "properties": {
                "op": {
                    "type": "string",
                    "enum": ["add", "sub", "mul", "div"],
                    "description": "运算类型"
                },
                "a": {"type": "number", "description": "第一个数字"},
                "b": {"type": "number", "description": "第二个数字"}
            },
            "required": ["op", "a", "b"]
        }
    }
}

# MCP工具自动转换为Function Calling格式
def to_function_calling_format(self) -> Dict[str, Any]:
    return {
        "type": "function",
        "function": {
            "name": self.name,
            "description": self.description,
            "parameters": self.parameters
        }
    }
```

## 故障排除

### 常见问题

**1. MCP连接失败**
```
错误: MCP库不可用，无法连接到MCP服务器
解决: 检查MCP依赖是否正确安装，确认服务器进程是否启动
```

**2. 工具调用超时**
```
错误: 工具调用超时
解决: 检查网络连接，增加超时时间，或实现异步调用
```

**3. 环境适配问题**
```
错误: Function Calling模块导入失败
解决: 检查音频库依赖，在WSL2环境中使用模拟模式
```

### 调试工具

```python
# 1. 查看已注册的工具
def list_all_tools():
    local_tools = list(FunctionRegistry._registry.keys())
    mcp_tools = [tool.name for tool in mcp_manager.get_available_tools()]
    return {"local": local_tools, "mcp": mcp_tools}

# 2. 测试工具调用
async def test_tool_call(tool_name: str, **kwargs):
    try:
        result = await compatible_function_call(tool_name, **kwargs)
        return {"success": True, "result": result}
    except Exception as e:
        return {"success": False, "error": str(e)}

# 3. 检查MCP连接状态
def check_mcp_status():
    return {
        "connected_servers": list(mcp_manager.clients.keys()),
        "available_tools": len(mcp_manager.get_available_tools()),
        "health": "healthy" if mcp_manager.clients else "disconnected"
    }
```

## 性能监控

### 工具调用统计

```python
class ToolCallMonitor:
    def __init__(self):
        self.call_count = {}
        self.call_times = {}
        self.error_count = {}

    async def monitor_call(self, tool_name: str, func, *args, **kwargs):
        start_time = time.time()
        try:
            result = await func(*args, **kwargs)
            self.call_count[tool_name] = self.call_count.get(tool_name, 0) + 1
            self.call_times[tool_name] = time.time() - start_time
            return result
        except Exception as e:
            self.error_count[tool_name] = self.error_count.get(tool_name, 0) + 1
            raise

    def get_stats(self):
        return {
            "call_count": self.call_count,
            "avg_call_time": {k: v for k, v in self.call_times.items()},
            "error_rate": {k: v/self.call_count.get(k, 1) for k, v in self.error_count.items()}
        }
```

## 总结

FC & MCP集成方案为eu03-asr-llm-tts系统提供了强大的工具调用能力：

### 核心优势
- **统一接口**: 本地工具和MCP工具的透明调用
- **环境适配**: 自动适配不同运行环境
- **智能控制**: V1.5.0静默调用模式优化用户体验
- **扩展性**: 支持动态添加新工具和MCP服务器
- **可靠性**: 完善的错误处理和自动恢复机制

### 技术特色
- **异步优先**: 全面支持异步工具调用
- **类型安全**: 完整的参数验证和类型检查
- **模块化设计**: 清晰的组件分离和接口定义
- **监控完善**: 详细的调用统计和性能监控

### 应用场景
- **智能对话**: LLM驱动的自然语言工具调用
- **系统控制**: 音频、视频、硬件设备控制
- **信息查询**: 系统状态、计算、数据处理
- **远程操作**: 网络设备和服务的远程控制

这套方案确保了系统的高可用性、可扩展性和良好的用户体验，为构建智能语音助手提供了坚实的技术基础。
