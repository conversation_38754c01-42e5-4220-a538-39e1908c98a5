# 应用状态管理器使用指南

## 概述

应用状态管理器是为ASR-LLM-TTS语音对话助手系统设计的统一状态管理解决方案。它解决了现有前端状态分散、同步复杂、维护困难等问题，提供了类型安全、高性能、易维护的状态管理方案。

## 设计原则

### 1. 类型安全
- 完整的TypeScript类型支持
- 编译时错误检查
- 智能代码提示

### 2. 不可变性
- 使用不可变状态更新
- 防止意外的状态修改
- 支持时间旅行调试

### 3. 可预测性
- 清晰的状态转换逻辑
- 单向数据流
- 易于调试和测试

### 4. 性能优化
- 减少不必要的重渲染
- 智能状态订阅
- 内存使用优化

## 核心概念

### 状态结构

```typescript
interface AppState {
  connection: ConnectionState    // WebSocket连接状态
  conversation: ConversationState // 消息和对话状态
  ui: UIState                   // UI交互状态
  system: SystemState           // 系统状态和错误
  tools: ToolsState            // 工具调用状态
}
```

### 操作类型

所有状态变更都通过预定义的Action进行：

```typescript
type AppAction = 
  | { type: 'CONNECTION_CONNECTED'; payload: { socket: WebSocket } }
  | { type: 'MESSAGE_ADD'; payload: { message: Message } }
  | { type: 'UI_SET_INPUT_MODE'; payload: { mode: InputMode } }
  // ... 更多操作类型
```

## 快速开始

### 1. 安装状态管理器

在应用根组件中包装`AppStateProvider`：

```tsx
import { AppStateProvider } from '@/components/app-state-provider'

export default function App() {
  return (
    <AppStateProvider autoConnect={true}>
      <YourAppComponents />
    </AppStateProvider>
  )
}
```

### 2. 在组件中使用状态

```tsx
import { useAppState } from '@/components/app-state-provider'

function MyComponent() {
  const { 
    state, 
    sendUserInput, 
    setInputMode,
    addMessage 
  } = useAppState()
  
  const { messages, isConnected } = state.conversation
  
  // 使用状态和操作...
}
```

## API参考

### 状态访问

#### 连接状态
```typescript
const { connection } = state
// connection.state: 'disconnected' | 'connecting' | 'connected' | 'reconnecting' | 'error'
// connection.socket: WebSocket | null
// connection.error: string | null
```

#### 对话状态
```typescript
const { conversation } = state
// conversation.messages: Message[]
// conversation.currentInput: string
// conversation.isAssistantTyping: boolean
```

#### UI状态
```typescript
const { ui } = state
// ui.inputMode: 'keyboard' | 'voice'
// ui.isListening: boolean
// ui.asrResult: string
```

### 操作函数

#### 连接管理
```typescript
connectWebSocket(url?: string): void
disconnectWebSocket(): void
sendMessage(message: WebSocketMessage): boolean
```

#### 消息管理
```typescript
addMessage(message: Omit<Message, 'id' | 'timestamp'>): string
updateMessage(id: string, updates: Partial<Message>): void
clearMessages(): void
```

#### 输入管理
```typescript
setInput(text: string): void
clearInput(): void
sendUserInput(text?: string): void
```

#### UI状态管理
```typescript
setInputMode(mode: InputMode): void
setListening(isListening: boolean): void
setAsrResult(result: string): void
```

## 迁移指南

### 从现有WebSocketProvider迁移

**之前：**
```tsx
const { sendMessage, isConnected, lastMessage } = useWebSocket()
const [messages, setMessages] = useState<Message[]>([])
const [input, setInput] = useState("")
```

**之后：**
```tsx
const { 
  state: { connection: { state }, conversation: { messages, currentInput } },
  sendMessage,
  setInput,
  addMessage
} = useAppState()

const isConnected = state === 'connected'
```

### 从ChatInterface迁移

**之前：**
```tsx
const [isAssistantTyping, setIsAssistantTyping] = useState(false)
const [isVoiceMode, setIsVoiceMode] = useState(false)
const [isListening, setIsListening] = useState(false)

// 复杂的消息处理逻辑...
```

**之后：**
```tsx
const {
  state: { 
    conversation: { isAssistantTyping },
    ui: { inputMode, isListening }
  },
  setInputMode,
  setListening,
  setAssistantTyping
} = useAppState()

// 简化的逻辑，状态管理器处理复杂性
```

## 最佳实践

### 1. 状态订阅优化

只订阅需要的状态片段：

```tsx
// ❌ 不好：订阅整个状态
const { state } = useAppState()

// ✅ 好：只订阅需要的部分
const { 
  state: { conversation: { messages } },
  addMessage 
} = useAppState()
```

### 2. 操作函数使用

优先使用提供的操作函数而不是直接dispatch：

```tsx
// ❌ 不好：直接使用dispatch
dispatch({ type: 'MESSAGE_ADD', payload: { message } })

// ✅ 好：使用操作函数
addMessage(message)
```

### 3. 错误处理

利用统一的错误状态管理：

```tsx
const { 
  state: { system: { errors } },
  addError,
  clearError 
} = useAppState()

// 添加错误
addError({
  message: "操作失败",
  source: "user_action",
  severity: 'error'
})

// 清除错误
errors.forEach(error => clearError(error.id))
```

### 4. 性能优化

使用React.memo和useCallback优化渲染：

```tsx
const MessageList = React.memo(({ messages }: { messages: Message[] }) => {
  return (
    <div>
      {messages.map(message => (
        <MessageItem key={message.id} message={message} />
      ))}
    </div>
  )
})

function ChatComponent() {
  const { state: { conversation: { messages } } } = useAppState()
  
  return <MessageList messages={messages} />
}
```

## 调试和开发工具

### 1. 状态日志

状态管理器内置了详细的日志记录：

```typescript
// 在开发环境中启用详细日志
console.log("Current state:", state)
console.log("Last action:", lastAction)
```

### 2. 状态检查

可以在任何组件中检查当前状态：

```tsx
function DebugPanel() {
  const { state } = useAppState()
  
  return (
    <pre>{JSON.stringify(state, null, 2)}</pre>
  )
}
```

### 3. 时间旅行调试

状态管理器支持状态历史记录：

```typescript
// 查看消息历史
const { state: { system: { messageHistory } } } = useAppState()
console.log("WebSocket message history:", messageHistory)
```

## 常见问题

### Q: 如何处理异步操作？

A: 使用提供的操作函数，它们内部处理了异步逻辑：

```tsx
// 发送消息（异步）
const success = sendMessage({ type: 'USER_TEXT_INPUT', payload: { text } })
if (!success) {
  // 处理发送失败
}
```

### Q: 如何扩展状态结构？

A: 修改`AppState`接口和相应的reducer逻辑：

```typescript
interface AppState {
  // 现有状态...
  newFeature: {
    data: any[]
    loading: boolean
  }
}
```

### Q: 如何处理复杂的状态依赖？

A: 使用useEffect监听相关状态变化：

```tsx
const { state: { ui: { asrResult, inputMode } } } = useAppState()

useEffect(() => {
  if (inputMode === 'voice' && asrResult) {
    // 处理语音识别结果
  }
}, [asrResult, inputMode])
```

## 总结

应用状态管理器提供了一个强大、类型安全、高性能的状态管理解决方案。通过统一的状态管理，可以：

1. **简化组件逻辑**：组件只需关注UI渲染
2. **提高代码质量**：类型安全和清晰的API
3. **增强可维护性**：集中的状态管理和操作
4. **优化性能**：减少不必要的重渲染
5. **改善开发体验**：丰富的调试工具和文档

建议逐步迁移现有组件，先从简单的组件开始，逐渐替换复杂的状态管理逻辑。
