#!/bin/bash

# ASR-LLM-TTS 服务状态查看脚本

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 获取本机IP地址
get_local_ip() {
    local ip=""

    # 方式1: 优先使用ip route获取默认路由的源IP (最准确)
    if command -v ip &> /dev/null; then
        ip=$(ip route get ******* 2>/dev/null | grep -oP 'src \K\S+' | head -1)
    fi

    # 方式2: 如果上面失败，检查活跃的网络接口
    if [ -z "$ip" ] && command -v ifconfig &> /dev/null; then
        # 优先检查wlan0 (WiFi)，然后是eth0 (有线)
        for interface in wlan0 eth0 enp0s3 ens33; do
            if ifconfig $interface 2>/dev/null | grep -q "inet " && ifconfig $interface 2>/dev/null | grep -q "RUNNING"; then
                ip=$(ifconfig $interface 2>/dev/null | grep 'inet ' | awk '{print $2}' | head -1)
                if [ -n "$ip" ] && [ "$ip" != "127.0.0.1" ]; then
                    break
                fi
            fi
        done
    fi

    # 方式3: 使用ip命令检查活跃接口
    if [ -z "$ip" ] && command -v ip &> /dev/null; then
        for interface in wlan0 eth0 enp0s3 ens33; do
            if ip link show $interface 2>/dev/null | grep -q "state UP"; then
                ip=$(ip addr show $interface 2>/dev/null | grep 'inet ' | awk '{print $2}' | cut -d/ -f1 | head -1)
                if [ -n "$ip" ] && [ "$ip" != "127.0.0.1" ]; then
                    break
                fi
            fi
        done
    fi

    echo "$ip"
}

echo -e "${BLUE}=== ASR-LLM-TTS 服务状态 ===${NC}"

# 检查前端服务
FRONTEND_PIDS=$(pgrep -f "npm run dev" 2>/dev/null | grep -v grep)
if [ -z "$FRONTEND_PIDS" ]; then
    FRONTEND_PIDS=$(pgrep -f "next dev" 2>/dev/null | grep -v grep)
fi

if [ -n "$FRONTEND_PIDS" ]; then
    FRONTEND_COUNT=$(echo "$FRONTEND_PIDS" | wc -l)
    echo -e "${GREEN}✅ 前端服务: 运行中 ($FRONTEND_COUNT 个进程)${NC}"
    echo -e "${BLUE}   PID: $(echo $FRONTEND_PIDS | tr '\n' ' ')${NC}"
else
    echo -e "${RED}❌ 前端服务: 未运行${NC}"
    FRONTEND_COUNT=0
fi

# 检查后端服务
BACKEND_PIDS=$(pgrep -f "python.*server\.py" 2>/dev/null | grep -v grep)

if [ -n "$BACKEND_PIDS" ]; then
    BACKEND_COUNT=$(echo "$BACKEND_PIDS" | wc -l)
    echo -e "${GREEN}✅ 后端服务: 运行中 ($BACKEND_COUNT 个进程)${NC}"
    echo -e "${BLUE}   PID: $(echo $BACKEND_PIDS | tr '\n' ' ')${NC}"
else
    echo -e "${RED}❌ 后端服务: 未运行${NC}"
    BACKEND_COUNT=0
fi

# 检查端口占用
echo -e "\n${PURPLE}=== 端口状态 ===${NC}"

# 检查3000端口
if netstat -tuln 2>/dev/null | grep -q ":3000 " || ss -tuln 2>/dev/null | grep -q ":3000 "; then
    echo -e "${GREEN}✅ 端口 3000: 已占用 (前端)${NC}"
else
    echo -e "${RED}❌ 端口 3000: 空闲${NC}"
fi

# 检查8000端口
if netstat -tuln 2>/dev/null | grep -q ":8000 " || ss -tuln 2>/dev/null | grep -q ":8000 "; then
    echo -e "${GREEN}✅ 端口 8000: 已占用 (后端)${NC}"
else
    echo -e "${RED}❌ 端口 8000: 空闲${NC}"
fi

# 显示访问地址
if [ "$FRONTEND_COUNT" -gt 0 ] || [ "$BACKEND_COUNT" -gt 0 ]; then
    echo -e "\n${PURPLE}=== 访问地址 ===${NC}"
    
    echo -e "${BLUE}本地访问:${NC}"
    if [ "$FRONTEND_COUNT" -gt 0 ]; then
        echo -e "  前端: http://localhost:3000"
    fi
    if [ "$BACKEND_COUNT" -gt 0 ]; then
        echo -e "  后端: http://localhost:8000"
    fi
    
    LOCAL_IP=$(get_local_ip)
    if [ -n "$LOCAL_IP" ]; then
        echo -e "${BLUE}局域网访问:${NC}"
        if [ "$FRONTEND_COUNT" -gt 0 ]; then
            echo -e "  前端: http://${LOCAL_IP}:3000"
        fi
        if [ "$BACKEND_COUNT" -gt 0 ]; then
            echo -e "  后端: http://${LOCAL_IP}:8000"
            echo -e "  WebSocket: ws://${LOCAL_IP}:8000/ws/chat"
        fi
    fi
fi

# 检查日志文件
echo -e "\n${PURPLE}=== 日志文件 ===${NC}"
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

if [ -f "$PROJECT_ROOT/frontend.log" ]; then
    FRONTEND_LOG_SIZE=$(du -h "$PROJECT_ROOT/frontend.log" | cut -f1)
    echo -e "${GREEN}✅ frontend.log: 存在 ($FRONTEND_LOG_SIZE)${NC}"
else
    echo -e "${YELLOW}⚠️  frontend.log: 不存在${NC}"
fi

if [ -f "$PROJECT_ROOT/backend.log" ]; then
    BACKEND_LOG_SIZE=$(du -h "$PROJECT_ROOT/backend.log" | cut -f1)
    echo -e "${GREEN}✅ backend.log: 存在 ($BACKEND_LOG_SIZE)${NC}"
else
    echo -e "${YELLOW}⚠️  backend.log: 不存在${NC}"
fi

# 总结
echo -e "\n${PURPLE}=== 总结 ===${NC}"
if [ "$FRONTEND_COUNT" -gt 0 ] && [ "$BACKEND_COUNT" -gt 0 ]; then
    echo -e "${GREEN}🎉 系统运行正常，前后端服务都在运行${NC}"
elif [ "$FRONTEND_COUNT" -gt 0 ] || [ "$BACKEND_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}⚠️  部分服务运行中，请检查未运行的服务${NC}"
else
    echo -e "${RED}❌ 所有服务都未运行${NC}"
    echo -e "${YELLOW}使用 ./run.sh 启动服务${NC}"
fi
