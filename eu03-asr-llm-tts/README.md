# ASR-LLM-TTS 语音对话助手

本项目是一个集成了自动语音识别 (ASR)、大型语言模型 (LLM) 和文本转语音 (TTS) 功能的语音对话助手系统。采用前后端分离架构，支持Web界面交互和语音对话，用户可以通过语音或文本输入与助手交互，助手能够理解指令、调用工具（如计算器、音乐播放器、机械臂控制等）并以自然的语音进行回复。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        前端 (Next.js)                           │
│  ┌─────────────────┐      ┌───────────────────────────────────┐ │
│  │   ChatInterface │◄────►│      WebSocketProvider           │ │
│  │   (聊天界面)     │      │   (WebSocket通信管理)             │ │
│  └─────────────────┘      └───────────────┬───────────────────┘ │
│           ▲                               │                     │
│           │                               ▼                     │
│  ┌────────────────┐            ┌────────────────────┐          │
│  │     Sphere     │            │    UI Components   │          │
│  │  (3D状态可视化) │            │   (shadcn/ui)      │          │
│  └────────────────┘            └────────────────────┘          │
└─────────────────────────────────┬───────────────────────────────┘
                                  │ WebSocket
                                  ▼
┌─────────────────────────────────────────────────────────────────┐
│                      后端 (FastAPI)                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   ASR Service   │  │  Turn Manager   │  │  TTS Player     │ │
│  │  (语音识别)      │  │  (对话管理)      │  │  (语音合成)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│           │                     │                     │         │
│           ▼                     ▼                     ▼         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  Sherpa-NCNN    │  │   LLM Client    │  │  Audio Player   │ │
│  │   (本地ASR)      │  │  (火山引擎)      │  │  (音频播放)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│                               │                                 │
│                               ▼                                 │
│                    ┌─────────────────┐                         │
│                    │ Function Call   │                         │
│                    │   (工具调用)     │                         │
│                    └─────────────────┘                         │
└─────────────────────────────────────────────────────────────────┘
```

## 🌟 主要功能

### 核心能力
- **🎤 语音识别 (ASR)**: 使用 Sherpa-NCNN 进行本地、实时的语音识别
- **🧠 大型语言模型 (LLM)**: 对接火山引擎 Doubao 系列，支持 Function Calling
- **🔊 文本转语音 (TTS)**: 火山引擎 TTS 服务，流式语音合成与播放
- **🌐 Web界面**: 基于 Next.js 的现代化前端界面，支持实时对话
- **📱 响应式设计**: 适配移动端和桌面端，支持局域网访问

### Function Calling 工具生态
- **🧮 计算器**: 基本数学运算
- **🎵 音乐播放器**: 本地音乐文件播放（wav, mp3, flac）
- **🔊 音量控制**: 分别控制背景音乐和语音助手音量
- **🤖 机械臂控制**: 物体抓取、放置、方向移动等操作
- **👁️ 视觉识别**: 物体检测和场景理解
- **📹 摄像头控制**: 云台转动、人脸追踪等功能

### 技术特性
- **🔄 双向流式处理**: LLM 输出、TTS 合成、音频播放并发处理
- **⚡ 实时交互**: WebSocket 通信，低延迟响应
- **🎛️ 多输入模式**: 支持文本输入和语音输入
- **⚙️ 高度可配置**: 通过 `config.yaml` 灵活配置各项参数
- **🔧 模块化设计**: 前后端分离，组件高度解耦

## 📁 项目结构

```
asr-llm-tts/
├── Frontend/             # 前端项目 (Next.js + TypeScript)
│   ├── app/              # Next.js 应用目录
│   ├── components/       # React 组件
│   ├── hooks/            # 自定义 Hooks
│   ├── lib/              # 工具库
│   ├── public/           # 静态资源
│   ├── package.json      # 前端依赖配置
│   └── README.md         # 前端技术文档
├── Backend/              # 后端项目 (FastAPI + Python)
│   ├── app/              # 主应用程序逻辑
│   ├── asr/              # 语音识别模块
│   ├── llm/              # 大型语言模型交互模块
│   ├── tts/              # 文本转语音模块
│   ├── utils/            # 通用工具和辅助模块
│   ├── test/             # 测试脚本
│   ├── model/            # 存放 ASR/VAD 模型文件
│   ├── resource/         # 存放音乐文件
│   ├── config.yaml       # 全局配置文件
│   ├── server.py         # FastAPI 服务器入口
│   ├── requirements.txt  # Python 依赖
│   └── README.md         # 后端技术文档
├── docs/                 # 项目文档
├── run.sh                # 一键启动脚本
├── status.sh             # 服务状态查看脚本
├── README.md             # 项目总体说明 (本文档)
└── version.md            # 版本更新记录
```

## 🔧 前后端技术架构

### 前端架构 (Frontend/)
> 详细技术文档请参考：[Frontend/README.md](Frontend/README.md)

**技术栈：**
- **框架**: Next.js 15.2.4 (React 19) + TypeScript
- **样式**: TailwindCSS + shadcn/ui 组件库
- **3D渲染**: Three.js (WebGL 3D库)
- **通信**: WebSocket 实时双向通信
- **状态管理**: React Context API

**核心组件：**
- **WebSocketProvider**: WebSocket连接管理和消息处理
- **ChatInterface**: 聊天界面，消息显示和输入处理
- **Sphere**: 3D状态可视化，使用GLSL着色器实现动态效果
- **UI组件**: 基于shadcn/ui的现代化界面组件

### 后端架构 (Backend/)
> 详细技术文档请参考：[Backend/README.md](Backend/README.md)

**技术栈：**
- **框架**: FastAPI + Uvicorn
- **语音识别**: Sherpa-NCNN (本地)
- **大语言模型**: 火山引擎 Doubao
- **语音合成**: 火山引擎 TTS
- **音频处理**: sounddevice + pygame

**核心模块：**
- **server.py**: FastAPI服务器，WebSocket端点管理
- **app/**: 应用逻辑层，对话管理和状态控制
- **asr/**: 语音识别服务，多进程架构
- **llm/**: LLM客户端，支持流式响应和Function Calling
- **tts/**: TTS客户端，双向流式语音合成
- **utils/**: 工具库，音频播放器和Function Calling实现

## 🚀 快速启动

### 一键启动（推荐）

项目提供了一键启动脚本，适用于Linux板卡环境：

```bash
# 启动前后端服务
./run.sh
```

启动成功后会显示：
```
=== 启动完成 ===
=== 本地访问地址 ===
前端地址: http://localhost:3000
后端地址: http://localhost:8000
=== 局域网访问地址 ===
前端地址: http://***********:3000
后端地址: http://***********:8000
WebSocket: ws://***********:8000/ws/chat
局域网内其他设备可使用上述IP地址访问
```

### 停止服务

在 `run.sh` 运行的终端中按 `Ctrl+C`，脚本会自动清理所有服务：
```
收到停止信号，正在清理服务...
正在停止前端服务 (1 个进程)...
正在停止后端服务 (1 个进程)...
✅ 所有服务已成功停止
```

### 查看服务状态

```bash
# 查看详细的服务状态
./status.sh
```

### 启动脚本功能特点

**run.sh（一体化脚本）**
- ✅ 自动获取并显示局域网IP地址
- ✅ 自动检查和安装前端依赖
- ✅ 智能等待服务启动（后端10秒，前端15秒）
- ✅ 实时进程监控和状态检查
- ✅ 增强的Ctrl+C清理功能：
  - 智能检测运行中的服务
  - 显示停止的进程数量
  - 优雅停止 + 强制清理残留进程
  - 停止结果确认
- ✅ 服务异常监控和自动退出

**status.sh**
- ✅ 详细的服务状态显示
- ✅ 端口占用检查
- ✅ 本地和局域网访问地址
- ✅ 日志文件状态
- ✅ 系统运行总结

### 系统要求

- **操作系统**: Ubuntu 22.04 Linux板卡环境
- **Python**: 3.x (系统环境，不使用虚拟环境)
- **Node.js**: 18.x或更高版本
- **端口**: 3000 (前端)、8000 (后端)

### 日志文件

- `frontend.log` - 前端服务日志
- `backend.log` - 后端服务日志

## ⚙️ 详细配置

### 环境配置

1. **克隆仓库**:
   ```bash
   git clone <your-repository-url>
   cd asr-llm-tts
   ```

2. **安装后端依赖**:
   ```bash
   cd Backend
   pip install -r requirements.txt
   ```

3. **安装前端依赖**:
   ```bash
   cd Frontend
   npm install
   ```

### 模型文件配置

**ASR模型下载**：
- 预训练模型介绍：https://k2-fsa.github.io/sherpa/onnx/pretrained_models/index.html
- 下载地址：https://github.com/k2-fsa/sherpa-onnx/releases/tag/asr-models
- 解压后放到 `Backend/model/asr/` 目录下

**VAD模型下载**：
- 下载地址：https://github.com/k2-fsa/sherpa-onnx/releases/download/asr-models/silero_vad.onnx
- 放到 `Backend/model/vad/` 目录下

### 配置文件设置

编辑 `Backend/config.yaml` 文件：

**关键配置项**：
- `app.input_mode`: 设置为 `"keyboard"` 或 `"asr"`
- `llm.volcengine.api_key`: 填入火山引擎 API Key
- `llm.model.name`: LLM 模型名称（需支持 Function Calling）
- `tts.volcengine`: 填入火山引擎 TTS 配置（AppKey, AccessKey, ResourceID）
- `asr.model_dir`: ASR 模型路径
- `asr.vad_model_path`: VAD 模型路径

**重要：API密钥配置**
- 为了安全起见，火山引擎的API密钥通过环境变量进行管理。
- 请在 `Backend/` 目录下找到 `.env.example` 文件。
- 将其复制并重命名为 `.env` (即 `Backend/.env`)。
- 编辑 `Backend/.env` 文件，填入您真实的火山引擎 LLM 和 TTS API 密钥。
  ```env
  VOLCENGINE_LLM_API_KEY="YOUR_VOLCENGINE_LLM_API_KEY_HERE"
  VOLCENGINE_TTS_APP_ID="YOUR_VOLCENGINE_TTS_APP_ID_HERE"
  VOLCENGINE_TTS_ACCESS_TOKEN="YOUR_VOLCENGINE_TTS_ACCESS_TOKEN_HERE"
  ```
  火山引擎的LLM-API密钥和TTS-API密钥需要从火山引擎官网获取。
  LLM-API密钥路径`https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey`
  TTS-API密钥路径`https://console.volcengine.com/ark/region:ark+cn-beijing/apiKey`
- `Backend/.env` 文件已被配置在根目录的 `.gitignore` 中，不会被提交到版本库。

**可选配置**：
- `audio_player.volume`: 默认音量
- `tts.voice.default_speaker`: TTS 音色
- `music_player.resource_dir`: 音乐文件目录

### 音乐文件准备

将音乐文件（wav, mp3, flac）放入 `Backend/resource/` 目录下。

## 🛠️ Function Calling 工具生态

系统支持丰富的工具调用功能，LLM 可以调用以下本地工具：

### 基础工具
- **🧮 计算器 (`calculator`)**
  - 操作：`add`, `sub`, `mul`, `div`
  - 示例："计算 12.5 乘以 4"

### 音频控制
- **🎵 音乐播放器 (`music_player_*`)**
  - `music_player_list`: "有哪些歌可以听？"
  - `music_player_play`: "播放《彩虹》这首歌"
  - `music_player_play_random`: "随便放一首歌"
  - `music_player_stop`: "停止播放音乐"
  - `music_player_set_volume`: "把背景音乐声音调到 80%"
  - `music_player_increase_volume`: "音乐大声点"
  - `music_player_decrease_volume`: "音乐小声点"

- **🔊 语音助手音量 (`tts_player_*`)**
  - `tts_player_set_volume`: "把我的声音调到 0.7"
  - `tts_player_increase_volume`: "你说话声音大一点"
  - `tts_player_decrease_volume`: "你声音太响了，小一点"

### 机械臂控制（MCP扩展功能）
- **🤖 智能视觉机械臂控制**
  - 功能已迁移到MCP服务器 (`vision-arm-control`)
  - 支持物体检测、抓取、放置等智能操作
  - 通过MCP协议提供更稳定的远程控制能力

- **📹 摄像头控制**
  - `yolo_move_servo_relative`: 控制云台转动
  - `yolo_reset_servo`: 云台回到中心位置
  - `yolo_toggle_tracking`: 开关人脸追踪
  - `yolo_get_face_count`: 查询人脸数量

## 🔄 通信机制

### WebSocket 消息类型

**客户端→服务器：**
- `USER_TEXT_INPUT`: 用户文本输入
- `INTERRUPT_TURN`: 中断当前对话
- `TOGGLE_ASR_LISTENING`: 切换ASR监听

**服务器→客户端：**
- `SYSTEM_STATUS`: 系统状态更新
- `LLM_CHUNK`: LLM流式响应片段
- `LLM_FINAL_RESPONSE`: LLM最终响应
- `TTS_STATUS`: TTS播放状态
- `ASR_FINAL_RESULT`: ASR识别结果
- `TOOL_CALL_START/RESULT`: 工具调用开始/结果
- `TURN_ENDED/INTERRUPTED`: 对话结束/中断

### 访问地址

- **前端界面**: http://localhost:3000 或 http://[板卡IP]:3000
- **后端API**: http://localhost:8000 或 http://[板卡IP]:8000
- **WebSocket**: ws://localhost:8000/ws/chat 或 ws://[板卡IP]:8000/ws/chat

## 🧪 测试与开发

### 测试脚本

`Backend/test/` 目录下包含了各种测试脚本：
- 单元测试脚本
- 集成测试脚本
- 模块功能验证脚本

### 开发调试

**查看日志**：
```bash
# 实时查看日志
tail -f frontend.log backend.log

# 或使用状态脚本
./status.sh
```

**手动启动**：
```bash
# 手动启动后端
cd Backend
python server.py

# 手动启动前端
cd Frontend
npm run dev
```

## 📚 相关文档

- [前端技术文档](Frontend/README.md) - Next.js前端架构详解
- [后端技术文档](Backend/README.md) - FastAPI后端架构详解
- [WebSocket API文档](docs/websocket_api.md) - 通信协议详解
- [版本更新记录](version.md) - 项目版本历史

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🆘 常见问题

**Q: 端口被占用怎么办？**
A: 使用 `./status.sh` 检查端口状态，或手动停止占用端口的进程。

**Q: 前端依赖安装失败？**
A: 确保 Node.js 版本 >= 18，尝试清除缓存：`npm cache clean --force`

**Q: 后端服务启动失败？**
A: 检查 `backend.log` 日志文件，确认配置文件和模型文件路径正确。

**Q: 局域网无法访问？**
A: 确认防火墙设置，检查IP地址是否正确显示。

---

**🎉 享受您的语音对话助手体验！**
