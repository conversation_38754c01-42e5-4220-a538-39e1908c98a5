"""Manages shared state variables and events for the application."""

import asyncio
from typing import Optional

# --- Global State for Interrupt Handling & Communication ---

# Event to signal the currently active turn handler task to stop.
# This is typically created and managed per turn in server.py.
current_turn_stop_event: Optional[asyncio.Event] = None

# Reference to the currently running turn handler task (set in server.py)
current_turn_task: Optional[asyncio.Task] = None

# --- Conversation State ---

# List holding the conversation history for the LLM (No longer used)
# shared_history: List[Dict[str, Any]] = []

# --- Global Instances (Consider if they belong here or should be passed) ---

# Might be better to initialize these in main.py and pass them around
# or use a dependency injection pattern.

# text_queue: asyncio.Queue = asyncio.Queue(maxsize=200) # LLM -> TTS queue
# llm_client: Optional[LLMClient] = None
# tts_client: Optional[TTSClient] = None
# audio_player: Optional[AudioNewPlayer] = None

# --- Configuration related state (Probably better loaded once in main.py) ---
# TTS_SAMPLE_RATE = 48000 # Example, should come from config
# TTS_CHANNELS = 1      # Example, should come from config
# MAX_HISTORY_TURNS = 10 # Example, could be configurable

# Note: Avoid putting too much complex logic here. This module should primarily
# hold shared data structures and synchronization primitives. 