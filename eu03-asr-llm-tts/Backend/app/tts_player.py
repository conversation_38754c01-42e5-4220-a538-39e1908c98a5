"""Encapsulates TTS synthesis and audio playback logic."""

import asyncio
import logging
from typing import Callable, Dict, Any, Optional

# Actual imports (restored)
from tts.client import TTSClient
# from tts.config import TTSConfig # Incorrect path
from utils.config_loader import TTSConfig # Corrected import path
from utils.audio_new_player import AudioNewPlayer, AudioPlayerError, PlayerState
# from app import shared_state # No longer needed here directly

logger = logging.getLogger("TTS_PLAYER")

# Define TTS_STATUS message types for clarity, mirroring server.py/plan.md
MSG_TYPE_TTS_STATUS = "TTS_STATUS"

async def stream_tts_and_play(
    tts: TTSClient, # Pass TTS Client
    player: AudioNewPlayer, # Pass Player
    text_queue: asyncio.Queue,
    stop_event: asyncio.Event, # Pass stop event
    tts_config: TTSConfig, # Pass TTS config for settings
    websocket_callback: Optional[Callable[[str, Dict[str, Any]], Any]] = None # Added websocket_callback
):
    """Gets text from queue, streams TTS, and plays audio.
       Adapted from test_llm_tts.py stream_tts_and_play.
       Now uses websocket_callback for sending TTS status updates.
    """
    logger.info("Starting TTS streaming and playback task.")
    audio_generator = None

    async def _send_tts_status(status: str, message: Optional[str] = None):
        if websocket_callback:
            payload = {"status": status}
            if message:
                payload["message"] = message
            try:
                # Assuming websocket_callback is an async function as it's awaited elsewhere
                # If it's from turn_manager, which gets it from server.py, it should be async.
                await websocket_callback(MSG_TYPE_TTS_STATUS, payload)
            except Exception as e_cb:
                logger.error(f"Error calling websocket_callback from TTS player: {e_cb}")
        else:
            logger.debug(f"TTS Status: {status} (callback not provided)") # Log if no callback

    try:
        # Start TTS bidirectional stream
        logger.info("Calling tts.synthesize_stream_bidi...")
        audio_generator = tts.synthesize_stream_bidi(text_queue=text_queue)

        # Start audio playback using settings from config
        logger.info("Calling player.play_stream...")
        # Before starting playback, send speaking status
        await _send_tts_status("speaking")

        await player.play_stream(
            audio_generator=audio_generator,
            sample_rate=tts_config.sample_rate, # Use sample rate from config
            channels=1 # Assuming mono, adjust if needed or get from config
            # stop_event is implicitly handled by player's cancellation/stop
        )
        logger.info("player.play_stream finished normally.")
        await _send_tts_status("idle") # Send idle status upon normal completion

    except AudioPlayerError as e:
        if "播放错误" in str(e) or "停止" in str(e) or "cancelled" in str(e).lower() or "interrupted" in str(e).lower():
             logger.info(f"Audio playback stopped or cancelled (likely due to interruption): {e}")
             await _send_tts_status("idle", message=f"Playback interrupted: {e}") # Interruption leads to idle
        else:
             logger.error(f"AudioPlayerError during playback: {e}", exc_info=True)
             # print(f"\nPlayback Error: {e}") # Avoid print, use callback
             await _send_tts_status("error", message=f"Playback error: {e}")
    except Exception as e:
        logger.error(f"Unexpected error during TTS/Playback: {e}", exc_info=True)
        # print(f"\nAssistant Error: TTS or Playback failed.") # Avoid print, use callback
        await _send_tts_status("error", message=f"Unexpected TTS/Playback error: {e}")
    finally:
        logger.info("TTS streaming and playback task finished.")
        # Ensure a final status is sent if not already (e.g. if an error occurred before normal completion)
        # However, the specific error/completion handlers above should cover most cases.
        # If player state is not IDLE and no error was sent, it might imply an unclean stop.
        # For now, relying on the specific handlers above.
        # Cleanup is handled by the respective clients/player internally
        # when the stream ends or is stopped. 