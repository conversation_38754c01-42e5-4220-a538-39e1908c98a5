"""
ASR服务进程，基于sherpa-onnx的VAD+非流式ASR，直接采集麦克风音频并分段识别。
"""
import os
import time
import logging
import multiprocessing
from pathlib import Path
from typing import Optional, Union, Tuple
import numpy as np
import queue

import sounddevice as sd
import sherpa_onnx

from utils.config_loader import ASRConfig

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [ASR_SERVICE] - %(message)s'
)
logger = logging.getLogger("ASR_SERVICE")

# Helper function for resampling
def resample_audio(audio_data: np.ndarray, original_sr: int, target_sr: int) -> np.ndarray:
    """使用线性插值重采样音频数据"""
    if original_sr == target_sr:
        return audio_data

    num_original_samples = len(audio_data)
    duration = num_original_samples / original_sr
    num_target_samples = int(duration * target_sr)

    if num_target_samples == 0:
        return np.array([], dtype=audio_data.dtype)

    # 创建原始和目标采样点的时间轴
    time_original = np.linspace(0, duration, num_original_samples, endpoint=False)
    time_target = np.linspace(0, duration, num_target_samples, endpoint=False)

    # 进行线性插值
    resampled_data = np.interp(time_target, time_original, audio_data)
    return resampled_data.astype(audio_data.dtype)

class ASROnnxService:
    def __init__(self, config: ASRConfig, result_queue: multiprocessing.Queue):
        self.config = config
        self.result_queue = result_queue
        self._running = False
        self._segment_id = 0
        self._recognizer = None
        self._vad = None
        self._target_asr_sample_rate = config.sample_rate # ASR模型期望的采样率
        self._window_size = None

        self._device_actual_sample_rate: int = config.sample_rate # 设备实际打开的采样率
        self._input_device_name_for_log: str = "未知"

    def _init_models(self):
        # VAD模型
        vad_model_path = self.config.vad_model_path
        assert Path(vad_model_path).is_file(), f"VAD模型文件不存在: {vad_model_path}"
        vad_config = sherpa_onnx.VadModelConfig()
        vad_config.silero_vad.model = vad_model_path
        vad_config.silero_vad.min_silence_duration = 0.25
        vad_config.sample_rate = self._target_asr_sample_rate # VAD 使用 ASR 目标采样率
        self._window_size = vad_config.silero_vad.window_size
        self._vad = sherpa_onnx.VoiceActivityDetector(vad_config, buffer_size_in_seconds=100)
        logger.info(f"VAD模型加载完成: {vad_model_path} (期望输入采样率: {self._target_asr_sample_rate} Hz)")
        print("VAD模型加载完成，正在加载ASR模型，可能需要较长时间，请耐心等待...")
        logger.info("正在加载ASR模型，可能需要较长时间，请耐心等待...")

        # ASR模型
        asr_model_dir = Path(__file__).parent.parent / self.config.model_dir
        tokens = str(asr_model_dir / self.config.model_files["tokens"])
        paraformer = str(asr_model_dir / self.config.model_files["paraformer"])
        assert Path(tokens).is_file(), f"ASR tokens文件不存在: {tokens}"
        assert Path(paraformer).is_file(), f"ASR paraformer模型不存在: {paraformer}"
        self._recognizer = sherpa_onnx.OfflineRecognizer.from_paraformer(
            paraformer=paraformer,
            tokens=tokens,
            num_threads=self.config.num_threads,
            sample_rate=self._target_asr_sample_rate, # ASR 模型使用其目标采样率
            feature_dim=self.config.feature_dim,
            decoding_method=self.config.decoding_method,
            debug=False
        )
        logger.info(f"ASR模型加载完成: {paraformer} (期望输入采样率: {self._target_asr_sample_rate} Hz)")

    def _select_input_device(self) -> Tuple[Optional[Union[int, str]], int, str]:
        """
        根据配置中的关键词选择输入设备。
        返回 (设备标识符, 实际打开采样率, 用于日志的设备名)
        """
        target_keyword = self.config.target_microphone_keyword
        asr_target_sr = self._target_asr_sample_rate
        device_to_open_sr = asr_target_sr # 默认尝试以ASR目标采样率打开
        device_name_for_log = "默认设备"

        if not target_keyword:
            logger.info(f"配置中未指定 target_microphone_keyword，尝试使用默认输入设备。ASR目标采样率: {asr_target_sr}Hz。")
            try:
                sd.check_input_settings(samplerate=asr_target_sr, channels=1)
                logger.info(f"默认输入设备原生支持 {asr_target_sr}Hz 和单通道。")
                device_to_open_sr = asr_target_sr
                default_device_info = sd.query_devices(kind='input')
                if default_device_info and isinstance(default_device_info, dict): # query_devices(kind='input') 返回单个设备字典
                    device_name_for_log = default_device_info.get('name', "默认设备")
                return None, device_to_open_sr, device_name_for_log
            except sd.PortAudioError:
                logger.warning(f"默认输入设备不支持ASR目标采样率 {asr_target_sr}Hz。将尝试以其自身默认采样率打开并进行重采样。")
                try:
                    default_device_info = sd.query_devices(kind='input')
                    if default_device_info and isinstance(default_device_info, dict):
                         device_to_open_sr = int(default_device_info['default_samplerate'])
                         device_name_for_log = default_device_info.get('name', "默认设备")
                         sd.check_input_settings(samplerate=device_to_open_sr, channels=1, device=None) # 检查默认设备是否能以其默认采样率打开
                         logger.info(f"将以默认设备 ({device_name_for_log}) 的默认采样率 {device_to_open_sr}Hz 打开，并重采样到 {asr_target_sr}Hz。")
                         return None, device_to_open_sr, device_name_for_log
                    else:
                        raise sd.PortAudioError("无法获取默认输入设备信息。")
                except Exception as e_default_sr:
                    logger.error(f"尝试获取或检查默认设备默认采样率失败: {e_default_sr}。ASR服务无法启动。")
                    logger.error("请在 config.yaml中指定一个有效的 'target_microphone_keyword' 或确保默认设备可用。可用设备如下：")
                    for i, dev_info in enumerate(sd.query_devices()):
                        if dev_info['max_input_channels'] > 0:
                            logger.error(f"  设备 {i}: {dev_info['name']} (默认采样率: {dev_info['default_samplerate']})")
                    return "ERROR_DEFAULT_DEVICE_UNSUITABLE", asr_target_sr, "错误"

        target_keyword_lower = target_keyword.lower()
        devices = sd.query_devices()
        matched_devices = []
        for i, device_info in enumerate(devices):
            if device_info['max_input_channels'] > 0 and target_keyword_lower in device_info['name'].lower():
                matched_devices.append({'index': i, 'name': device_info['name'], 'info': device_info})

        if not matched_devices:
            logger.error(f"未找到名称包含关键词 \'{target_keyword}\' 的输入设备。将尝试使用默认设备逻辑。")
            # 回退到默认设备逻辑 (与上方 target_keyword 为空时类似)
            return self._select_input_device_fallback_to_default()

        if len(matched_devices) > 1:
            logger.warning(f"找到多个名称包含关键词 \'{target_keyword}\' 的设备。将使用第一个匹配到的设备: {matched_devices[0]['name']}\")")
            for dev in matched_devices:
                logger.info(f"  匹配项: 设备 {dev['index']}: {dev['name']}\")")

        selected_device_info_dict = matched_devices[0]
        selected_device_index = selected_device_info_dict['index']
        selected_device_name = selected_device_info_dict['name']
        device_name_for_log = selected_device_name
        
        logger.info(f"根据关键词 \'{target_keyword}\' 初步选择输入设备: {selected_device_name} (索引: {selected_device_index}). ASR目标采样率: {asr_target_sr}Hz.")

        try:
            # 尝试以ASR目标采样率打开选定设备
            sd.check_input_settings(device=selected_device_index, samplerate=asr_target_sr, channels=1)
            logger.info(f"选定设备 '{selected_device_name}' 原生支持 {asr_target_sr}Hz。将以此采样率打开。")
            device_to_open_sr = asr_target_sr
        except sd.PortAudioError:
            logger.warning(f"选定设备 '{selected_device_name}' 不支持ASR目标采样率 {asr_target_sr}Hz。")
            try:
                # 如果不支持，则尝试以该设备的默认采样率打开
                actual_device_info = sd.query_devices(selected_device_index)
                device_to_open_sr = int(actual_device_info['default_samplerate'])
                sd.check_input_settings(device=selected_device_index, samplerate=device_to_open_sr, channels=1)
                logger.info(f"将以选定设备 '{selected_device_name}' 的默认采样率 {device_to_open_sr}Hz 打开，并重采样到 {asr_target_sr}Hz。")
            except Exception as e_dev_sr:
                logger.error(f"尝试获取或检查选定设备 '{selected_device_name}' 的默认采样率失败: {e_dev_sr}。ASR服务无法启动。")
                return f"ERROR_DEVICE_UNSUITABLE_{selected_device_index}", asr_target_sr, selected_device_name
        
        return selected_device_index, device_to_open_sr, device_name_for_log

    def _select_input_device_fallback_to_default(self) -> Tuple[Optional[Union[int, str]], int, str]:
        """ _select_input_device 的辅助函数，用于处理未找到关键词匹配时回退到默认设备的逻辑。 """
        asr_target_sr = self._target_asr_sample_rate
        device_to_open_sr = asr_target_sr
        device_name_for_log = "默认设备 (回退)"
        logger.info(f"回退：尝试使用默认输入设备。ASR目标采样率: {asr_target_sr}Hz。")
        try:
            sd.check_input_settings(samplerate=asr_target_sr, channels=1)
            logger.info(f"默认输入设备 (回退) 原生支持 {asr_target_sr}Hz。")
            default_device_info = sd.query_devices(kind='input')
            if default_device_info and isinstance(default_device_info, dict):
                device_name_for_log = default_device_info.get('name', "默认设备 (回退)")
            return None, device_to_open_sr, device_name_for_log
        except sd.PortAudioError:
            logger.warning(f"默认输入设备 (回退) 不支持ASR目标采样率 {asr_target_sr}Hz。尝试以其自身默认采样率打开并重采样。")
            try:
                default_device_info = sd.query_devices(kind='input')
                if default_device_info and isinstance(default_device_info, dict):
                     device_to_open_sr = int(default_device_info['default_samplerate'])
                     device_name_for_log = default_device_info.get('name', "默认设备 (回退)")
                     sd.check_input_settings(samplerate=device_to_open_sr, channels=1, device=None)
                     logger.info(f"将以默认设备 ({device_name_for_log}) 的默认采样率 {device_to_open_sr}Hz 打开 (回退)，并重采样到 {asr_target_sr}Hz。")
                     return None, device_to_open_sr, device_name_for_log
                else:
                    raise sd.PortAudioError("无法获取默认输入设备信息 (回退)。")
            except Exception as e_default_sr:
                logger.error(f"尝试获取或检查默认设备默认采样率失败 (回退): {e_default_sr}。ASR服务无法启动。")
                return "ERROR_FALLBACK_DEVICE_UNSUITABLE", asr_target_sr, "错误 (回退)"

    def _main_loop(self):
        loop_start_time = time.monotonic()
        last_log_time = loop_start_time
        cycle_count = 0

        input_device_specifier, actual_sr_to_open, device_name = self._select_input_device()
        self._device_actual_sample_rate = actual_sr_to_open # 保存实际打开的采样率
        self._input_device_name_for_log = device_name       # 保存设备名用于日志

        if isinstance(input_device_specifier, str) and "ERROR" in input_device_specifier:
            logger.error(f"无法配置合适的输入设备 ({input_device_specifier})，ASR 服务无法启动。")
            return

        # VAD和ASR模型期望的是self._target_asr_sample_rate
        # 但音频流以self._device_actual_sample_rate打开
        # 如果两者不同，后续需要重采样

        samples_per_read = int(0.1 * self._device_actual_sample_rate) # 根据实际打开的采样率计算每读取的样本数
        buffer = np.array([], dtype=np.float32)
        logger.info("ASR服务已启动，等待语音输入...")

        logger.info(f"尝试打开音频流，设备: {self._input_device_name_for_log}, "
                    f"请求打开采样率: {self._device_actual_sample_rate}Hz, "
                    f"ASR目标采样率: {self._target_asr_sample_rate}Hz")

        try:
             with sd.InputStream(
                 device=input_device_specifier,
                 channels=1,
                 dtype="float32",
                 samplerate=self._device_actual_sample_rate # 以选择的或设备默认的采样率打开
             ) as s:
                logger.info(f"音频流成功打开 ({self._input_device_name_for_log} @ {self._device_actual_sample_rate}Hz)，进入主循环...")
                while self._running:
                    try:
                        read_start_time = time.monotonic()
                        samples, _ = s.read(samples_per_read)
                        read_duration = time.monotonic() - read_start_time

                        samples = samples.reshape(-1)

                        # 如果设备采样率与ASR模型所需采样率不同，则进行重采样
                        if self._device_actual_sample_rate != self._target_asr_sample_rate:
                            resample_start_time = time.monotonic()
                            samples = resample_audio(samples, self._device_actual_sample_rate, self._target_asr_sample_rate)
                            resample_duration = time.monotonic() - resample_start_time
                            if resample_duration > 0.005: # Log if resampling takes noticeable time
                                logger.debug(f"Resampling from {self._device_actual_sample_rate}Hz to {self._target_asr_sample_rate}Hz took: {resample_duration:.4f}s for {len(samples)} samples")
                        
                        if len(samples) == 0: # Resampling might result in zero samples if input is too short
                            continue

                        buffer = np.concatenate([buffer, samples])

                        vad_loop_start_time = time.monotonic()
                        vad_processed_count = 0
                        # VAD 的 window_size 是基于 self._target_asr_sample_rate 计算的
                        while len(buffer) >= self._window_size: # _window_size 是在 _init_models 中基于 target_asr_sample_rate 计算的
                            vad_accept_start_time = time.monotonic()
                            # 送给VAD的数据应该是重采样到ASR目标采样率之后的数据
                            self._vad.accept_waveform(buffer[:self._window_size])
                            vad_accept_duration = time.monotonic() - vad_accept_start_time
                            if vad_accept_duration > 0.01:
                                 logger.debug(f"VAD accept_waveform took: {vad_accept_duration:.4f}s")
                            buffer = buffer[self._window_size:]
                            vad_processed_count += 1
                        vad_loop_duration = time.monotonic() - vad_loop_start_time
                        if vad_processed_count > 0 and vad_loop_duration > 0.05: # Increased threshold
                            logger.debug(f"VAD processing loop ({vad_processed_count} chunks) took: {vad_loop_duration:.4f}s")

                        asr_loop_start_time = time.monotonic()
                        asr_processed_count = 0
                        while not self._vad.empty():
                            asr_step_start_time = time.monotonic()
                            stream = self._recognizer.create_stream()
                            # 送给ASR的数据也是重采样后的 (VAD.front.samples 是基于重采样后数据填充的)
                            stream.accept_waveform(self._target_asr_sample_rate, self._vad.front.samples)
                            self._vad.pop()
                            decode_start_time = time.monotonic()
                            self._recognizer.decode_stream(stream)
                            decode_duration = time.monotonic() - decode_start_time
                            text = stream.result.text.strip()
                            asr_step_duration = time.monotonic() - asr_step_start_time
                            logger.debug(f"ASR decode_stream took: {decode_duration:.4f}s, Full ASR step took: {asr_step_duration:.4f}s")
                            if text:
                                self._segment_id += 1
                                logger.info(f"ASR识别结果[{self._segment_id}]: {text}")
                                put_start_time = time.monotonic()
                                try:
                                    self.result_queue.put(text, timeout=0.1)
                                except queue.Full:
                                    logger.warning("ASR result queue is full, discarding result.")
                                put_duration = time.monotonic() - put_start_time
                                if put_duration > 0.01:
                                    logger.debug(f"Putting result to queue took: {put_duration:.4f}s")
                            asr_processed_count += 1
                        asr_loop_duration = time.monotonic() - asr_loop_start_time
                        if asr_processed_count > 0: # Log only if ASR actually processed something
                             logger.debug(f"ASR processing loop ({asr_processed_count} segments) took: {asr_loop_duration:.4f}s")

                        cycle_count += 1
                        current_time = time.monotonic()
                        if current_time - last_log_time > 5.0: # Log every 5 seconds
                            avg_cycle_time = (current_time - loop_start_time) / cycle_count if cycle_count > 0 else 0
                            logger.debug(f"ASR loop status: Avg cycle time: {avg_cycle_time:.4f}s over {cycle_count} cycles. Last read took: {read_duration:.4f}s")
                            last_log_time = current_time

                    except sd.CallbackStop:
                         logger.info("Callback signaled stop, exiting main loop.")
                         self._running = False
                         break
                    except queue.Full:
                         logger.warning("ASR result queue is full in main loop, result discarded.")
                         time.sleep(0.01) # Slight pause if queue is full
                    except Exception as e:
                         logger.error(f"ASR服务主循环内部异常: {e}", exc_info=True)
                         time.sleep(0.1)
                logger.info("ASR 服务主循环已终止。")

        except sd.PortAudioError as pae:
             logger.error(f"打开音频输入流失败 (尝试的设备: {self._input_device_name_for_log}, "
                          f"请求打开采样率: {self._device_actual_sample_rate}Hz): {pae}", exc_info=True)
             if "Invalid device" in str(pae):
                  logger.error("错误提示：请检查 config.yaml 中的 asr.target_microphone_keyword 是否正确，或者设备是否已连接。")
                  logger.error("您可以通过在终端运行 'arecord -l' (Linux) 或查看系统声音设置 (其他系统) 查看可用的录音设备列表。")
             elif "Invalid sample rate" in str(pae):
                 logger.error(f"错误提示：所选/默认设备不支持请求的打开采样率 {self._device_actual_sample_rate}Hz，或者无法进行转换。")
                 logger.error("请检查设备规格。")
             elif "Device unavailable" in str(pae):
                   logger.error("错误提示：音频设备可能被其他程序占用或未插入。")
        except Exception as e:
             logger.error(f"ASR服务主循环启动或流管理时发生意外异常: {e}", exc_info=True)

        logger.info("ASR 服务 _main_loop 方法执行完毕。")

    def run(self):
        """运行 ASR 服务的主入口点"""
        self._running = True
        try:
             self._init_models() # 初始化模型，这时 self._target_asr_sample_rate 已经确定
             self._main_loop()   # 主循环中会确定 self._device_actual_sample_rate
        except Exception as e:
             logger.error(f"ASR 服务 run 方法中发生未处理的异常: {e}", exc_info=True)
        finally:
             logger.info("ASR服务进程准备退出")


def run_asr_service(config: ASRConfig, audio_queue, result_queue):
    """
    启动ASR服务进程（兼容main.py接口，audio_queue参数已废弃）
    """
    service = ASROnnxService(config, result_queue)
    try:
        service.run()
    except KeyboardInterrupt:
        logger.info("ASR服务收到键盘中断")
    except Exception as e:
        logger.error(f"ASR服务进程发生未捕获异常: {e}")
    finally:
        logger.info("ASR服务函数退出")

if __name__ == "__main__":
    print("ASR服务模块 - 此模块设计为由主程序调用，不应直接运行")
    print("要测试ASR服务，请调整main.py并使用--input-mode=asr运行主程序") 