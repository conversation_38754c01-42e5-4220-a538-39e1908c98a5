import asyncio
import uuid
import random
import websockets
import logging
from typing import Optional, AsyncGenerator, Dict, Any
from utils.config_loader import TTSConfig
from .constants import *
from .protocol import Header, ProtocolHandler
import sounddevice as sd

# 确保使用的是正确的websockets库
try:
    # 检查是否正在使用异步websockets库
    if not hasattr(websockets, 'connect'):
        logger.warning("找不到websockets.connect，尝试从websockets.client导入")
        from websockets.client import connect as ws_connect
    else:
        ws_connect = websockets.connect
except ImportError as e:
    logger.error(f"无法导入websockets模块: {e}")
    raise

# 设置logger
logger = logging.getLogger(__name__)

class TTSClient:
    def __init__(self, config: TTSConfig):
        self.config = config
        self.connect_id = str(uuid.uuid4())
        self.session_id = None
        self.websocket = None
        self.protocol = ProtocolHandler()
        self._is_connecting = False
        self._connection_lock = asyncio.Lock()
        self._max_reconnect_attempts = 3
        self._connection_timeout = 10  # 连接超时时间（秒）
        self._last_session_end_time = 0  # 上次会话结束时间
        self._min_session_interval = 0.5  # 会话之间的最小间隔时间(秒)

    async def connect(self, force_reconnect=False):
        """建立 WebSocket 连接，支持自动重连"""
        # 使用锁防止并发连接
        async with self._connection_lock:
            # 检查是否已连接且不需要强制重连
            if not force_reconnect and self.websocket and not getattr(self.websocket, 'closed', False):
                return self.websocket
                
            # 检查是否正在连接中
            if self._is_connecting:
                logger.debug("连接已在进行中，等待完成...")
                return self.websocket
                
            self._is_connecting = True
            
            try:
                # 关闭现有连接
                if self.websocket and not getattr(self.websocket, 'closed', False):
                    await self.close()
                
                # 生成新的连接ID
                self.connect_id = str(uuid.uuid4())
                
                headers = {
                    "X-Api-App-Key": self.config.app_id, # Changed from app_key
                    "X-Api-Access-Key": self.config.access_token, # Changed from access_key
                    "X-Api-Resource-Id": self.config.resource_id,
                    "X-Api-Connect-Id": self.connect_id,
                }

                logger.info(f"正在建立TTS WebSocket连接: {self.config.ws_url}")
                
                # 添加连接超时 - 修改ping设置，与官方示例保持一致
                self.websocket = await asyncio.wait_for(
                    ws_connect(
                        self.config.ws_url,
                        additional_headers=headers,
                        max_size=self.config.max_size,
                        ping_interval=None,  # 禁用客户端ping，依赖服务端控制
                    ), 
                    timeout=self._connection_timeout
                )

                # 发送开始连接事件
                await self.protocol.send_event(
                    self.websocket,
                    Header(
                        message_type=FULL_CLIENT_REQUEST,
                        message_type_flags=MSG_FLAG_WITH_EVENT
                    ),
                    event=EVENT_Start_Connection,
                    payload="{}"
                )

                # 等待连接响应
                response = await self.protocol.receive_response(self.websocket)
                if response.get("event") != EVENT_ConnectionStarted:
                    await self.websocket.close()  # 确保失败时清理
                    self.websocket = None
                    raise RuntimeError(f"连接失败: {response}")

                logger.info("TTS WebSocket连接建立成功")
                return self.websocket

            except Exception as e:
                logger.error(f"WebSocket连接失败: {e}")
                if self.websocket and not getattr(self.websocket, 'closed', False):
                    try:
                        await self.websocket.close()
                    except Exception as close_err:
                        logger.debug(f"关闭失败的连接时出错: {close_err}")
                self.websocket = None
                raise
            finally:
                self._is_connecting = False

    async def ensure_connection(self):
        """确保WebSocket连接存在且有效"""
        try:
            # 使用更通用的方式检查连接状态
            if not self.websocket or getattr(self.websocket, 'closed', False) or \
               (hasattr(self.websocket, 'open') and not self.websocket.open):
                logger.info("WebSocket连接不存在或已关闭，尝试重新连接")
                return await self.retry_with_backoff(self.connect, force_reconnect=True)
            return True
        except Exception as e:
            logger.error(f"检查连接状态时出错: {e}")
            return False

    def _get_tts_payload(self, text: str, speaker: Optional[str] = None):
        """生成 TTS 请求的 payload"""
        if speaker is None:
            speaker = self.config.default_speaker

        return {
            "user": {"uid": self.config.user_id},
            "event": EVENT_TaskRequest, # Keep event as TaskRequest here for payload structure
            "namespace": self.config.namespace,
            "req_params": {
                "text": text,
                "speaker": speaker,
                "audio_params": {
                    "format": self.config.format,
                    "sample_rate": self.config.sample_rate
                }
            }
        }

    async def start_session(self, speaker: Optional[str] = None):
        """开始一个新的合成会话，包含自动重连"""
        # 确保连接有效
        if not await self.ensure_connection():
            raise RuntimeError("无法建立WebSocket连接")

        try:
            # 生成新的会话ID
            self.session_id = str(uuid.uuid4()).replace('-', '')
            current_speaker = speaker if speaker is not None else self.config.default_speaker
            
            logger.debug(f"开始TTS会话，ID: {self.session_id}, 音色: {current_speaker}")

            # 生成用于 StartSession 的 payload
            start_session_payload_dict = self._get_tts_payload("", current_speaker)
            # 移除不必要的内嵌 event 字段 (根据文档分析)
            if 'event' in start_session_payload_dict:
                del start_session_payload_dict['event']
            logger.debug(f"StartSession payload (修正后): {start_session_payload_dict}")

            # 发送开始会话请求 (使用修正后的 payload)
            await self.protocol.send_event(
                self.websocket,
                Header(
                    message_type=FULL_CLIENT_REQUEST,
                    message_type_flags=MSG_FLAG_WITH_EVENT,
                    serial_method=JSON_SERIALIZATION
                ),
                event=EVENT_StartSession, # Correct event for the message
                session_id=self.session_id,
                payload=start_session_payload_dict # Use the modified payload
            )

            # 等待会话开始响应
            response = await self.protocol.receive_response(self.websocket)
            if response.get("event") != EVENT_SessionStarted:
                 # 清理会话ID
                 failed_session_id = self.session_id
                 self.session_id = None
                 raise RuntimeError(f"会话启动失败: {response}. Session ID: {failed_session_id}")

            logger.debug(f"TTS会话 {self.session_id} 启动成功")
            return response
            
        except websockets.exceptions.ConnectionClosed as e:
            logger.error(f"开始会话时连接已关闭: {e}")
            self.session_id = None
            self.websocket = None  # 标记连接无效
            raise RuntimeError(f"会话启动时连接断开: {e}")
        except Exception as e:
            # 清理会话ID
            self.session_id = None
            logger.error(f"开始会话失败: {e}")
            raise e

    async def synthesize_stream(self, text: str, speaker: Optional[str] = None) -> AsyncGenerator[bytes, None]:
        """合成文本为语音流 (异步生成器)，包含自动重连机制"""
        # 每次流式合成使用新的连接和会话
        try:
            # 1. 确保有新连接
            await self.connect(force_reconnect=True)
            
            # 2. 开始新会话
            await self.start_session(speaker)
            
            current_speaker = speaker if speaker is not None else self.config.default_speaker

            # 3. 发送文本合成任务请求
            logger.debug(f"发送合成请求: 会话ID={self.session_id}, 文本长度={len(text)}")
            await self.protocol.send_event(
                self.websocket,
                Header(
                    message_type=FULL_CLIENT_REQUEST,
                    message_type_flags=MSG_FLAG_WITH_EVENT,
                    serial_method=JSON_SERIALIZATION
                ),
                event=EVENT_TaskRequest,
                session_id=self.session_id,
                payload=self._get_tts_payload(text, current_speaker)
            )

            # 4. 发送结束会话请求 (告知服务器文本已发送完毕)
            await self.protocol.send_event(
                self.websocket,
                Header(
                    message_type=FULL_CLIENT_REQUEST,
                    message_type_flags=MSG_FLAG_WITH_EVENT,
                    serial_method=JSON_SERIALIZATION
                ),
                event=EVENT_FinishSession,
                session_id=self.session_id,
                payload="{}" # Payload required but can be empty JSON
            )

            # 5. 循环接收音频数据或事件
            audio_chunks_received = 0
            total_audio_bytes = 0
            session_finished = False
            
            while not session_finished:
                try:
                    response = await self.protocol.receive_response(self.websocket)
                    logger.debug(f"收到response: keys={list(response.keys())}, event={response.get('event')}, message_type={response.get('message_type')}, 长度={len(response.get('audio_data', b'')) if 'audio_data' in response else '-'}")
                    # 音频数据
                    if "audio_data" in response:
                        audio_chunks_received += 1
                        chunk_len = len(response["audio_data"])
                        total_audio_bytes += chunk_len
                        logger.debug(f"收到音频块 {audio_chunks_received}，长度: {chunk_len}，累计: {total_audio_bytes} bytes")
                        yield response["audio_data"]
                        
                    # 会话结束事件
                    elif response.get("event") == EVENT_SessionFinished:
                        logger.info(f"会话已完成，共接收 {audio_chunks_received} 个音频块，总字节: {total_audio_bytes}")
                        session_finished = True
                    
                    # 会话失败事件    
                    elif response.get("event") == EVENT_SessionFailed:
                        error_msg = response.get("payload", {})
                        logger.error(f"会话失败: {error_msg}")
                        break
                        
                    # 错误信息事件
                    elif response.get("event") == EVENT_ErrMsg:
                        error_msg = response.get("errMsg", "未知错误")
                        error_code = response.get("errCode", "未知错误码") 
                        logger.error(f"合成过程中出现错误: 代码={error_code}, 信息={error_msg}")
                        break
                        
                    # 其他事件类型
                    elif response.get("event") not in [None, EVENT_TTSResponse, EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                        logger.debug(f"收到事件: {response.get('event')}")
                
                except websockets.exceptions.ConnectionClosed as e:
                    logger.error(f"接收音频数据时连接断开: {e}")
                    break
                    
                except Exception as e:
                    logger.error(f"接收音频数据时出错: {e}")
                    break

            # 记录会话结束时间，用于避免过快启动新会话
            self._last_session_end_time = asyncio.get_event_loop().time()
            
            # 确保当前连接关闭，以便下一次合成使用新连接
            ws_to_close = self.websocket # Store reference
            self.websocket = None # Set to None immediately
            if ws_to_close and not getattr(ws_to_close, 'closed', False):
                logger.info("[synthesize_stream] 尝试关闭WebSocket连接...")
                try:
                    await ws_to_close.close()
                    logger.debug("[synthesize_stream] WebSocket连接关闭成功。")
                except (OSError, RuntimeError, websockets.exceptions.ConnectionClosedError, websockets.exceptions.ConnectionClosedOK) as close_err:
                    logger.warning(f"[synthesize_stream] 关闭WebSocket时发生可忽略的错误: {type(close_err).__name__}: {close_err}")
                except Exception as e:
                    logger.error(f"[synthesize_stream] 关闭WebSocket时发生意外错误: {e}", exc_info=True)

        except Exception as e:
            logger.error(f"语音合成过程中发生错误: {e}")
            # Also apply the robust close logic in the except block
            ws_to_close_on_error = self.websocket
            self.websocket = None
            if ws_to_close_on_error and not getattr(ws_to_close_on_error, 'closed', False):
                logger.info("[synthesize_stream] 发生错误，尝试关闭WebSocket连接...")
                try:
                    await ws_to_close_on_error.close()
                    logger.debug("[synthesize_stream] 错误处理中，WebSocket连接关闭成功。")
                except (OSError, RuntimeError, websockets.exceptions.ConnectionClosedError, websockets.exceptions.ConnectionClosedOK) as close_err:
                    logger.warning(f"[synthesize_stream] 错误处理中关闭WebSocket时发生可忽略的错误: {type(close_err).__name__}: {close_err}")
                except Exception as close_e:
                     logger.error(f"[synthesize_stream] 错误处理中关闭WebSocket时发生意外错误: {close_e}", exc_info=True)
            raise e

    async def continuous_synthesize_stream(self, texts: list, speaker: Optional[str] = None) -> AsyncGenerator[bytes, None]:
        """连续合成多段文本为语音流，优化连接管理（异步生成器）"""
        if not texts:
            logger.warning("没有提供文本，无法进行合成")
            return
            
        try:
            # 建立一个连接用于整个合成过程
            await self.connect(force_reconnect=True)
            
            # 处理所有文本段落
            for i, text in enumerate(texts):
                if i > 0:
                    # 连续多段合成需要等待一小段时间，让上一个会话完全结束
                    # 避免两个会话过于接近导致服务端混淆
                    await asyncio.sleep(0.1)
                    
                # 为每段文本创建新会话
                current_session_start = asyncio.get_event_loop().time()
                await self.start_session(speaker)
                
                current_speaker = speaker if speaker is not None else self.config.default_speaker
                logger.debug(f"发送第 {i+1}/{len(texts)} 段合成请求: 会话ID={self.session_id}, 文本长度={len(text)}")
                
                # 发送文本合成任务请求
                await self.protocol.send_event(
                    self.websocket,
                    Header(
                        message_type=FULL_CLIENT_REQUEST,
                        message_type_flags=MSG_FLAG_WITH_EVENT,
                        serial_method=JSON_SERIALIZATION
                    ),
                    event=EVENT_TaskRequest,
                    session_id=self.session_id,
                    payload=self._get_tts_payload(text, current_speaker)
                )
                
                # 发送结束会话请求
                await self.protocol.send_event(
                    self.websocket,
                    Header(
                        message_type=FULL_CLIENT_REQUEST,
                        message_type_flags=MSG_FLAG_WITH_EVENT,
                        serial_method=JSON_SERIALIZATION
                    ),
                    event=EVENT_FinishSession,
                    session_id=self.session_id,
                    payload="{}"
                )
                
                # 接收当前段落的所有音频数据
                audio_chunks_received = 0
                current_session_finished = False
                
                while not current_session_finished:
                    try:
                        response = await self.protocol.receive_response(self.websocket)
                        
                        # 音频数据
                        if "audio_data" in response:
                            audio_chunks_received += 1
                            yield response["audio_data"]
                            
                        # 会话结束事件
                        elif response.get("event") == EVENT_SessionFinished:
                            logger.debug(f"第 {i+1} 段会话已完成，共接收 {audio_chunks_received} 个音频块")
                            current_session_finished = True
                            
                        # 会话失败事件
                        elif response.get("event") == EVENT_SessionFailed:
                            error_msg = response.get("payload", {})
                            logger.error(f"第 {i+1} 段会话失败: {error_msg}")
                            break
                            
                        # 错误信息事件
                        elif response.get("event") == EVENT_ErrMsg:
                            error_msg = response.get("errMsg", "未知错误")
                            error_code = response.get("errCode", "未知错误码")
                            logger.error(f"第 {i+1} 段合成过程中出现错误: 代码={error_code}, 信息={error_msg}")
                            break
                            
                        # 其他事件类型
                        elif response.get("event") not in [None, EVENT_TTSResponse, EVENT_TTSSentenceStart, EVENT_TTSSentenceEnd]:
                            logger.debug(f"第 {i+1} 段收到事件: {response.get('event')}")
                    
                    except websockets.exceptions.ConnectionClosed as e:
                        logger.error(f"第 {i+1} 段接收音频数据时连接断开: {e}")
                        # 尝试重新连接
                        await self.connect(force_reconnect=True)
                        break
                        
                    except Exception as e:
                        logger.error(f"第 {i+1} 段接收音频数据时出错: {e}")
                        break
                
                # 记录会话结束时间
                self._last_session_end_time = asyncio.get_event_loop().time()
            
            # 所有段落处理完毕后，关闭连接
            if self.websocket and not getattr(self.websocket, 'closed', False):
                logger.info("连续合成完成，关闭WebSocket连接...")
                await self.websocket.close()
                self.websocket = None
                
        except Exception as e:
            logger.error(f"连续语音合成过程中发生错误: {e}")
            if self.websocket and not getattr(self.websocket, 'closed', False):
                await self.websocket.close()
                self.websocket = None
            raise e

    async def synthesize_text(self, text: str, speaker: Optional[str] = None) -> bytes:
        """合成文本为语音 (非流式, 内部调用流式实现)，包含重试机制"""
        return await self.retry_with_backoff(self._synthesize_text, text, speaker)
        
    async def _synthesize_text(self, text: str, speaker: Optional[str] = None) -> bytes:
        """内部实现：合成文本为完整的音频数据"""
        audio_data = bytearray()
        
        async for chunk in self.synthesize_stream(text, speaker):
            audio_data.extend(chunk)
        
        return bytes(audio_data)

    async def retry_with_backoff(self, operation, *args, max_retries=2, initial_delay=1.0, **kwargs):
        """使用指数退避策略重试操作"""
        delay = initial_delay
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                return await operation(*args, **kwargs)
            except (websockets.exceptions.ConnectionClosed, ConnectionError) as e:
                last_error = e
                if attempt < max_retries:
                    # 添加随机抖动，避免同时重试
                    jitter = random.uniform(0.1, 0.3) * delay
                    wait_time = delay + jitter
                    logger.warning(f"操作失败 ({attempt+1}/{max_retries+1})，将在 {wait_time:.2f} 秒后重试: {e}")
                    
                    # 重置连接
                    self.websocket = None
                    self.session_id = None
                    
                    # 等待后重试
                    await asyncio.sleep(wait_time)
                    delay *= 2  # 指数增长
        
        # 所有重试都失败
        logger.error(f"{max_retries+1}次尝试后操作仍然失败: {last_error}")
        raise last_error

    async def close(self):
        """关闭连接 (优化版)"""
        ws = self.websocket
        if ws:
            try:
                # 检查事件循环是否已关闭，若已关闭则跳过关闭操作
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_closed():
                        logger.warning("[TTSClient.close] 事件循环已关闭，跳过WebSocket关闭。")
                        self.websocket = None
                        self.session_id = None
                        return
                except RuntimeError:
                    # 没有事件循环，直接跳过
                    logger.warning("[TTSClient.close] 无法获取事件循环，跳过WebSocket关闭。")
                    self.websocket = None
                    self.session_id = None
                    return

                logger.info("[TTSClient.close] 尝试关闭WebSocket连接...")
                if not getattr(ws, 'closed', False):
                    try:
                        await asyncio.wait_for(ws.close(), timeout=3.0)
                        logger.debug("[TTSClient.close] WebSocket关闭握手完成")
                    except Exception as e:
                        logger.warning(f"[TTSClient.close] WebSocket关闭时异常: {e}")
                else:
                    logger.debug("[TTSClient.close] WebSocket连接已关闭，无需再次关闭。")

            except Exception as close_err:
                import traceback
                logger.warning(f"[TTSClient.close] 关闭WebSocket时发生异常: {type(close_err).__name__}: {close_err}")
                logger.debug(traceback.format_exc())
            finally:
                self.websocket = None
                self.session_id = None
        else:
            logger.debug("[TTSClient.close] No active WebSocket connection to close.")
            self.websocket = None
            self.session_id = None

    async def synthesize_stream_bidi(
        self,
        text_queue: asyncio.Queue,
        speaker: Optional[str] = None
    ) -> AsyncGenerator[bytes, None]:
        """
        边发边收的流式TTS合成方法，适配火山双向流式API。
        :param text_queue: asyncio.Queue，LLM流式输出的文本段，每段str，最后以None结尾
        :param speaker: 可选音色
        :yield: 音频数据块
        """
        await self.connect(force_reconnect=True)
        await self.start_session(speaker)
        session_finished = False
        send_task_done = asyncio.Event()

        async def send_texts():
            while True:
                text = await text_queue.get()
                if text is None:
                    # 发送 finish session
                    await self.protocol.send_event(
                        self.websocket,
                        Header(
                            message_type=FULL_CLIENT_REQUEST,
                            message_type_flags=MSG_FLAG_WITH_EVENT,
                            serial_method=JSON_SERIALIZATION
                        ),
                        event=EVENT_FinishSession,
                        session_id=self.session_id,
                        payload="{}"
                    )
                    send_task_done.set()
                    break
                # 发送 task request
                await self.protocol.send_event(
                    self.websocket,
                    Header(
                        message_type=FULL_CLIENT_REQUEST,
                        message_type_flags=MSG_FLAG_WITH_EVENT,
                        serial_method=JSON_SERIALIZATION
                    ),
                    event=EVENT_TaskRequest,
                    session_id=self.session_id,
                    payload=self._get_tts_payload(text, speaker)
                )

        async def recv_audio():
            nonlocal session_finished
            while not session_finished:
                try:
                    response = await self.protocol.receive_response(self.websocket)
                    # 音频数据
                    if "audio_data" in response:
                        yield response["audio_data"]
                    # 会话结束
                    elif response.get("event") == EVENT_SessionFinished:
                        session_finished = True
                        break
                    # 会话失败
                    elif response.get("event") == EVENT_SessionFailed:
                        logger.error(f"TTS会话失败: {response.get('payload', {})}")
                        session_finished = True
                        break
                    # 错误信息
                    elif response.get("event") == EVENT_ErrMsg:
                        logger.error(f"TTS合成错误: {response.get('errMsg', '未知错误')}")
                        session_finished = True
                        break
                except Exception as e:
                    logger.error(f"TTS音频接收异常: {e}")
                    session_finished = True
                    break

        # 启动发送和接收
        send_task = asyncio.create_task(send_texts())
        try:
            async for audio_chunk in recv_audio():
                yield audio_chunk
            # 确保发送任务结束
            await send_task
        finally:
            # 关闭连接
            ws_to_close = self.websocket
            self.websocket = None
            if ws_to_close and not getattr(ws_to_close, 'closed', False):
                try:
                    await ws_to_close.close()
                except Exception:
                    pass