import sounddevice as sd

print("Available audio devices:")
print(sd.query_devices())

print("\nDefault input device:")
print(sd.query_devices(sd.default.device[0]))

# 您可以取消注释以下行来尝试通过名称查询
# target_device_name = "UGREEN" # 或者 "CM564" 或者从 query_devices() 输出中看到的完整名称
# try:
#     print(f"\nQuerying for input device containing '{target_device_name}':")
#     specific_device = sd.query_devices(target_device_name, kind='input')
#     if specific_device:
#         print(specific_device)
#         # 尝试获取更详细的设备信息，特别是支持的采样率
#         # device_info = sd.query_devices(specific_device['name']) # 使用完整的设备名
#         # print(f"Details for {specific_device['name']}: {device_info}")
#     else:
#         print(f"No input device found containing '{target_device_name}'.")
# except Exception as e:
#     print(f"Error querying for device '{target_device_name}': {e}")

# 列出所有输入设备及其支持的采样率 (如果API支持)
# print("\nDetailed input device capabilities:")
# for i, device in enumerate(sd.query_devices()):
#     if device['max_input_channels'] > 0:
#         print(f"Device #{i}: {device['name']}")
#         try:
#             # 尝试查询详细信息，包括支持的采样率，但这不一定直接可用
#             # 对于特定的设备，可能需要更具体的查询或尝试打开不同采样率的流
#             print(f"  Default samplerate: {device['default_samplerate']}")
#             # Sounddevice 本身不直接提供一个函数来列出所有支持的采样率
#             # 通常需要尝试打开流来测试
#         except Exception as e:
#             print(f"  Could not get detailed info for {device['name']}: {e}")