#!/usr/bin/env python3
"""
测试音频设备管理器的功能
"""

import asyncio
import logging
import time
from utils.audio_device_manager import audio_device_manager, AudioDeviceType
from utils.function_call.function_tools_impl import MusicPlayer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_device_manager():
    """测试设备管理器的基本功能"""
    print("=== 测试音频设备管理器 ===")
    
    # 测试1: 检查初始状态
    print(f"初始状态 - 当前用户: {audio_device_manager.get_current_user()}")
    print(f"设备是否可用: {audio_device_manager.is_device_available()}")
    
    # 测试2: 请求TTS设备访问权限
    print("\n--- 测试TTS设备访问 ---")
    success = audio_device_manager.request_device_access_sync(
        AudioDeviceType.TTS_PLAYER,
        {"test": "tts_test"}
    )
    print(f"TTS请求设备访问: {success}")
    print(f"当前用户: {audio_device_manager.get_current_user()}")
    
    # 测试3: 音乐播放器请求访问（应该会停止TTS）
    print("\n--- 测试音乐播放器设备访问 ---")
    
    # 创建音乐播放器实例
    music_player = MusicPlayer()
    
    # 模拟TTS停止回调
    def mock_tts_stop():
        print("模拟TTS播放器被停止")
    
    audio_device_manager.register_stop_callback(AudioDeviceType.TTS_PLAYER, mock_tts_stop)
    
    success = audio_device_manager.request_device_access_sync(
        AudioDeviceType.MUSIC_PLAYER,
        {"test": "music_test"}
    )
    print(f"音乐播放器请求设备访问: {success}")
    print(f"当前用户: {audio_device_manager.get_current_user()}")
    
    # 测试4: 释放设备访问权限
    print("\n--- 测试释放设备访问权限 ---")
    audio_device_manager.release_device_access_sync(AudioDeviceType.MUSIC_PLAYER)
    print(f"释放后当前用户: {audio_device_manager.get_current_user()}")
    print(f"设备是否可用: {audio_device_manager.is_device_available()}")
    
    print("\n=== 设备管理器测试完成 ===")

def test_music_player():
    """测试音乐播放器的设备访问功能"""
    print("\n=== 测试音乐播放器 ===")
    
    music_player = MusicPlayer()
    
    # 获取音乐列表
    music_list = music_player.list_music()
    print(f"可用音乐: {music_list}")
    
    if music_list:
        # 尝试播放第一首音乐
        result = music_player.play_music(music_list[0])
        print(f"播放结果: {result}")
        
        # 等待一段时间
        print("等待3秒...")
        time.sleep(3)
        
        # 停止音乐
        result = music_player.stop_music()
        print(f"停止结果: {result}")
    else:
        print("没有找到可播放的音乐文件")
    
    print("=== 音乐播放器测试完成 ===")

if __name__ == "__main__":
    try:
        # 测试设备管理器
        test_device_manager()
        
        # 测试音乐播放器
        test_music_player()
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}", exc_info=True)
