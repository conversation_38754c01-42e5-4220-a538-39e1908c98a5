#!/usr/bin/env python3
"""
测试音频混合播放功能
"""

import asyncio
import logging
import time
import threading
from utils.audio_device_manager import audio_device_manager, AudioDeviceType
from utils.function_call.function_tools_impl import MusicPlayer
from utils.audio_new_player import AudioNewPlayer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mixed_audio_playback():
    """测试音乐和TTS混合播放"""
    print("=== 测试音频混合播放功能 ===")
    
    # 创建音乐播放器
    music_player = MusicPlayer()
    
    # 创建TTS播放器
    tts_player = AudioNewPlayer(device=None, buffer_size=150, min_prebuffer=15)
    
    # 获取音乐列表
    music_list = music_player.list_music()
    print(f"可用音乐: {music_list}")
    
    if not music_list:
        print("没有找到可播放的音乐文件，跳过测试")
        return
    
    try:
        # 1. 开始播放音乐
        print("\n--- 步骤1: 开始播放音乐 ---")
        result = music_player.play_music(music_list[0])
        print(f"音乐播放结果: {result}")
        
        # 检查设备状态
        active_users = audio_device_manager.get_active_users()
        print(f"当前活跃用户: {[user.value for user in active_users]}")
        
        # 等待音乐开始播放
        print("等待3秒让音乐开始播放...")
        time.sleep(3)
        
        # 2. 模拟TTS播放
        print("\n--- 步骤2: 开始TTS播放（应该降低音乐音量） ---")
        
        # 请求TTS设备访问
        access_granted = audio_device_manager.request_device_access_sync(
            AudioDeviceType.TTS_PLAYER,
            {"test": "tts_simulation"}
        )
        print(f"TTS请求设备访问: {access_granted}")
        
        # 检查设备状态
        active_users = audio_device_manager.get_active_users()
        print(f"当前活跃用户: {[user.value for user in active_users]}")
        
        # 模拟TTS播放时间
        print("模拟TTS播放5秒...")
        time.sleep(5)
        
        # 3. TTS播放结束
        print("\n--- 步骤3: TTS播放结束（应该恢复音乐音量） ---")
        audio_device_manager.release_device_access_sync(AudioDeviceType.TTS_PLAYER)
        
        # 检查设备状态
        active_users = audio_device_manager.get_active_users()
        print(f"当前活跃用户: {[user.value for user in active_users]}")
        
        # 等待一段时间观察音乐继续播放
        print("等待3秒观察音乐继续播放...")
        time.sleep(3)
        
        # 4. 停止音乐
        print("\n--- 步骤4: 停止音乐播放 ---")
        result = music_player.stop_music()
        print(f"停止音乐结果: {result}")
        
        # 检查最终状态
        active_users = audio_device_manager.get_active_users()
        print(f"最终活跃用户: {[user.value for user in active_users]}")
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}", exc_info=True)
    
    print("=== 混合播放测试完成 ===")

def test_device_manager_state():
    """测试设备管理器状态管理"""
    print("\n=== 测试设备管理器状态 ===")
    
    # 初始状态
    print(f"初始活跃用户: {[user.value for user in audio_device_manager.get_active_users()]}")
    print(f"设备是否可用: {audio_device_manager.is_device_available()}")
    
    # 添加多个用户
    audio_device_manager.request_device_access_sync(AudioDeviceType.TTS_PLAYER)
    audio_device_manager.request_device_access_sync(AudioDeviceType.MUSIC_PLAYER)
    
    print(f"添加用户后: {[user.value for user in audio_device_manager.get_active_users()]}")
    
    # 检查特定用户状态
    print(f"TTS用户是否活跃: {audio_device_manager.is_user_active(AudioDeviceType.TTS_PLAYER)}")
    print(f"音乐用户是否活跃: {audio_device_manager.is_user_active(AudioDeviceType.MUSIC_PLAYER)}")
    
    # 释放用户
    audio_device_manager.release_device_access_sync(AudioDeviceType.TTS_PLAYER)
    print(f"释放TTS后: {[user.value for user in audio_device_manager.get_active_users()]}")
    
    audio_device_manager.release_device_access_sync(AudioDeviceType.MUSIC_PLAYER)
    print(f"释放音乐后: {[user.value for user in audio_device_manager.get_active_users()]}")
    
    print("=== 设备管理器状态测试完成 ===")

if __name__ == "__main__":
    try:
        # 测试设备管理器状态
        test_device_manager_state()
        
        # 测试混合播放
        test_mixed_audio_playback()
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}", exc_info=True)
