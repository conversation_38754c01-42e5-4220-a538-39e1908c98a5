# 音频测试模块

这个目录包含了所有与音频功能相关的测试脚本。

## 文件说明

### 设备测试

- **`check_devices.py`** - 基础设备检测
- **`checkAudioDeviceSampleRates.py`** - 音频设备采样率检测
- **`check_audio_device_capabilities.py`** - 音频设备能力检测
- **`test_audio_devices.py`** - 音频设备功能测试

### 播放器测试

- **`test_audio_player_new.py`** - AudioNewPlayer测试
- **`test_mixed_audio.py`** - 混合音频播放测试
- **`test_audio_sharing.py`** - 音频设备共享测试

### 功能测试

- **`test_device_manager.py`** - 音频设备管理器测试
- **`test_volume_control.py`** - 音量控制测试

## 使用方法

### 运行单个测试
```bash
cd Backend
python test/audio_tests/test_audio_devices.py
```

### 运行所有音频测试
```bash
cd Backend
python -m pytest test/audio_tests/ -v
```

### 测试音频播放器
```bash
cd Backend
python test/audio_tests/test_audio_player_new.py
```

### 测试设备共享
```bash
cd Backend
python test/audio_tests/test_audio_sharing.py
```

## 测试环境要求

- 确保音频设备已连接
- PulseAudio正常运行
- 有测试音频文件（在resource目录中）

## 相关工具

如果测试失败，可以使用以下工具诊断：
- `../utils/audio_config/check_audio_devices.py` - 检测设备状态
- `../utils/audio_config/fix_audio_routing.py` - 修复音频路由
- `../scripts/audio_tools/quick_audio_check.sh` - 快速检测
