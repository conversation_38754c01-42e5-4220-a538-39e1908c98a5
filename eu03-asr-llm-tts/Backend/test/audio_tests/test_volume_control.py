import asyncio
import logging
import sys
import os
import time
from typing import Optional
from pydub import AudioSegment
from pydub.exceptions import CouldntDecodeError

# Adjust import paths to find the main project modules
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from utils.audio_new_player import AudioNewPlayer, PlayerState, AudioPlayerError

# --- Configuration ---
logging.basicConfig(
    level=logging.INFO, # Use INFO for general test, DEBUG for detailed player logs
    format='%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
)
logger = logging.getLogger("VOLUME_TEST")
# Set AudioNewPlayer logger to DEBUG if needed
# logging.getLogger("AudioNewPlayer").setLevel(logging.DEBUG)


# --- Helper Function to Load Audio ---
def load_audio_file(filepath: str) -> tuple[Optional[bytes], Optional[int], Optional[int]]:
    """Loads an audio file using pydub and returns raw data, sample rate, channels."""
    try:
        logger.info(f"Loading audio file: {filepath}")
        if not os.path.exists(filepath):
            logger.error(f"Audio file not found: {filepath}")
            return None, None, None
            
        audio = AudioSegment.from_file(filepath)
        
        # Ensure audio is PCM int16 for the player
        # pydub usually handles this, but we force it for safety
        audio = audio.set_sample_width(2) # 2 bytes = 16 bits
        
        raw_data = audio.raw_data
        sample_rate = audio.frame_rate
        channels = audio.channels
        logger.info(f"Loaded {filepath}: {len(raw_data)} bytes, {sample_rate} Hz, {channels} channels, int16")
        return raw_data, sample_rate, channels
        
    except CouldntDecodeError:
        logger.error(f"Could not decode audio file: {filepath}. Is ffmpeg installed and in PATH?")
        return None, None, None
    except Exception as e:
        logger.error(f"Error loading audio file {filepath}: {e}", exc_info=True)
        return None, None, None

# --- Main Test Function ---
async def test_volume_adjustment(player: AudioNewPlayer, audio_data: bytes, sample_rate: int, channels: int, file_label: str):
    """Plays audio data while adjusting volume."""
    logger.info(f"--- 开始测试音量调节: {file_label} ---")
    
    playback_task = None
    try:
        # 设置初始音量为1.0（最大）
        player.set_volume(1.0)
        logger.info(f"正在播放 {file_label}，初始音量 1.00...")

        # 将音频数据分块以便更好地测试
        async def chunk_generator():
            # 分成大约20个块
            chunk_size = len(audio_data) // 20
            if chunk_size == 0 or chunk_size % 2 != 0:
                # 确保块大小是偶数（对于int16样本）
                chunk_size = (len(audio_data) // 2) * 2
                if chunk_size == 0:
                    chunk_size = len(audio_data)
                    if chunk_size % 2 != 0:
                        chunk_size -= 1  # 确保是偶数
            
            # 如果文件太小，不分块
            if chunk_size < 1024:
                if len(audio_data) > 0:
                    yield audio_data
                return
            
            # 否则分块发送
            for i in range(0, len(audio_data), chunk_size):
                end = min(i + chunk_size, len(audio_data))
                # 确保块长度是偶数
                if (end - i) % 2 != 0:
                    end -= 1
                if i < end:
                    yield audio_data[i:end]
                    # 适当延迟以模拟流式传输
                    await asyncio.sleep(0.05)
        
        # 使用play_stream而不是play
        playback_task = asyncio.create_task(
            player.play_stream(chunk_generator(), sample_rate, channels),
            name=f"Playback-{file_label}"
        )
        
        # 给播放一点时间开始 - 正常音量
        await asyncio.sleep(5.0)  # 调整为5秒
        
        # 按照用户要求的音量变化趋势设置
        volume_tests = [
            (0.2, "低音量 (0.2)", 5.0),       # 0.2音量持续5秒
            (0.8, "中高音量 (0.8)", 5.0),     # 0.8音量持续5秒
            (0.0, "静音 (0.0)", 5.0),        # 静音持续5秒
            (1.0, "恢复标准音量 (1.0)", 5.0)   # 最后恢复标准音量持续5秒
        ]

        for volume, desc, duration in volume_tests:
            if not playback_task or playback_task.done():
                logger.info("播放在完成音量测试前结束。")
                break
                
            # 设置新音量
            logger.info(f">>> 设置为{desc}")
            player.set_volume(volume)
            
            # 等待指定时间以便听清音量变化
            await asyncio.sleep(duration)

        # Wait for the playback task to complete naturally if it hasn't already
        if playback_task and not playback_task.done():
            logger.info("等待播放完成...")
            await playback_task
        
        logger.info(f"--- {file_label} 音量测试完成 ---")

    except AudioPlayerError as e:
        logger.error(f"AudioPlayerError during {file_label} test: {e}")
    except Exception as e:
        logger.error(f"Unexpected error during {file_label} test: {e}", exc_info=True)
    finally:
        # Ensure the task is awaited or cancelled if it exists and isn't done
        if playback_task and not playback_task.done():
            logger.warning(f"Playback task for {file_label} still running after test logic, cancelling.")
            playback_task.cancel()
            try:
                await asyncio.wait_for(playback_task, timeout=1.0)
            except asyncio.TimeoutError: pass
            except asyncio.CancelledError: pass
            except Exception: pass
        # Reset volume to default after test
        logger.info("Resetting volume to 1.0")
        player.set_volume(1.0)

async def main():
    # Define audio file paths relative to this script's location
    script_dir = os.path.dirname(__file__)
    wav_path = os.path.join(script_dir, "resource", "test_output_1.wav")
    mp3_path = os.path.join(script_dir, "resource", "彩虹.mp3")

    # Load audio files
    wav_data, wav_sr, wav_ch = load_audio_file(wav_path)
    mp3_data, mp3_sr, mp3_ch = load_audio_file(mp3_path)

    if wav_data is None and mp3_data is None:
        logger.error("No audio files could be loaded. Exiting.")
        return

    # Initialize player
    player = AudioNewPlayer()

    # Test WAV file
    if wav_data:
        await test_volume_adjustment(player, wav_data, wav_sr, wav_ch, "WAV")
        await asyncio.sleep(1) # Pause between tests

    # Test MP3 file
    if mp3_data:
        await test_volume_adjustment(player, mp3_data, mp3_sr, mp3_ch, "MP3")

    logger.info("All volume tests completed.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Test interrupted by user.")
    except Exception as e:
        logger.critical(f"Unhandled exception in main: {e}", exc_info=True) 