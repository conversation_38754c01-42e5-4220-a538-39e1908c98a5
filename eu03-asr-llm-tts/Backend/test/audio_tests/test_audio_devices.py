#!/usr/bin/env python3
"""
音频设备测试脚本
用于验证设备配置和播放功能
"""

import sounddevice as sd
import numpy as np
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.audio_new_player import AudioNewPlayer

def list_audio_devices():
    """列出所有音频设备"""
    print("=== 所有音频设备 ===")
    devices = sd.query_devices()
    
    for i, device in enumerate(devices):
        device_type = []
        if device['max_input_channels'] > 0:
            device_type.append(f"输入({device['max_input_channels']})")
        if device['max_output_channels'] > 0:
            device_type.append(f"输出({device['max_output_channels']})")
        
        type_str = ", ".join(device_type) if device_type else "无"
        print(f"设备 {i}: {device['name']}")
        print(f"  类型: {type_str}")
        print(f"  默认采样率: {device['default_samplerate']} Hz")
        
        # 标记目标设备
        if 'Y1076' in device['name'] or 'Yundea' in device['name']:
            print(f"  *** 这是Yundea 1076设备 ***")
        if 'UGREEN' in device['name'] or 'CM564' in device['name']:
            print(f"  *** 这是UGREEN麦克风设备 ***")
        print()

def test_device_selection():
    """测试设备选择功能"""
    print("=== 测试设备选择 ===")
    
    # 测试关键词选择
    player = AudioNewPlayer(device_keyword="Yundea 1076")
    print(f"使用关键词 'Yundea 1076' 选择的设备: {player.device}")
    
    # 测试生成测试音频
    duration = 2.0  # 2秒
    sample_rate = 48000
    frequency = 440  # A4音符
    
    print(f"\n生成测试音频: {frequency}Hz, {duration}秒, {sample_rate}Hz采样率")
    
    # 生成正弦波
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(2 * np.pi * frequency * t) * 0.3  # 30%音量
    audio_data = (audio_data * 32767).astype(np.int16)  # 转换为16位整数

    # AudioNewPlayer需要字节数据
    audio_bytes = audio_data.tobytes()
    
    return player, audio_bytes, sample_rate

async def test_audio_playback():
    """测试音频播放"""
    print("=== 测试音频播放 ===")
    
    try:
        player, audio_bytes, sample_rate = test_device_selection()

        print("开始播放测试音频...")
        await player.play(audio_bytes, sample_rate, channels=1)
        print("播放完成!")
        
        return True
        
    except Exception as e:
        print(f"播放测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_device_keyword_matching():
    """测试设备关键词匹配"""
    print("=== 测试设备关键词匹配 ===")
    
    test_keywords = [
        "Yundea 1076",
        "Y1076", 
        "USB Audio",
        "Yundea",
        "1076"
    ]
    
    for keyword in test_keywords:
        try:
            player = AudioNewPlayer(device_keyword=keyword)
            print(f"关键词 '{keyword}' -> 设备: {player.device}")
        except Exception as e:
            print(f"关键词 '{keyword}' -> 失败: {e}")

if __name__ == "__main__":
    print("音频设备测试脚本")
    print("=" * 50)
    
    # 1. 列出所有设备
    list_audio_devices()
    
    # 2. 测试设备关键词匹配
    test_device_keyword_matching()
    
    # 3. 测试音频播放
    import asyncio
    
    print("\n准备测试音频播放...")
    print("注意: 将播放2秒的440Hz测试音频")
    input("按Enter键继续...")
    
    success = asyncio.run(test_audio_playback())
    
    if success:
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 测试失败，请检查设备配置")
