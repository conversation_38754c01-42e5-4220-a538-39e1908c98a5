#!/usr/bin/env python3
"""
RDKX5音频问题诊断脚本
检查音频设备状态、音量设置、PulseAudio配置等
"""

import asyncio
import logging
import subprocess
import sys
import os
import numpy as np
import sounddevice as sd
from pathlib import Path

# 添加Backend目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from utils.audio_new_player import AudioNewPlayer
from utils.audio_device_manager import AudioDeviceManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_command(cmd):
    """运行系统命令并返回输出"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.stdout.strip(), result.stderr.strip(), result.returncode
    except Exception as e:
        return "", str(e), -1

def check_pulseaudio_status():
    """检查PulseAudio状态"""
    print("=== PulseAudio状态检查 ===")
    
    # 检查PulseAudio是否运行
    stdout, stderr, code = run_command("pulseaudio --check -v")
    if code == 0:
        print("✅ PulseAudio正在运行")
    else:
        print("❌ PulseAudio未运行或有问题")
        print(f"错误: {stderr}")
    
    # 检查默认设备
    stdout, stderr, code = run_command("pactl info | grep 'Default Sink'")
    if code == 0:
        print(f"🔊 默认输出设备: {stdout}")
    else:
        print("❌ 无法获取默认输出设备")
    
    # 列出所有输出设备
    print("\n📋 可用输出设备:")
    stdout, stderr, code = run_command("pactl list short sinks")
    if code == 0:
        for line in stdout.split('\n'):
            if line.strip():
                print(f"  - {line}")
    else:
        print("❌ 无法列出输出设备")

def check_alsa_status():
    """检查ALSA状态"""
    print("\n=== ALSA状态检查 ===")
    
    # 检查ALSA播放设备
    stdout, stderr, code = run_command("aplay -l")
    if code == 0:
        print("📋 ALSA播放设备:")
        print(stdout)
    else:
        print("❌ 无法列出ALSA播放设备")
    
    # 检查音量控制
    stdout, stderr, code = run_command("amixer scontrols")
    if code == 0:
        print("\n🎚️ 可用音量控制:")
        for line in stdout.split('\n')[:5]:  # 只显示前5个
            if line.strip():
                print(f"  - {line}")
    else:
        print("❌ 无法列出音量控制")

def check_sounddevice_devices():
    """检查SoundDevice设备"""
    print("\n=== SoundDevice设备检查 ===")
    
    try:
        devices = sd.query_devices()
        print("📋 SoundDevice可用设备:")
        for i, device in enumerate(devices):
            if device['max_output_channels'] > 0:
                print(f"  设备 {i}: {device['name']} (输出通道: {device['max_output_channels']})")
        
        # 检查默认设备
        default_device = sd.default.device
        print(f"\n🔊 SoundDevice默认设备: {default_device}")
        
        # 查找pulse设备
        pulse_devices = [i for i, dev in enumerate(devices) if 'pulse' in dev['name'].lower()]
        if pulse_devices:
            print(f"🎵 找到Pulse设备: {pulse_devices}")
        else:
            print("❌ 未找到Pulse设备")
            
    except Exception as e:
        print(f"❌ SoundDevice检查失败: {e}")

async def test_audio_playback():
    """测试音频播放"""
    print("\n=== 音频播放测试 ===")
    
    try:
        # 创建测试音频播放器
        player = AudioNewPlayer(device_keyword="pulse", volume=0.8)
        print("✅ AudioNewPlayer创建成功")
        
        # 生成测试音频（440Hz正弦波，2秒）
        sample_rate = 48000
        duration = 2.0
        frequency = 440
        
        t = np.linspace(0, duration, int(sample_rate * duration), endpoint=False)
        sine_wave = (0.3 * np.sin(2 * np.pi * frequency * t) * 32767).astype(np.int16)
        audio_data = sine_wave.tobytes()
        
        print(f"🎵 播放测试音频: {frequency}Hz正弦波, {duration}秒")
        print("   如果听不到声音，说明音频输出有问题")
        
        # 播放测试音频
        await player.play(audio_data, sample_rate, channels=1)
        print("✅ 音频播放完成")
        
    except Exception as e:
        print(f"❌ 音频播放测试失败: {e}")
        import traceback
        traceback.print_exc()

def check_system_volume():
    """检查系统音量设置"""
    print("\n=== 系统音量检查 ===")
    
    # 检查PulseAudio音量
    stdout, stderr, code = run_command("pactl list sinks | grep -A 15 'State: RUNNING'")
    if code == 0:
        print("🔊 活跃输出设备状态:")
        print(stdout)
    
    # 检查ALSA音量
    stdout, stderr, code = run_command("amixer get PCM")
    if code == 0:
        print("\n🎚️ PCM音量设置:")
        print(stdout)
    else:
        # 尝试其他常见控制
        for control in ['Master', 'Speaker', 'Headphone']:
            stdout, stderr, code = run_command(f"amixer get {control}")
            if code == 0:
                print(f"\n🎚️ {control}音量设置:")
                print(stdout)
                break

def suggest_fixes():
    """提供修复建议"""
    print("\n=== 修复建议 ===")
    
    print("如果音频播放测试失败，请尝试以下步骤：")
    print()
    print("1. 检查PulseAudio默认设备:")
    print("   pactl list short sinks")
    print("   pactl set-default-sink <设备名称>")
    print()
    print("2. 检查音量设置:")
    print("   amixer set PCM 100%")
    print("   pactl set-sink-volume @DEFAULT_SINK@ 100%")
    print()
    print("3. 重启PulseAudio:")
    print("   pulseaudio -k")
    print("   pulseaudio --start")
    print()
    print("4. 检查音频线缆连接")
    print("5. 检查硬件音量开关")

async def main():
    """主诊断流程"""
    print("🔍 RDKX5音频问题诊断开始...")
    print("=" * 50)
    
    # 系统检查
    check_pulseaudio_status()
    check_alsa_status()
    check_sounddevice_devices()
    check_system_volume()
    
    # 音频播放测试
    await test_audio_playback()
    
    # 修复建议
    suggest_fixes()
    
    print("\n" + "=" * 50)
    print("🔍 诊断完成")

if __name__ == "__main__":
    asyncio.run(main())
