import sounddevice as sd

device_index_or_name = 0 # 或者 "hw:0,0" 请根据您的情况选择
common_rates = [8000, 11025, 16000, 22050, 32000, 44100, 48000, 96000]

print(f"查询设备: {device_index_or_name}")
try:
    device_info = sd.query_devices(device_index_or_name)
    print(f"设备名称: {device_info['name']}")
    print(f"默认输出采样率: {device_info['default_samplerate']}")
except Exception as e:
    print(f"查询设备信息失败: {e}")
    exit()

print("\n尝试支持的输出采样率:")
for rate in common_rates:
    try:
        sd.check_output_settings(device=device_index_or_name, samplerate=rate, channels=1)
        print(f"  {rate} Hz: 支持")
    except Exception as e:
        # print(f"  {rate} Hz: 不支持 ({e})") # 详细错误可选
        pass