import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

import asyncio
import json
from llm.client import LLMClient
from utils.config_loader import load_config
from utils.function_call.function_schema import get_all_schemas
from utils.function_call.function_call_tools import FunctionRegistry

async def main():
    # 加载 LLM 配置
    config_loader = load_config()
    llm_config_dict = config_loader.get_config().get('llm', {})
    llm = LLMClient(llm_config_dict)
    tools = get_all_schemas()

    print("LLM 流式交互测试 (输入 exit 退出)")
    while True:
        try:
            user_input = input("你: ").strip()
        except (EOFError, KeyboardInterrupt):
            print("\n退出.")
            break
        if user_input.lower() in ("exit", "quit", "q"): break
        if not user_input:
            continue
        messages = [{"role": "user", "content": user_input}]
        print("LLM 回复:", flush=True)
        try:
            # 1. 首次请求，带上 tools
            tool_calls_buffer = {}
            content_buffer = ""
            finish_reason = None
            assistant_message = None
            tool_calls_list = []
            async for chunk in llm.stream_chat(messages=messages, tools=tools):
                # 输出内容
                if chunk.get("content"):
                    print(chunk["content"], end="", flush=True)
                    content_buffer += chunk["content"] or ""
                # 处理 tool_calls
                for tool_call in chunk.get("tool_calls") or []:
                    idx = tool_call.get("index")
                    if idx is not None:
                        if idx not in tool_calls_buffer:
                            tool_calls_buffer[idx] = tool_call.copy()
                            # 确保 arguments 字段存在
                            if "function" in tool_calls_buffer[idx] and "arguments" not in tool_calls_buffer[idx]["function"]:
                                tool_calls_buffer[idx]["function"]["arguments"] = ""
                        else:
                            # 拼接 arguments
                            tool_calls_buffer[idx]["function"]["arguments"] += tool_call["function"].get("arguments", "")
                finish_reason = chunk.get("finish_reason")
                # 保存 assistant 消息（只保存第一次 tool_calls 出现时的完整 assistant）
                if chunk.get("tool_calls"):
                    # 构造 assistant 消息
                    # tool_calls 需为 list，且每个元素包含 id/type/function
                    tool_calls_list = []
                    for idx, tool_call in tool_calls_buffer.items():
                        tool_calls_list.append({
                            "id": tool_call.get("id"),
                            "type": tool_call.get("type"),
                            "function": tool_call.get("function")
                        })
                    assistant_message = {
                        "role": "assistant",
                        "content": None,
                        "tool_calls": tool_calls_list
                    }
            # 2. 如果 finish_reason == 'tool_calls'，自动调用本地函数
            if finish_reason == "tool_calls" and tool_calls_buffer and assistant_message:
                tool_results = []
                # 补全 assistant 消息
                messages.append(assistant_message)
                for idx, tool_call in tool_calls_buffer.items():
                    func_name = tool_call["function"]["name"]
                    arguments_str = tool_call["function"].get("arguments", "{}")
                    try:
                        arguments = json.loads(arguments_str) if arguments_str else {}
                    except Exception as e:
                        arguments = {}
                    try:
                        result = await FunctionRegistry.call(func_name, **arguments)
                    except Exception as e:
                        result = f"[本地函数调用异常] {e}"
                    tool_call_id = tool_call.get("id")
                    if not tool_call_id:
                        tool_call_id = f"call_{idx}"
                    tool_message = {
                        "role": "tool",
                        "tool_call_id": tool_call_id,
                        "content": str(result)
                    }
                    messages.append(tool_message)
                    tool_results.append(tool_message)
                # 3. 再次请求 LLM，获得最终总结，tools 也要带上
                print("\n[函数调用结果已补全，LLM 总结回复]:", flush=True)
                print("==== messages ====")
                print(json.dumps(messages, ensure_ascii=False, indent=2))
                print("==== tools ====")
                print(json.dumps(tools, ensure_ascii=False, indent=2))
                async for chunk2 in llm.stream_chat(messages=messages, tools=tools):
                    if chunk2.get("content"):
                        print(chunk2["content"], end="", flush=True)
                print()
            else:
                print()  # 补全换行
        except Exception as e:
            print(f"[异常] {e}")

if __name__ == "__main__":
    asyncio.run(main())
