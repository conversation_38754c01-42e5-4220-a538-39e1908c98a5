#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS流式播放测试脚本 (使用 AudioNewPlayer)
主要测试功能：
1. 流式播放 - 测试边合成边播放的流式效果
2. 流式播放打断 - 测试长文本播放时的中途打断功能
"""

import asyncio
import os
import sys
import logging
import argparse
import time
from pathlib import Path
from typing import AsyncGenerator, Optional, Tuple
import websockets

# 将项目根目录添加到系统路径
# Ensure the parent directory (project root) is in the Python path
project_root = Path(__file__).resolve().parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

# 尝试导入必要的模块
try:
    from tts import TTSClient
    # 使用新的播放器
    from utils.audio_new_player import AudioNewPlayer, AudioPlayerError
    from utils.config_loader import load_config
except ImportError as e:
    print(f"导入模块失败: {e}")
    print(f"请确保项目结构正确，并且必要的库已安装。当前 Python 路径: {sys.path}")
    sys.exit(1)

# 设置日志
log_dir = Path(__file__).parent / "logs"
log_dir.mkdir(exist_ok=True)
log_file = log_dir / 'tts_streaming_test_new.log'

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(log_file, 'w', encoding='utf-8')
    ]
)
logger = logging.getLogger("tts_streaming_test_new")

class TTSStreamingTestNew:
    """TTS流式播放测试类 (使用 AudioNewPlayer)"""

    def __init__(self, config_path: str = "config.yaml"):
        """初始化测试环境"""
        # 确保配置文件路径相对于项目根目录
        if not Path(config_path).is_absolute():
            config_path = str(project_root / config_path)

        logger.info(f"使用配置文件: {config_path}")

        try:
            # 加载配置
            config_loader = load_config(config_path)
            self.tts_config = config_loader.get_tts_config()
            self.player_config_data = config_loader.get_audio_player_config() # 获取播放器配置数据 (AudioPlayerConfig dataclass)

            # 初始化TTS客户端
            self.tts_client = TTSClient(self.tts_config)

            # 初始化音频播放器 (使用 AudioNewPlayer)
            self.audio_player = AudioNewPlayer(
                # 从 dataclass 实例中获取值
                device=self.player_config_data.device,
                blocksize=self.player_config_data.blocksize,
                buffer_size=self.player_config_data.buffer_size,
                min_prebuffer=self.player_config_data.min_prebuffer,
                volume=self.player_config_data.volume
            )
            logger.info(f"AudioNewPlayer 初始化: device={self.audio_player.device}, blocksize={self.audio_player.blocksize}, buffer_size={self.audio_player.buffer_size}, min_prebuffer={self.audio_player.min_prebuffer}, volume={self.audio_player.get_volume():.2f}")


            logger.info(f"初始化完成，TTS采样率: {self.tts_config.sample_rate}Hz")
            logger.info(f"默认音色: {self.tts_config.default_speaker}")
            logger.info(f"可用音色列表: {self.tts_config.available_speakers}")

        except FileNotFoundError:
             logger.error(f"配置文件未找到: {config_path}")
             raise
        except Exception as e:
            logger.error(f"初始化失败: {e}", exc_info=True)
            raise

    async def test_stream_playback(self, text: str, speaker: Optional[str] = None) -> Tuple[bool, float]:
        """测试1：流式播放 - 边合成边播放

        Args:
            text: 要合成的文本
            speaker: 音色名称，如果为None则使用默认音色

        Returns:
            (success, duration): 成功标志和执行时间
        """
        logger.info(f"[流式播放测试] 开始 (文本长度: {len(text)})")
        start_time = time.time()
        success = False

        try:
            # 获取音频流
            logger.info("创建TTS音频流...")
            audio_stream: AsyncGenerator[bytes, None] = self.tts_client.synthesize_stream(text, speaker)

            # 直接流式播放
            logger.info("开始播放音频流...")
            # AudioNewPlayer.play_stream 会阻塞直到播放完成或发生错误/停止
            await self.audio_player.play_stream(
                audio_stream,
                sample_rate=int(self.tts_config.sample_rate),
                channels=1 # TTS 通常是单声道
            )

            duration = time.time() - start_time
            logger.info(f"[流式播放测试] 播放完成，总耗时 {duration:.2f}秒")
            success = True

        except AudioPlayerError as e:
            duration = time.time() - start_time
            logger.error(f"[流式播放测试] 音频播放失败: {e}")
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"[流式播放测试] 发生意外错误: {e}", exc_info=True)
        finally:
             # 确保播放器状态在测试结束后是 STOPPED 或 IDLE
            current_state = self.audio_player.state.name
            logger.info(f"[流式播放测试] 结束时播放器状态: {current_state}")
            if self.audio_player.is_playing():
                 logger.warning("[流式播放测试] 播放器在测试结束后仍在播放，尝试停止...")
                 await self.audio_player.stop("测试结束强制停止")

        return success, time.time() - start_time


    async def test_stream_interrupt(self, text: str,
                                   interrupt_after: float = 3.0,
                                   speaker: Optional[str] = None) -> Tuple[bool, float]:
        """测试2：流式播放打断 - 测试长文本播放时的中途打断功能

        Args:
            text: 要合成的长文本
            interrupt_after: 多少秒后打断播放
            speaker: 音色名称，如果为None则使用默认音色

        Returns:
            (success, duration): 成功标志和执行时间
        """
        if len(text) < 100:
            logger.warning(f"文本过短 ({len(text)} 字符)，可能不适合长时间打断测试，将重复文本。")
            repeat_times = max(2, int(1000 / len(text))) # 确保总长度约1000
            text = text * repeat_times
            logger.info(f"文本重复 {repeat_times} 次，新长度: {len(text)}")

        logger.info(f"[流式播放打断测试] 开始 (文本长度: {len(text)}, {interrupt_after}秒后打断)")
        start_time = time.time()
        success = False
        play_task: Optional[asyncio.Task] = None

        try:
            # 创建音频流任务但不等待它完成
            logger.info("创建TTS音频流...")
            audio_stream = self.tts_client.synthesize_stream(text, speaker)

            # 创建播放任务
            logger.info("开始播放音频流...")
            play_task = asyncio.create_task(
                self.audio_player.play_stream(
                    audio_stream,
                    sample_rate=int(self.tts_config.sample_rate),
                    channels=1
                ),
                name="interrupt_play_task"
            )

            # 等待指定时间或播放任务提前结束
            logger.info(f"等待 {interrupt_after} 秒或播放完成...")
            done, pending = await asyncio.wait(
                [play_task],
                timeout=interrupt_after,
                return_when=asyncio.FIRST_COMPLETED
            )

            if play_task in done:
                 # 播放任务在打断时间之前就结束了
                 logger.info("播放在预定打断时间前已自然结束。")
                 # 检查任务是否有异常
                 try:
                     await play_task
                     success = True # 正常结束算成功
                 except Exception as e:
                     logger.error(f"播放任务提前结束但带有错误: {e}")
                     success = False
            else:
                 # 播放任务仍在运行，执行打断
                 logger.info("时间到，执行打断...")
                 await self.audio_player.stop("测试请求打断")
                 logger.info("停止信号已发送。等待播放任务结束...")
                 try:
                     await asyncio.wait_for(play_task, timeout=5.0)
                     # 如果 play_task 正常结束（没有抛异常），也可能是成功停止了？
                     # 但更可能的情况是它内部处理了 stop 并抛出了 AudioPlayerError
                     logger.info("播放任务已结束（可能正常退出或内部处理了停止）。")
                     # 检查最终状态是否是停止相关状态
                     if self.audio_player.state in (PlayerState.STOPPED, PlayerState.IDLE):
                         success = True # 确认状态正确
                     else:
                         logger.warning(f"任务结束但播放器状态不是 STOPPED/IDLE: {self.audio_player.state.name}")
                         success = False
                 except asyncio.TimeoutError:
                     logger.warning("等待播放任务响应停止信号超时。")
                     success = False
                 except asyncio.CancelledError:
                     logger.info("播放任务被取消（可能在停止过程中）。")
                     success = True # 取消也算打断成功
                 # --- 修改: 捕获 AudioPlayerError ---
                 except AudioPlayerError as player_err:
                     logger.info(f"播放任务因 AudioPlayerError 而结束 (预期中的打断): {player_err}")
                     success = True # 捕获到播放器错误，视为成功打断
                 # --- 修改结束 ---
                 except Exception as e:
                     logger.error(f"等待播放任务结束时发生错误: {e}")
                     success = False

            duration = time.time() - start_time
            logger.info(f"[流式播放打断测试] 完成，结果: {'成功' if success else '失败'}, 总耗时 {duration:.2f}秒")

        except AudioPlayerError as e:
            duration = time.time() - start_time
            logger.error(f"[流式播放打断测试] 音频播放器错误: {e}")
            success = False
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"[流式播放打断测试] 发生意外错误: {e}", exc_info=True)
            success = False
        finally:
             # 确保即使发生异常，play_task 也被处理
             if play_task and not play_task.done():
                  logger.warning("[流式播放打断测试] play_task 在测试结束时仍未完成，尝试取消。")
                  play_task.cancel()
                  try:
                       await asyncio.wait_for(play_task, timeout=1.0)
                  except (asyncio.TimeoutError, asyncio.CancelledError):
                       pass # 忽略取消或超时
             # 检查最终状态
             current_state = self.audio_player.state.name
             logger.info(f"[流式播放打断测试] 结束时播放器状态: {current_state}")


        return success, time.time() - start_time

    async def test_bidi_stream_playback(self, text: str, speaker: Optional[str] = None, chunk_size: int = 20) -> Tuple[bool, float]:
        """测试3：双向流式TTS - 边发边收边播
        Args:
            text: 要合成的文本
            speaker: 音色名称
            chunk_size: 每次送入TTS的文本块大小
        Returns:
            (success, duration): 成功标志和执行时间
        """
        logger.info(f"[双向流式TTS测试] 开始 (文本长度: {len(text)})")
        start_time = time.time()
        success = False
        import asyncio
        queue = asyncio.Queue()

        async def llm_simulate_stream(text, chunk_size):
            # 简单按chunk_size切分文本模拟LLM流式输出
            for i in range(0, len(text), chunk_size):
                chunk = text[i:i+chunk_size]
                logger.debug(f"[LLM模拟] 发送文本块: {chunk}")
                await queue.put(chunk)
                await asyncio.sleep(0.1)  # 模拟LLM生成延迟
            await queue.put(None)  # 结束标志

        try:
            # 启动LLM流式模拟和TTS边发边收
            logger.info("启动LLM流式文本模拟和TTS双向流式合成...")
            llm_task = asyncio.create_task(llm_simulate_stream(text, chunk_size))
            audio_stream = self.tts_client.synthesize_stream_bidi(queue, speaker)
            await self.audio_player.play_stream(
                audio_stream,
                sample_rate=int(self.tts_config.sample_rate),
                channels=1
            )
            await llm_task
            duration = time.time() - start_time
            logger.info(f"[双向流式TTS测试] 播放完成，总耗时 {duration:.2f}秒")
            success = True
        except AudioPlayerError as e:
            duration = time.time() - start_time
            logger.error(f"[双向流式TTS测试] 音频播放失败: {e}")
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"[双向流式TTS测试] 发生意外错误: {e}", exc_info=True)
        finally:
            current_state = self.audio_player.state.name
            logger.info(f"[双向流式TTS测试] 结束时播放器状态: {current_state}")
            if self.audio_player.is_playing():
                logger.warning("[双向流式TTS测试] 播放器在测试结束后仍在播放，尝试停止...")
                await self.audio_player.stop("测试结束强制停止")
        return success, time.time() - start_time

    async def close(self):
        """关闭测试环境，释放资源"""
        logger.info("开始释放资源...")
        try:
            # 确保播放器已停止 (AudioNewPlayer 没有单独的 close 方法，stop 会触发清理)
            if self.audio_player and self.audio_player.is_playing():
                logger.info("测试结束，确保停止播放器...")
                await self.audio_player.stop("测试结束清理")
                await asyncio.sleep(0.5) # 给点时间让清理完成

            # 关闭TTS客户端 (synthesize_stream 内部会关闭连接，这里不再显式调用)
            # logger.info("显式关闭 TTS 客户端 (注释掉)...")
            # if self.tts_client and hasattr(self.tts_client, 'close'):
            #      try:
            #          if asyncio.iscoroutinefunction(self.tts_client.close):
            #              await self.tts_client.close()
            #          else:
            #              self.tts_client.close()
            #          logger.info("TTS 客户端已关闭。")
            #      except (OSError, RuntimeError, websockets.exceptions.ConnectionClosedError, websockets.exceptions.ConnectionClosedOK) as close_err:
            #           logger.warning(f"关闭TTS客户端时发生可忽略的错误: {type(close_err).__name__}: {close_err}")
            #      except Exception as e:
            #          logger.error(f"关闭TTS客户端时发生意外错误: {e}", exc_info=True)

            logger.info("资源释放完成。")
        except Exception as e:
            logger.error(f"资源释放过程中发生错误: {e}", exc_info=True)

async def main():
    parser = argparse.ArgumentParser(
        description="""TTS流式播放测试 (使用 AudioNewPlayer)
主要测试功能：
  stream     流式播放测试
  interrupt  流式播放打断测试
  bidi       双向流式TTS测试
  both       同时运行两项测试
  all        运行所有测试
""",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument('--test',
                       choices=['stream', 'interrupt', 'bidi', 'both', 'all'],
                       default='both',
                       help='要运行的测试类型')
    parser.add_argument('--config',
                       default='config.yaml', # 相对于项目根目录
                       help='配置文件路径 (相对于项目根目录)')
    parser.add_argument('--text',
                       default='',
                       help='要合成的测试文本，如果不提供则使用默认文本')
    parser.add_argument('--speaker',
                        default=None,
                        help='指定 TTS 音色，不指定则使用配置中的默认音色')
    parser.add_argument('--interrupt-after',
                       type=float,
                       default=3.0,
                       help='打断测试中多少秒后打断播放')
    parser.add_argument('--log-level',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO',
                       help='设置日志记录级别')
    args = parser.parse_args()

    # 设置日志级别
    logger.setLevel(getattr(logging, args.log_level))
    # 应用到所有处理器
    for handler in logging.getLogger().handlers:
        handler.setLevel(getattr(logging, args.log_level))

    # 测试数据
    short_text = "这是一个使用新的音频播放器进行的TTS流式播放测试。我们正在测试文本到语音的合成效果以及新的播放器功能。"

    long_text = """人工智能（Artificial Intelligence），英文缩写为AI。它是研究、开发用于模拟、延伸和扩展人的智能的理论、方法、技术及应用系统的一门新的技术科学。
    人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器， 该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。
    人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大，可以设想，未来人工智能带来的科技产品，将会是人类智慧的"容器"。
    人工智能可以对人的意识、思维的信息过程的模拟。人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。
    人工智能的应用极其广泛，包括机器学习、计算机视觉、自然语言处理、机器人技术、专家系统等。例如，在医疗领域，AI可以辅助医生进行疾病诊断；在金融领域，AI可以用于风险评估和欺诈检测；在交通领域，AI是自动驾驶汽车的核心技术。
    随着算力的提升和算法的进步，人工智能正在以前所未有的速度发展，深刻地改变着我们的社会和生活方式。理解和掌握人工智能，对于迎接未来的挑战至关重要。"""

    # 如果提供了自定义文本，则使用它
    test_text_stream = args.text if args.text else short_text
    test_text_interrupt = args.text if args.text else long_text # 打断测试默认用长文本

    tester = None
    try:
        tester = TTSStreamingTestNew(args.config)
        logger.info(f"=== TTS流式播放测试 (AudioNewPlayer) [{args.test}] ===")
        logger.info(f"使用音色: {args.speaker or tester.tts_config.default_speaker}")

        # 运行指定的测试
        if args.test in ['stream', 'both']:
            logger.info("""
===== 开始流式播放测试 =====""")
            success, duration = await tester.test_stream_playback(test_text_stream, speaker=args.speaker)
            logger.info(f"流式播放测试结果: {'成功' if success else '失败'}, 耗时: {duration:.2f}秒")
            # 等待一小段时间以确保资源释放或为下一个测试做准备
            await asyncio.sleep(1.5)

        if args.test in ['interrupt', 'both']:
            logger.info("""
===== 开始流式播放打断测试 =====""")
            success, duration = await tester.test_stream_interrupt(
                test_text_interrupt,
                interrupt_after=args.interrupt_after,
                speaker=args.speaker
            )
            logger.info(f"流式播放打断测试结果: {'成功' if success else '失败'}, 耗时: {duration:.2f}秒")
            await asyncio.sleep(1.0)

        if args.test in ['bidi', 'all']:
            logger.info("""
===== 开始双向流式TTS测试 =====""")
            success, duration = await tester.test_bidi_stream_playback(test_text_stream, speaker=args.speaker)
            logger.info(f"双向流式TTS测试结果: {'成功' if success else '失败'}, 耗时: {duration:.2f}秒")
            await asyncio.sleep(1.0)

        logger.info("""
=== 所有测试完成 ===""")

    except FileNotFoundError:
        logger.error(f"无法找到配置文件 '{args.config}'。请确保文件存在于项目根目录或提供了正确的路径。")
        print(f"""
错误：找不到配置文件 '{args.config}'""")
    except ImportError as e:
         logger.error(f"导入模块时出错: {e}。请检查依赖是否正确安装以及项目结构。")
         print(f"""
错误：导入模块失败: {e}""")
    except AudioPlayerError as e:
        logger.error(f"音频播放器遇到问题: {e}")
        print(f"""
错误：音频播放器问题: {e}""")
    finally:
        # 确保资源释放
        if tester:
            await tester.close()
            await asyncio.sleep(0.5) # 稍长等待，让后台任务有机会完成

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        print("""
测试已被用户中断""")
    # 不再捕获顶层的 Exception，让 main 函数中的捕获处理
    # except Exception as e:
    #     logger.critical(f"启动测试时发生严重错误: {e}", exc_info=True)
    #     print(f"""
    # 启动测试失败: {e}""")
