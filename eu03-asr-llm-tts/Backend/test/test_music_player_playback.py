import os
import sys
import time
import logging
import asyncio

# 将项目根目录添加到 Python 路径中，以便导入模块
# 脚本位于 Backend/test/，因此我们需要向上两级
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

# 现在可以导入 MusicPlayer 了
from Backend.utils.function_call.function_tools_impl import MusicPlayer

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

async def main():
    """
    主函数，用于测试 MusicPlayer 的播放功能。
    """
    try:
        # 1. 实例化 MusicPlayer
        logging.info("Instantiating MusicPlayer...")
        music_player = MusicPlayer()
        logging.info("MusicPlayer instance created.")

        # 2. 使用 MusicPlayer 的方法来播放音乐
        music_file = "彩虹.mp3"
        logging.info(f"Attempting to play music file via MusicPlayer: {music_file}")
        result = await music_player.play_music(music_file)
        logging.info(f"MusicPlayer response: {result}")

        # 3. 等待足够长的时间（例如7秒），以确保慢速的音频加载和重采样能够完成
        logging.info("Waiting for audio to load and start playing...")
        await asyncio.sleep(7)

        # 4. 检查音乐是否正在播放
        if music_player.is_playing():
            logging.info(f"'{music_file}' is now playing. 🎶")
        else:
            logging.warning("Playback command issued, but music is not playing. Check logs for errors.")

        # 5. 保持脚本运行以供收听
        logging.info("Running for 10 seconds to allow for playback...")
        count = 0
        while count < 10 and music_player.is_playing():
            await asyncio.sleep(1)
            count += 1
            print(f"Playback active... {10 - count}s remaining", end="\r")
        print("\nPlayback duration finished or music stopped.")

        # 6. 停止音乐
        if music_player.is_playing():
            logging.info("Stopping music...")
            await music_player.stop_music()
            logging.info("Music stopped.")

    except Exception as e:
        logging.error(f"An error occurred in the test script: {e}", exc_info=True)

    finally:
        # 脚本结束
        if music_player and music_player.is_playing():
            await music_player.stop_music()
        logging.info("Script finished. ✨")

if __name__ == "__main__":
    asyncio.run(main())