import sys
import os
import time
import logging

# 将项目根目录（即 'eu03-asr-llm-tts'）添加到 Python 路径中
# 脚本位于 Backend/test/，因此我们需要向上两级才能到达根目录
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 配置日志记录
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

try:
    # 动态导入 MusicPlayer
    from Backend.utils.function_call.function_tools_impl import MusicPlayer
except ImportError as e:
    logging.error(f"无法导入 MusicPlayer: {e}")
    logging.error("请确保从正确的目录运行脚本，并且 Backend 模块在 Python 路径中。")
    sys.exit(1)

def main():
    """
    主函数，用于测试音乐播放功能。
    """
    logging.info("正在初始化 MusicPlayer...")
    music_player = MusicPlayer()
    
    music_file = "彩虹.mp3"
    logging.info(f"尝试播放音乐文件: {music_file}")

    try:
        # 调用 play_music 方法
        play_result = music_player.play_music(music_file)
        logging.info(f"play_music('{music_file}') 返回: {play_result}")

        # 等待片刻，让音乐有时间开始播放
        logging.info("等待 2 秒让音乐开始...")
        time.sleep(2)

        # 检查音乐是否真的在播放
        if music_player.is_playing():
            logging.info("✅ 成功: 已确认音乐正在播放。")
        else:
            logging.error("❌ 失败: 调用 play_music 后音乐未播放。")
            return  # 如果播放失败则提前退出

        # 让音乐播放一会儿
        logging.info("让音乐播放 10 秒钟...")
        time.sleep(10)

        # 停止音乐
        logging.info("正在停止音乐...")
        stop_result = music_player.stop_music()
        logging.info(f"stop_music() 返回: {stop_result}")
        
        # 最终检查
        time.sleep(1) # 等待一秒确保状态更新
        if not music_player.is_playing():
            logging.info("音乐已成功停止。")
        else:
            logging.warning("警告: 调用 stop_music 后音乐仍在播放。")

    except Exception as e:
        logging.error(f"测试过程中发生错误: {e}")
    finally:
        # 确保即使发生错误也能清理资源
        if music_player and music_player.is_playing():
            logging.info("在 finally 块中确保音乐停止。")
            music_player.stop_music()
        logging.info("测试脚本执行完毕。")


if __name__ == "__main__":
    # 注意: 运行此测试可能需要一个可用的显示服务器（即使是虚拟的，如 Xvfb），
    # 因为 pygame 可能需要它。
    # 例如: xvfb-run python Backend/test/test_simple_music_playback.py
    main()