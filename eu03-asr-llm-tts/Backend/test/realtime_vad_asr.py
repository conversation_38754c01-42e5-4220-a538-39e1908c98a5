import pyaudio
import webrtcvad
import wave
import numpy as np
import time
import threading
import os
import sherpa_ncnn
from datetime import datetime # Import datetime

# --- Helper function for logging ---
def log_with_timestamp(message):
    """Prints a message prepended with the current timestamp."""
    now = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3] # Millisecond precision
    print(f"[{now}] {message}")

# --- Parameters ---
# VAD
VAD_FRAME_MS = 30  # VAD frame duration (10, 20, 30 ms)
RATE = 16000
CHANNELS = 1
VAD_CHUNK_SIZE = int(RATE * VAD_FRAME_MS / 1000) # Bytes per VAD frame (e.g., 30ms -> 960 bytes)
VAD_MODE = 3       # VAD aggressiveness (0-3)

# Audio Input (PyAudio)
PYAUDIO_FORMAT = pyaudio.paInt16
PYAUDIO_CHUNK_SIZE = VAD_CHUNK_SIZE # Read audio in VAD frame sizes

# Silence Detection
NO_SPEECH_THRESHOLD_S = 1.0 # Seconds of silence to trigger segment processing

# Output
OUTPUT_DIR = "./output_wavs" # Directory to save temporary audio segments

# --- Global Variables ---
audio_buffer = []         # Buffer to hold speech frames
is_speaking = False       # Flag if currently detecting speech
last_speech_time = 0      # Timestamp of the last detected speech frame
recording_active = True   # Flag to control the recording loop
audio_file_count = 0      # Counter for naming audio segment files
recognizer = None         # Global recognizer instance (initialized later)
segment_id = 0            # Counter for recognized segments
asr_lock = threading.Lock() # Lock for protecting ASR recognizer access

# --- sherpa-ncnn ASR Model Configuration ---
# Adjust path as needed
ASR_MODEL_DIR = '../model/sherpa-ncnn-streaming-zipformer-bilingual-zh-en-2023-02-13'

def initialize_asr():
    """Initializes the sherpa-ncnn recognizer."""
    global recognizer
    if not os.path.exists(f"{ASR_MODEL_DIR}/tokens.txt"):
         log_with_timestamp(f"Error: ASR model files not found in {ASR_MODEL_DIR}")
         log_with_timestamp("Please ensure the model files are downloaded and placed correctly.")
         exit(1)

    recognizer = sherpa_ncnn.Recognizer(
        tokens=f"{ASR_MODEL_DIR}/tokens.txt",
        encoder_param=f"{ASR_MODEL_DIR}/encoder_jit_trace-pnnx.ncnn.param",
        encoder_bin=f"{ASR_MODEL_DIR}/encoder_jit_trace-pnnx.ncnn.bin",
        decoder_param=f"{ASR_MODEL_DIR}/decoder_jit_trace-pnnx.ncnn.param",
        decoder_bin=f"{ASR_MODEL_DIR}/decoder_jit_trace-pnnx.ncnn.bin",
        joiner_param=f"{ASR_MODEL_DIR}/joiner_jit_trace-pnnx.ncnn.param",
        joiner_bin=f"{ASR_MODEL_DIR}/joiner_jit_trace-pnnx.ncnn.bin",
        num_threads=4,
        decoding_method="modified_beam_search",
        # Endpoint detection is not needed here as we segment based on silence
        enable_endpoint_detection=False,
    )
    log_with_timestamp("sherpa-ncnn Recognizer initialized.")

def run_asr_on_file(filepath):
    """Runs ASR on a given WAV file using sherpa-ncnn."""
    global recognizer, segment_id
    if not recognizer:
        log_with_timestamp("Error: Recognizer not initialized.")
        return

    # Acquire lock to ensure thread-safe access to the recognizer
    with asr_lock:
        try:
            with wave.open(filepath, 'rb') as wf:
                if wf.getnchannels() != CHANNELS or \
                   wf.getframerate() != RATE or \
                   wf.getsampwidth() != pyaudio.get_sample_size(PYAUDIO_FORMAT):
                    log_with_timestamp(f"Warning: Audio file {filepath} has unexpected format. Skipping.")
                    return

                audio_data = wf.readframes(wf.getnframes())
                audio_int16 = np.frombuffer(audio_data, dtype=np.int16)
                # Normalize to float32 [-1, 1]
                audio_float32 = audio_int16.astype(np.float32) / 32768.0

            log_with_timestamp(f"Processing segment: {filepath}")

            # Feed the entire waveform to the recognizer
            recognizer.accept_waveform(RATE, audio_float32)

            # Indicate that this is the end of the audio input for this segment
            recognizer.input_finished()

            # Get the final result for the segment by accessing the .text attribute
            result = recognizer.text

            # Reset the recognizer state for the next segment
            recognizer.reset()

            if result:
                segment_id += 1
                log_with_timestamp(f"ASR Result [{segment_id}]: {result}")
            else:
                log_with_timestamp("ASR Result: [No speech detected in segment]")

        except Exception as e:
            log_with_timestamp(f"Error processing ASR for {filepath}: {e}")
            # Attempt to reset recognizer even if error occurs during processing
            try:
                recognizer.reset()
            except Exception as reset_e:
                log_with_timestamp(f"Error resetting recognizer after ASR error: {reset_e}")

    # --- Lock is released here ---

    # Optional: Delete the temporary file after processing (outside the lock)
    try:
        os.remove(filepath)
        # log_with_timestamp(f"Deleted temporary file: {filepath}") # Optional log
    except OSError as e:
        log_with_timestamp(f"Error deleting file {filepath}: {e}")

def save_and_process_audio(frames):
    """Saves the buffered audio frames to a WAV file and starts ASR processing."""
    global audio_file_count
    if not frames:
        return

    audio_file_count += 1
    filepath = os.path.join(OUTPUT_DIR, f"segment_{audio_file_count}.wav")
    log_with_timestamp(f"Saving segment to {filepath}...")

    try:
        wf = wave.open(filepath, 'wb')
        wf.setnchannels(CHANNELS)
        wf.setsampwidth(pyaudio.get_sample_size(PYAUDIO_FORMAT))
        wf.setframerate(RATE)
        wf.writeframes(b''.join(frames))
        wf.close()

        # Start ASR in a separate thread
        asr_thread = threading.Thread(target=run_asr_on_file, args=(filepath,))
        asr_thread.start()

    except Exception as e:
        log_with_timestamp(f"Error saving or processing audio segment: {e}")


def audio_recorder():
    """Handles audio recording, VAD, buffering, and triggering processing."""
    global audio_buffer, is_speaking, last_speech_time, recording_active

    vad = webrtcvad.Vad(VAD_MODE)
    p = pyaudio.PyAudio()
    stream = None

    try:
        stream = p.open(format=PYAUDIO_FORMAT,
                        channels=CHANNELS,
                        rate=RATE,
                        input=True,
                        frames_per_buffer=PYAUDIO_CHUNK_SIZE)

        log_with_timestamp('开始实时检测麦克风输入 (WebRTC VAD)...')
        log_with_timestamp(f"VAD 帧长: {VAD_FRAME_MS}ms, 静音阈值: {NO_SPEECH_THRESHOLD_S}s")

        while recording_active:
            try:
                # Read audio data corresponding to VAD frame size
                data = stream.read(PYAUDIO_CHUNK_SIZE, exception_on_overflow=False)

                # Ensure the data length is correct for VAD
                if len(data) != VAD_CHUNK_SIZE * pyaudio.get_sample_size(PYAUDIO_FORMAT):
                    log_with_timestamp(f"Warning: Incorrect data size read: {len(data)}")
                    continue

                is_speech = vad.is_speech(data, RATE)

                if is_speech:
                    # log_with_timestamp("语音", end=" ") # Verbose output
                    if not is_speaking:
                        log_with_timestamp("检测到语音开始...")
                    audio_buffer.append(data)
                    is_speaking = True
                    last_speech_time = time.time()
                else:
                    # log_with_timestamp("静音", end=" ") # Verbose output
                    if is_speaking:
                        # Check if silence duration exceeds threshold
                        if time.time() - last_speech_time > NO_SPEECH_THRESHOLD_S:
                            log_with_timestamp(f"检测到静音超过 {NO_SPEECH_THRESHOLD_S}s, 处理语音段...")
                            # Copy buffer content before clearing
                            frames_to_process = list(audio_buffer)
                            audio_buffer.clear()
                            is_speaking = False
                            # Process the collected speech segment
                            save_and_process_audio(frames_to_process)
                        else:
                            # Silence detected, but not long enough yet, keep buffering
                            audio_buffer.append(data)
                    else:
                        # Continuous silence, do nothing
                        pass
                # time.sleep(0.001) # Small sleep if needed, but reading should block

            except IOError as e:
                log_with_timestamp(f"PyAudio read error: {e}")
                # Handle buffer overflow or other read issues if necessary
                # Potentially restart stream? For now, just continue.
                time.sleep(0.1)


    except Exception as e:
        log_with_timestamp(f"An error occurred in audio_recorder: {e}")
    finally:
        log_with_timestamp("Stopping audio recording...")
        if stream:
            stream.stop_stream()
            stream.close()
        p.terminate()
        log_with_timestamp("PyAudio terminated.")

# --- Main Execution ---
if __name__ == "__main__":
    # Ensure output directory exists
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Initialize ASR model
    initialize_asr()

    # Start the audio recorder thread
    audio_thread = threading.Thread(target=audio_recorder)
    audio_thread.start()

    try:
        # Keep the main thread alive until Ctrl+C
        while audio_thread.is_alive():
            audio_thread.join(timeout=1.0)
    except KeyboardInterrupt:
        log_with_timestamp("Ctrl+C detected. Shutting down...")
        recording_active = False # Signal the recording thread to stop
        audio_thread.join()      # Wait for the recording thread to finish cleanup
        log_with_timestamp("Shutdown complete.")
    except Exception as e:
        log_with_timestamp(f"An unexpected error occurred in main: {e}")
        recording_active = False
        if audio_thread.is_alive():
            audio_thread.join()
        log_with_timestamp("Shutdown due to error.") 