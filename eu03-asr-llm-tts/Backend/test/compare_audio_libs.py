#!/usr/bin/env python3
"""
比较 pygame 和 sounddevice 的设备识别方式
找出为什么 sounddevice 能识别 UACDemoV1.0 而 pygame 不能
"""

import pygame
import sounddevice as sd
import os
import numpy as np
import time

def test_sounddevice_devices():
    """测试 sounddevice 设备识别"""
    print("=== SoundDevice 设备信息 ===")
    
    # 列出所有设备
    devices = sd.query_devices()
    print("所有可用设备:")
    for i, device in enumerate(devices):
        print(f"  设备 {i}: {device['name']}")
        print(f"    最大输出通道: {device['max_output_channels']}")
        print(f"    默认采样率: {device['default_samplerate']}")
        if 'UACDemo' in device['name'] or 'UACDemoV1.0' in device['name']:
            print(f"    *** 找到目标设备! ***")
        print()
    
    # 尝试通过不同方式指定 UACDemoV1.0
    target_patterns = ['UACDemoV1.0', 'UACDemo', 'Jieli']
    
    for pattern in target_patterns:
        print(f"\n尝试查找包含 '{pattern}' 的设备:")
        for i, device in enumerate(devices):
            if pattern in device['name']:
                print(f"  找到设备 {i}: {device['name']}")
                
                # 测试是否可以用这个设备播放
                try:
                    # 生成测试音频
                    sample_rate = int(device['default_samplerate'])
                    duration = 1.0
                    frequency = 440
                    t = np.linspace(0, duration, int(sample_rate * duration), False)
                    wave = np.sin(frequency * 2 * np.pi * t) * 0.3
                    
                    print(f"    尝试播放测试音频 (设备索引 {i})...")
                    sd.play(wave, samplerate=sample_rate, device=i)
                    sd.wait()
                    print(f"    ✓ 播放成功!")
                    
                    # 也尝试通过名称指定
                    print(f"    尝试通过名称播放...")
                    sd.play(wave, samplerate=sample_rate, device=device['name'])
                    sd.wait()
                    print(f"    ✓ 通过名称播放成功!")
                    
                    return i, device['name']  # 返回成功的设备信息
                    
                except Exception as e:
                    print(f"    ✗ 播放失败: {e}")
    
    return None, None

def test_pygame_with_pulseaudio_device_names():
    """测试 pygame 使用 PulseAudio 设备名称"""
    print("\n=== 尝试 pygame 使用 PulseAudio 设备名称 ===")
    
    # 获取 PulseAudio 设备列表
    import subprocess
    try:
        result = subprocess.run(['pactl', 'list', 'short', 'sinks'], 
                               capture_output=True, text=True)
        if result.returncode == 0:
            print("PulseAudio 输出设备:")
            lines = result.stdout.strip().split('\n')
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    device_id = parts[0]
                    device_name = parts[1]
                    print(f"  设备 {device_id}: {device_name}")
                    
                    # 如果是 UACDemoV1.0 相关设备，尝试用 pygame
                    if 'UACDemo' in device_name or 'Jieli' in device_name:
                        print(f"    找到目标设备，尝试用 pygame...")
                        
                        # 尝试不同的设备名称格式
                        device_names_to_try = [
                            device_name,  # 完整 PulseAudio 名称
                            device_name.split('.')[-1],  # 最后一部分
                            f"pulse:{device_name}",  # 明确指定 pulse 前缀
                            device_id,  # 设备ID
                        ]
                        
                        for dev_name in device_names_to_try:
                            try:
                                if pygame.mixer.get_init():
                                    pygame.mixer.quit()
                                
                                print(f"      尝试设备名: {dev_name}")
                                pygame.mixer.init(devicename=dev_name)
                                print(f"      ✓ 成功!")
                                return dev_name
                                
                            except Exception as e:
                                print(f"      ✗ 失败: {e}")
    except Exception as e:
        print(f"获取 PulseAudio 设备失败: {e}")
    
    return None

def test_pygame_environment_settings():
    """测试不同的 pygame 环境设置"""
    print("\n=== 测试 pygame 不同环境设置 ===")
    
    # 测试不同的 SDL_AUDIODRIVER 设置
    audio_drivers = ['pulse', 'alsa', 'oss', None]
    
    for driver in audio_drivers:
        print(f"\n测试 SDL_AUDIODRIVER = {driver}")
        
        # 设置环境变量
        if driver:
            os.environ['SDL_AUDIODRIVER'] = driver
        elif 'SDL_AUDIODRIVER' in os.environ:
            del os.environ['SDL_AUDIODRIVER']
        
        try:
            if pygame.mixer.get_init():
                pygame.mixer.quit()
            
            # 尝试默认初始化
            pygame.mixer.init()
            init_info = pygame.mixer.get_init()
            print(f"  默认初始化成功: {init_info}")
            
            # 尝试播放测试音频
            # 创建1秒的测试音频
            sample_rate = init_info[0]
            duration = 1.0
            frequency = 440
            samples = int(sample_rate * duration)
            
            # 生成正弦波
            wave_array = np.sin(2 * np.pi * frequency * np.linspace(0, duration, samples))
            wave_array = (wave_array * 32767).astype(np.int16)
            
            # 转换为 pygame 格式，确保数组是C连续的
            if init_info[2] == 2:  # 立体声
                stereo_wave = np.array([wave_array, wave_array]).T
                stereo_wave = np.ascontiguousarray(stereo_wave)  # 确保C连续
                sound = pygame.sndarray.make_sound(stereo_wave)
            else:  # 单声道
                wave_array = np.ascontiguousarray(wave_array)  # 确保C连续
                sound = pygame.sndarray.make_sound(wave_array)
            
            print(f"  播放测试音频...")
            sound.play()
            time.sleep(duration + 0.5)
            print(f"  ✓ 播放成功!")
            
        except Exception as e:
            print(f"  ✗ 失败: {e}")
    
    # 恢复原始设置
    os.environ['SDL_AUDIODRIVER'] = 'pulse'

def main():
    """主函数"""
    print("音频库设备识别对比测试")
    print("=" * 60)
    
    # 测试 sounddevice
    sd_device_index, sd_device_name = test_sounddevice_devices()
    
    # 测试 pygame 环境设置
    test_pygame_environment_settings()
    
    # 测试 pygame PulseAudio 设备名称
    successful_pygame_device = test_pygame_with_pulseaudio_device_names()
    
    print("\n" + "=" * 60)
    print("总结:")
    print(f"SoundDevice 成功设备: {sd_device_name} (索引: {sd_device_index})")
    print(f"Pygame 成功设备: {successful_pygame_device}")
    
    if sd_device_name and not successful_pygame_device:
        print("\n建议解决方案:")
        print("1. 使用 PulseAudio 设置默认设备:")
        print("   pactl set-default-sink <pulseaudio_device_name>")
        print("2. 或者继续使用 sounddevice 而不是 pygame")
        print("3. 检查 pygame 的 SDL 音频驱动配置")

if __name__ == "__main__":
    main() 