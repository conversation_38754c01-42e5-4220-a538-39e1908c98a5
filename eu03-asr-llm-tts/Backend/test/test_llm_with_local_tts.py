import sys
import os
import asyncio
import json
import logging
import requests
import io
import soundfile as sf

# --- 配置区 ---
# TTS 服务器 URL
TTS_SERVER_URL = "http://127.0.0.1:5000/synthesize"
# --- ---

# 配置 logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 自动添加项目根目录到sys.path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from utils.config_loader import ConfigLoader
from llm.client import LLMClient
from utils.function_call.function_call_tools import FunctionRegistry
from utils.function_call.function_call_tools import music_player_instance
from utils import AudioNewPlayer
from utils.function_call.function_schema import get_all_schemas

# 为 TTS 创建独立的播放器实例
tts_player = AudioNewPlayer(volume=1.0) # 可以调整音量
logger.info("TTS 音频播放器初始化完成。")

# 定义日志辅助函数 (保持不变)
def log_player_state(player: AudioNewPlayer, stage: str):
    if player is None:
        logger.info(f"[PlayerStateLog @ {stage}] Player instance is None")
        return

    is_playing = player.is_playing()
    stream_active = player._stream.active if player._stream else 'N/A'
    stream_stopped = player._stream.stopped if player._stream else 'N/A'
    qsize = player._audio_queue.qsize() if player._audio_queue else 'N/A'

    logger.info(f"[PlayerStateLog @ {stage}] is_playing={is_playing}, stream_active={stream_active}, stream_stopped={stream_stopped}, qsize={qsize}")


# 自动生成tools schema (保持不变)
def build_tools_schema():
    return get_all_schemas()

# --- 新增: TTS 客户端函数 ---
async def synthesize_and_play_tts(text: str, sid: int = 0):
    """将文本发送到TTS服务器并使用 tts_player 播放返回的音频"""
    if not text or not text.strip():
        logger.info("TTS: 接收到空文本，跳过合成。")
        return

    logger.info(f"TTS: 准备合成文本: '{text[:50]}...'") # 只打印部分文本
    payload = {"text": text, "sid": sid}

    try:
        # 使用 run_in_executor 避免阻塞 asyncio 事件循环
        loop = asyncio.get_running_loop()
        response = await loop.run_in_executor(
            None, # 使用默认的 ThreadPoolExecutor
            lambda: requests.post(TTS_SERVER_URL, json=payload, timeout=60) # 增加超时
        )
        response.raise_for_status()

        if response.headers.get('content-type') == 'audio/wav':
            logger.info("TTS: 收到音频流，准备播放...")
            try:
                # 使用 soundfile 从内存读取 *仅用于获取采样率*
                # 注意：如果服务器响应头包含采样率信息，可以优化掉这一步
                with io.BytesIO(response.content) as bio:
                    with sf.SoundFile(bio) as sound_file:
                        samplerate = sound_file.samplerate
                        # TTS通常是单声道，所以我们假设 channels=1
                        channels = 1 # 或 sound_file.channels 如果你想更精确
                        logger.info(f"TTS: 音频信息获取成功, 采样率: {samplerate}, 通道数: {channels}, 数据长度: {len(response.content)} bytes")

                # 使用 tts_player 的 play 方法，传递原始字节和获取到的信息
                await tts_player.play(response.content, samplerate, channels=channels)

                # 等待播放完成 (AudioNewPlayer 内部应该处理好了)
                # 如果 AudioNewPlayer 的 play 不是阻塞/等待的，
                # 可能需要一个机制来等待播放结束，或者允许并发播放
                # 假设 AudioNewPlayer 会处理好播放队列或状态
                logger.info("TTS: 音频已提交给播放器。")

            except sf.SoundFileError as e:
                logger.error(f"TTS: 读取音频数据失败: {e}", exc_info=True)
            except Exception as e:
                 logger.error(f"TTS: 播放时发生错误: {e}", exc_info=True)

        else:
            # 处理服务器返回错误 JSON 的情况
            try:
                error_details = response.json()
                logger.error(f"TTS 服务器返回错误: {error_details}")
            except requests.exceptions.JSONDecodeError:
                logger.error(f"TTS 服务器返回非预期 content-type: {response.headers.get('content-type')}")
                logger.error(f"TTS 服务器响应内容: {response.text[:200]}...") # 打印部分响应

    except requests.exceptions.Timeout:
        logger.error(f"TTS: 请求超时 ({TTS_SERVER_URL})")
    except requests.exceptions.RequestException as e:
        logger.error(f"TTS: 连接或通信失败: {e}")
    except Exception as e:
        logger.error(f"TTS: 调用时发生未知错误: {e}", exc_info=True)

# --- 修改清理函数 ---
async def cleanup_resources():
    """清理资源"""
    logger.info("正在清理资源...")
    tasks = []
    # 停止音乐播放
    if music_player_instance.is_playing():
        tasks.append(asyncio.create_task(music_player_instance.stop_music()))
        logger.info("已提交停止音乐播放任务")

    # 停止 TTS 播放 (如果 AudioNewPlayer 有 stop 方法)
    # 假设 tts_player 也有类似的停止逻辑或在退出时自动清理
    if hasattr(tts_player, 'stop') and callable(tts_player.stop):
         tasks.append(asyncio.create_task(tts_player.stop()))
         logger.info("已提交停止TTS播放任务")
    elif hasattr(tts_player, 'cleanup') and callable(tts_player.cleanup):
         tasks.append(asyncio.create_task(tts_player.cleanup())) # 或者可能是 cleanup
         logger.info("已提交清理TTS播放器任务")


    if tasks:
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info("资源清理任务完成")
    else:
        logger.info("没有需要清理的播放资源")


# --- 修改主聊天函数 ---
async def chat_with_llm():
    config_loader = ConfigLoader()
    llm_config_dict = config_loader.get_config()['llm']
    client = LLMClient(llm_config_dict)
    tools = build_tools_schema()
    system_prompt = llm_config_dict.get("system_prompt", "你是一个默认的中文助手。")

    messages = [
        {"role": "system", "content": system_prompt}
    ]

    # 获取音乐播放器实例 (保持不变)
    music_player = music_player_instance.get_player()

    print("\n欢迎使用 LLM + TTS 测试")
    print("输入 'exit' 或 'quit' 退出程序")
    print("输入 'stop' 停止当前音乐播放")
    print("输入 'help' 查看帮助信息\n")

    try:
        while True:
            try:
                user_input = input("用户: ").strip()
                if not user_input: continue

                # 处理特殊命令 (保持不变)
                if user_input.lower() in ("exit", "quit"): break
                elif user_input.lower() == "stop":
                    if music_player_instance.is_playing():
                        await music_player_instance.stop_music()
                        print("已停止音乐播放")
                    else:
                        print("当前没有正在播放的音乐")
                    continue
                elif user_input.lower() == "help":
                    # ... (help 信息不变) ...
                    print("\n可用命令：")
                    print("  exit/quit - 退出程序")
                    print("  stop      - 停止当前音乐播放")
                    print("  help      - 显示帮助信息")
                    print("\n其他输入将作为对话内容发送给AI，AI的回应将通过TTS播放")
                    continue

                messages.append({"role": "user", "content": user_input})
                print("AI:", end="", flush=True)

                tool_call_info = None
                full_response_text = "" # 用于累积AI的文本响应
                log_player_state(music_player, "MusicPlayer State Before LLM") # 记录音乐播放器状态
                log_player_state(tts_player, "TTSPlayer State Before LLM")     # 记录TTS播放器状态

                try:
                    # 第一个LLM调用：获取初步响应或工具调用请求
                    async for chunk in client.stream_chat(messages, tools=tools):
                        content = chunk.get("content")
                        tool_calls = chunk.get("tool_calls")
                        if content:
                            print(content, end="", flush=True)
                            full_response_text += content # 累积文本
                        if tool_calls:
                            print(f"\n[工具调用请求]: {tool_calls}")
                            tool_call_info = tool_calls # 仅记录，不累积
                        # finish_reason = chunk.get("finish_reason") # 可以记录完成原因
                        await asyncio.sleep(0)
                except asyncio.CancelledError:
                    logger.info("LLM 响应被取消")
                    break

                print() # 换行

                # 如果没有工具调用，并且有文本响应，则进行 TTS
                if not tool_call_info and full_response_text:
                    await synthesize_and_play_tts(full_response_text)

                # 处理工具调用 (逻辑保持不变)
                elif tool_call_info:
                    log_player_state(music_player, "MusicPlayer State Before Tool Call")
                    log_player_state(tts_player, "TTSPlayer State Before Tool Call")
                    tool_msgs = []
                    # ... (工具调用循环，与 test_llm.py 相同) ...
                    for call in tool_call_info:
                        tool_call_id = call.get("id")
                        function_name = call.get("function", {}).get("name")
                        arguments = call.get("function", {}).get("arguments")
                        print(f"[本地调用] {function_name}({arguments}) ...")
                        try:
                            args = json.loads(arguments) if arguments else {}
                        except Exception as e:
                            print(f"参数解析失败: {e}")
                            args = {}
                        try:
                            # !! 注意: 如果工具调用本身需要播放音频 (如音乐播放),
                            # !! 它会使用 music_player_instance
                            result = await FunctionRegistry.call(function_name, **args)
                            log_player_state(music_player, f"MusicPlayer State After Tool Call: {function_name}")
                            log_player_state(tts_player, f"TTSPlayer State After Tool Call: {function_name}")
                            tool_result = str(result) if result is not None else "工具执行完成"
                        except Exception as e:
                            logger.error(f"工具调用 {function_name} 失败: {e}", exc_info=True)
                            tool_result = f"工具调用错误: {e}"
                            log_player_state(music_player, f"MusicPlayer State After Tool Call Error: {function_name}")
                            log_player_state(tts_player, f"TTSPlayer State After Tool Call Error: {function_name}")

                        tool_msgs.append({
                            "role": "tool",
                            "tool_call_id": tool_call_id,
                            "content": tool_result,
                            "name": function_name
                        })

                    # 追加消息 (保持不变)
                    messages.append({
                        "role": "assistant",
                        "content": None,
                        "tool_calls": tool_call_info,
                    })
                    messages.extend(tool_msgs)

                    # 日志点
                    log_player_state(music_player, "MusicPlayer State Before AI Summary")
                    log_player_state(tts_player, "TTSPlayer State Before AI Summary")
                    print("[AI 总结]:", end="", flush=True)

                    summary_text = "" # 用于累积总结文本
                    last_summary_finish_reason = None
                    try:
                        # 第二个LLM调用：获取工具调用后的总结
                        async for chunk in client.stream_chat(messages, tools=tools):
                            content = chunk.get("content")
                            error_hint = chunk.get("hint")
                            if error_hint: print(f"\n[总结错误]: {error_hint}")
                            if content:
                                print(content, end="", flush=True)
                                summary_text += content # 累积总结文本
                            if chunk.get("finish_reason"):
                                last_summary_finish_reason = chunk.get("finish_reason")
                    except asyncio.CancelledError:
                        logger.info("AI 总结被取消")
                        break

                    print() # 换行
                    if last_summary_finish_reason: print(f"[总结结束原因]: {last_summary_finish_reason}")
                    else: print("[总结结束原因]: 未获取到")

                    # 对总结进行 TTS
                    if summary_text:
                        await synthesize_and_play_tts(summary_text)

                    log_player_state(music_player, "MusicPlayer State After AI Summary")
                    log_player_state(tts_player, "TTSPlayer State After AI Summary")

                print("\n-----------------------------\n")

            except KeyboardInterrupt:
                print("\n检测到中断信号，请输入 'exit' 或 'quit' 退出程序")
                continue

    except Exception as e:
        logger.error(f"聊天过程中发生错误: {e}", exc_info=True)
    finally:
        await cleanup_resources()
        print("\n程序已退出。感谢使用！")

# --- 主程序入口 (保持不变) ---
if __name__ == "__main__":
    try:
        asyncio.run(chat_with_llm())
    except KeyboardInterrupt:
        print("\n程序被中断，正在退出...")
    except Exception as e:
        logger.error(f"程序运行出错: {e}", exc_info=True)
    finally:
        print("再见！")
