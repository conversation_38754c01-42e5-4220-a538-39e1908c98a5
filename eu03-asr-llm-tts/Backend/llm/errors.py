import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

# 错误码到中文提示的映射
VOLCENGINE_ERROR_HINTS = {
    "AccountOverdueError": "火山引擎账户已欠费，请前往火山引擎费用中心充值。",
    "AccessDenied": "无权访问该资源，请检查 API Key 权限或联系管理员检查白名单设置。",
    "AuthenticationError": "API Key 或 AK/SK 无效或缺失，请检查配置。",
    "MissingParameter": "请求缺少必要参数，请检查代码或联系开发者。",
    "InvalidParameter": "请求包含非法参数，请检查代码或联系开发者。",
    "InvalidEndpoint.ClosedEndpoint": "请求的推理接入点已关闭或暂时不可用，请稍后重试或联系管理员。",
    "InvalidEndpointOrModel.NotFound": "模型或推理接入点不存在，或无权访问，请检查模型名称配置或联系管理员。",
    "ModelNotOpen": "当前账号尚未开通该模型服务，请前往火山方舟控制台开通。",
    "RateLimitExceeded.EndpointRPMExceeded": "请求频率 (RPM) 已超出接入点限制，请稍后重试或检查限流设置。",
    "RateLimitExceeded.EndpointTPMExceeded": "Token 数量 (TPM) 已超出接入点限制，请稍后重试或检查限流设置。",
    "ModelAccountRpmRateLimitExceeded": "请求频率 (RPM) 已超出账户模型限制，请稍后重试或联系平台技术支持。",
    "ModelAccountTpmRateLimitExceeded": "Token 数量 (TPM) 已超出账户模型限制，请稍后重试或联系平台技术支持。",
    "QuotaExceeded": "免费试用额度已耗尽，请前往火山方舟控制台开通模型服务。",
    "ModelLoadingError": "模型正在加载中，请稍后重试。",
    "ServerOverloaded": "服务资源紧张，请稍后重试。",
    "SetLimitExceeded": "已达到设置的推理限额，请前往火山方舟控制台调整限额或关闭安心体验模式。",
    "InternalServiceError": "火山引擎内部服务错误，请稍后重试或联系技术支持。",
    "NetworkError": "网络连接错误，请检查网络连接或 API URL 配置。",
    "StreamProcessingError": "处理流式响应时发生内部错误。", # 添加一个处理流内部错误的码
    "UnknownError": "发生未知错误，请检查日志。"
}

def get_error_hint(error_data: Dict[str, Any]) -> str:
    """根据错误字典生成用户友好的提示信息"""
    error_code = error_data.get("code", "UnknownError")
    # 对 HTTP 错误码也尝试映射
    if isinstance(error_code, str) and error_code.startswith("HTTP_"):
         http_status = error_code.split("_")[-1]
         if http_status == "401":
             error_code = "AuthenticationError"
         elif http_status == "403":
             # 403 可能对应多种情况，没有具体 code 时给通用提示
             error_code = "AccessDenied"
         elif http_status == "404":
             error_code = "InvalidEndpointOrModel.NotFound"
         elif http_status == "429":
             error_code = "RateLimitExceeded.EndpointRPMExceeded" # 给一个常见的限流提示
         # 其他 HTTP 错误码暂不特殊处理，使用 UnknownError

    hint = VOLCENGINE_ERROR_HINTS.get(error_code, VOLCENGINE_ERROR_HINTS["UnknownError"])
    message = error_data.get("message", "No specific message provided.")
    # 尝试从 message 中提取 Request ID
    request_id_part = ""
    if isinstance(message, str):
        if 'Request ID:' in message:
            request_id_part = f" Request ID: {message.split('Request ID:')[-1].strip()}"
        elif 'Request id:' in message: # 兼容小写 id
             request_id_part = f" Request ID: {message.split('Request id:')[-1].strip()}"

    return f"{hint} (错误码: {error_data.get('code', 'N/A')}{request_id_part})"

# 自定义异常类
class LLMClientError(Exception):
    def __init__(self, hint: str, error_data: Dict[str, Any]):
        super().__init__(hint)
        self.hint = hint
        self.error_data = error_data 