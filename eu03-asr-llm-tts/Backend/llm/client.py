import httpx
import asyncio
import logging
import json
import os # Added import
from typing import AsyncGenerator, Dict, Any, List, Optional, Union

# 从 llm.errors 导入
from .errors import get_error_hint, LLMClientError

logger = logging.getLogger(__name__)

class LLMClient:
    def __init__(self, config: Dict[str, Any]):
        volc = config['volcengine']
        self.api_url = volc['api_url'].rstrip('/') + '/api/v3/chat/completions'
        self.api_key = os.getenv("VOLCENGINE_LLM_API_KEY", volc.get('api_key')) # Prefer env var
        self.model = config['model']['name']
        self.parameters = config.get('parameters', {})
        self.timeout = config.get('timeout', 30.0)

    async def stream_chat(
        self,
        messages: List[Dict[str, Any]],
        stream: bool = True,
        **kwargs
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式对话，yield每个流块的完整结构，包含错误提示。
        错误时 yield {"error": ..., "hint": ...}
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            **self.parameters,
            **kwargs
        }

        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                async with client.stream("POST", self.api_url, headers=headers, json=payload) as response:
                    # 检查状态码，如果不是200，则读取错误信息并停止
                    if response.status_code != 200:
                        logger.error(f"请求失败，状态码: {response.status_code}")
                        error_data = {"code": f"HTTP_{response.status_code}", "message": "<Failed to read error body>"}
                        try:
                            error_details = await response.aread()
                            error_text = error_details.decode()
                            logger.error(f"服务器响应: {error_text}")
                            try:
                                error_json = json.loads(error_text)
                                # 提取火山引擎标准错误结构
                                if "error" in error_json and isinstance(error_json["error"], dict):
                                    error_data = error_json["error"]
                                    # 确保 code 和 message 存在
                                    error_data["code"] = error_data.get("code", f"HTTP_{response.status_code}")
                                    error_data["message"] = error_data.get("message", error_text)
                                else:
                                    # 如果不是标准结构，也记录下来
                                    error_data["message"] = error_text
                            except json.JSONDecodeError:
                                error_data["message"] = error_text # 保留原始文本
                        except Exception as read_err:
                            logger.error(f"读取或解析错误响应体失败: {read_err}")

                        hint = get_error_hint(error_data)
                        yield {"error": error_data, "hint": hint}
                        return

                    # 状态码为 200，正常处理流式响应
                    async for line in response.aiter_lines():
                        if not line or not line.startswith("data: "):
                            continue
                        data = line[len("data: "):].strip()
                        if data == "[DONE]":
                            break
                        try:
                            obj = json.loads(data)
                            # 检查是否是流式错误块
                            if obj.get("error") and isinstance(obj["error"], dict):
                                error_data = obj["error"]
                                logger.error(f"流式传输中收到错误: {error_data}")
                                hint = get_error_hint(error_data)
                                yield {"error": error_data, "hint": hint}
                                continue

                            # 处理正常的流式块 (省略具体逻辑...)
                            if obj.get("object") == "chat.completion.chunk":
                                choices = obj.get("choices", [])
                                usage = obj.get("usage", None)
                                for choice in choices:
                                    delta = choice.get("delta", {})
                                    yield {
                                        "content": delta.get("content"),
                                        "reasoning_content": delta.get("reasoning_content"),
                                        "tool_calls": delta.get("tool_calls"),
                                        "finish_reason": choice.get("finish_reason"),
                                        "index": choice.get("index"),
                                        "logprobs": choice.get("logprobs"),
                                        "usage": usage,
                                        "raw": obj
                                    }
                            elif obj.get("object") == "usage" or (obj.get("usage") and not obj.get("choices")):
                                yield {
                                    "content": None,
                                    "reasoning_content": None,
                                    "tool_calls": None,
                                    "finish_reason": None,
                                    "index": None,
                                    "logprobs": None,
                                    "usage": obj.get("usage"),
                                    "raw": obj
                                }
                            # ... 其他块处理
                        except json.JSONDecodeError as e:
                            logger.warning(f"解析流式 JSON 失败: {e}, data: {data}")
                        except Exception as e:
                            logger.error(f"处理流式块时发生错误: {e}")
                            error_data = {"code": "StreamProcessingError", "message": str(e)}
                            hint = get_error_hint(error_data)
                            yield {"error": error_data, "hint": hint}

        except httpx.RequestError as e:
            logger.error(f"请求过程中发生网络错误: {e}")
            error_data = {"code": "NetworkError", "message": str(e)}
            hint = get_error_hint(error_data)
            yield {"error": error_data, "hint": hint}
        except Exception as e:
            logger.error(f"请求过程中发生未知错误: {e}")
            error_data = {"code": "UnknownError", "message": str(e)}
            hint = get_error_hint(error_data)
            yield {"error": error_data, "hint": hint}

    async def chat(
        self,
        messages: List[Dict[str, Any]],
        stream: bool = False,
        **kwargs
    ) -> Dict[str, Any]:
        """
        非流式对话，返回完整响应结构。错误时抛出带提示的异常。
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "model": self.model,
            "messages": messages,
            "stream": stream,
            **self.parameters,
            **kwargs
        }
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                resp = await client.post(self.api_url, headers=headers, json=payload)

                if resp.status_code != 200:
                    logger.error(f"请求失败，状态码: {resp.status_code}")
                    error_data = {"code": f"HTTP_{resp.status_code}", "message": "<Failed to read error body>"}
                    error_text = "<Failed to read error body>"
                    try:
                        error_details = await resp.aread()
                        error_text = error_details.decode()
                        logger.error(f"服务器响应: {error_text}")
                        try:
                            error_json = json.loads(error_text)
                            if "error" in error_json and isinstance(error_json["error"], dict):
                                error_data = error_json["error"]
                                error_data["code"] = error_data.get("code", f"HTTP_{resp.status_code}")
                                error_data["message"] = error_data.get("message", error_text)
                            else:
                                error_data["message"] = error_text
                        except json.JSONDecodeError:
                            error_data["message"] = error_text
                    except Exception as read_err:
                        logger.error(f"读取或解析错误响应体失败: {read_err}")

                    hint = get_error_hint(error_data)
                    # 抛出包含提示的异常
                    raise LLMClientError(hint, error_data) from None

                # 状态码 200，返回 JSON
                return resp.json()
        except httpx.RequestError as e:
            logger.error(f"请求过程中发生网络错误: {e}")
            error_data = {"code": "NetworkError", "message": str(e)}
            hint = get_error_hint(error_data)
            raise LLMClientError(hint, error_data) from e
        except Exception as e:
            # 捕获上面抛出的 LLMClientError 或其他意外错误
            if not isinstance(e, LLMClientError):
                logger.error(f"请求过程中发生未知错误: {e}")
                error_data = {"code": "UnknownError", "message": str(e)}
                hint = get_error_hint(error_data)
                raise LLMClientError(hint, error_data) from e
            else:
                raise # 直接重新抛出 LLMClientError

# 用法示例
# from utils.config_loader import load_llm_config
# config = load_llm_config()  # 你自己的配置加载方法
# llm = LLMClient(config)
# async for chunk in llm.stream_chat([{"role": "user", "content": "你好"}]):
#     print(chunk["content"], end="", flush=True)
#     # 你也可以处理chunk["tool_calls"], chunk["reasoning_content"], chunk["finish_reason"]等
