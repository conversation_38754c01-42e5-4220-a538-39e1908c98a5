#!/usr/bin/env python3
"""
统一音频配置工具
交互式配置输入输出设备、音量控制，并永久保存配置
"""

import subprocess
import os
import shutil
from typing import Dict, List, Tuple

class UnifiedAudioSetup:
    def __init__(self):
        self.pulse_config_path = "/etc/pulse/default.pa"
        self.backup_path = "/etc/pulse/default.pa.backup"
        
    def run_command(self, cmd: str) -> Dict:
        """运行系统命令"""
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip()
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_playback_devices(self) -> List[Dict]:
        """获取播放设备"""
        result = self.run_command('aplay -l')
        devices = []

        if result['success']:
            for line in result['stdout'].split('\n'):
                if line.startswith('card '):
                    # 解析 "card 0: UACDemoV10 [UACDemoV1.0], device 0: USB Audio [USB Audio]"
                    try:
                        # 分割获取card信息
                        card_part = line.split(',')[0]  # "card 0: UACDemoV10 [UACDemoV1.0]"
                        device_part = line.split(',')[1].strip()  # "device 0: USB Audio [USB Audio]"

                        # 提取card号
                        card_num = card_part.split(':')[0].split()[1]  # "0"
                        # 提取card名称
                        card_name = card_part.split(':')[1].strip().split('[')[0].strip()  # "UACDemoV10"
                        # 提取完整名称
                        card_full_name = card_part.split('[')[1].split(']')[0]  # "UACDemoV1.0"
                        # 提取device号
                        device_num = device_part.split(':')[0].split()[1]  # "0"

                        devices.append({
                            'card': card_num,
                            'device': device_num,
                            'name': card_name,
                            'full_name': card_full_name,
                            'hw_id': f"hw:{card_num},{device_num}",
                            'display_name': f"Card {card_num}: {card_full_name}"
                        })
                    except Exception as e:
                        print(f"解析设备信息失败: {line} - {e}")
                        continue

        return devices
    
    def get_capture_devices(self) -> List[Dict]:
        """获取录音设备"""
        result = self.run_command('arecord -l')
        devices = []

        if result['success']:
            for line in result['stdout'].split('\n'):
                if line.startswith('card '):
                    try:
                        # 分割获取card信息
                        card_part = line.split(',')[0]  # "card 1: Audio [UGREEN CM564 USB Audio]"
                        device_part = line.split(',')[1].strip()  # "device 0: USB Audio [USB Audio]"

                        # 提取card号
                        card_num = card_part.split(':')[0].split()[1]  # "1"
                        # 提取card名称
                        card_name = card_part.split(':')[1].strip().split('[')[0].strip()  # "Audio"
                        # 提取完整名称
                        card_full_name = card_part.split('[')[1].split(']')[0]  # "UGREEN CM564 USB Audio"
                        # 提取device号
                        device_num = device_part.split(':')[0].split()[1]  # "0"

                        devices.append({
                            'card': card_num,
                            'device': device_num,
                            'name': card_name,
                            'full_name': card_full_name,
                            'hw_id': f"hw:{card_num},{device_num}",
                            'display_name': f"Card {card_num}: {card_full_name}"
                        })
                    except Exception as e:
                        print(f"解析录音设备信息失败: {line} - {e}")
                        continue

        return devices
    
    def show_devices(self, devices: List[Dict], device_type: str):
        """显示设备列表"""
        print(f"\n🎵 可用{device_type}设备:")
        for i, device in enumerate(devices):
            print(f"   [{i}] {device['display_name']}")
            print(f"       硬件ID: {device['hw_id']}")
    
    def select_device(self, devices: List[Dict], device_type: str) -> Dict:
        """选择设备"""
        while True:
            try:
                choice = input(f"\n请选择{device_type}设备 [0-{len(devices)-1}]: ").strip()
                if choice == '':
                    return None
                
                index = int(choice)
                if 0 <= index < len(devices):
                    return devices[index]
                else:
                    print("❌ 无效选择，请重新输入")
            except ValueError:
                print("❌ 请输入数字")
    
    def set_volume(self, device_type: str, device: Dict):
        """设置设备音量"""
        print(f"\n🔊 设置{device_type}音量")
        print("建议音量: 50-100% (输入0跳过)")
        
        while True:
            try:
                volume = input("请输入音量百分比 [0-200]: ").strip()
                if volume == '0' or volume == '':
                    return
                
                vol = int(volume)
                if 0 <= vol <= 200:
                    if device_type == "输出":
                        # 设置输出音量
                        result = self.run_command(f"pactl set-sink-volume @DEFAULT_SINK@ {vol}%")
                        if result['success']:
                            print(f"✅ 输出音量已设置为 {vol}%")
                        else:
                            print(f"❌ 设置输出音量失败")
                    else:
                        # 设置输入音量
                        result = self.run_command(f"pactl set-source-volume @DEFAULT_SOURCE@ {vol}%")
                        if result['success']:
                            print(f"✅ 输入音量已设置为 {vol}%")
                        else:
                            print(f"❌ 设置输入音量失败")
                    break
                else:
                    print("❌ 音量范围: 0-200%")
            except ValueError:
                print("❌ 请输入数字")
    
    def set_pulseaudio_defaults(self, output_device: Dict, input_device: Dict):
        """设置PulseAudio默认设备"""
        print(f"\n🔧 设置PulseAudio默认设备...")
        
        if output_device:
            # 查找对应的PulseAudio sink
            result = self.run_command('pactl list sinks short')
            if result['success']:
                for line in result['stdout'].split('\n'):
                    if line.strip() and output_device['hw_id'].replace(':', '_').replace(',', '_') in line:
                        sink_name = line.split('\t')[1]
                        set_result = self.run_command(f"pactl set-default-sink {sink_name}")
                        if set_result['success']:
                            print(f"✅ 已设置默认输出设备: {output_device['display_name']}")
                        break
        
        if input_device:
            # 查找对应的PulseAudio source
            result = self.run_command('pactl list sources short')
            if result['success']:
                for line in result['stdout'].split('\n'):
                    if line.strip() and input_device['hw_id'].replace(':', '_').replace(',', '_') in line and 'monitor' not in line:
                        source_name = line.split('\t')[1]
                        set_result = self.run_command(f"pactl set-default-source {source_name}")
                        if set_result['success']:
                            print(f"✅ 已设置默认输入设备: {input_device['display_name']}")
                        break
    
    def create_persistent_config(self, output_device: Dict, input_device: Dict):
        """创建持久化配置"""
        if os.geteuid() != 0:
            print("\n💾 创建持久化配置...")
            print("⚠️  需要root权限来修改系统配置文件")
            choice = input("是否现在设置持久化配置? [y/N]: ").strip().lower()
            if choice == 'y':
                return self.create_persistent_config_with_sudo(output_device, input_device)
            return False

    def create_persistent_config_with_sudo(self, output_device: Dict, input_device: Dict):
        """使用sudo权限创建持久化配置"""
        try:
            # 创建临时配置脚本
            script_content = f'''#!/bin/bash
# 备份原配置
cp /etc/pulse/default.pa /etc/pulse/default.pa.backup 2>/dev/null || true

# 检查是否已有自定义配置
if grep -q "Custom persistent audio device configuration" /etc/pulse/default.pa; then
    echo "配置已存在，跳过添加"
    exit 0
fi

# 注释掉自动检测模块，使用手动配置
sed -i 's/^load-module module-udev-detect/#load-module module-udev-detect/' /etc/pulse/default.pa

# 在.ifexists块的末尾添加我们的配置
sed -i '/^\.ifexists module-udev-detect\.so$/,/^\.else$/{{
/^\.else$/i\\
\\
### Custom persistent audio device configuration\\
### Load specific devices manually\\
load-module module-alsa-sink device={output_device['hw_id']} sink_name=custom_output\\
load-module module-alsa-source device={input_device['hw_id']} source_name=custom_input\\
\\
### Set as default devices\\
set-default-sink custom_output\\
set-default-source custom_input\\

}}' /etc/pulse/default.pa

echo "持久化配置已添加"
'''

            # 写入临时脚本
            script_path = '/tmp/audio_config_script.sh'
            with open(script_path, 'w') as f:
                f.write(script_content)

            # 设置执行权限
            os.chmod(script_path, 0o755)

            # 使用sudo执行脚本
            result = self.run_command(f'sudo bash {script_path}')

            # 清理临时文件
            os.remove(script_path)

            if result['success']:
                print("✅ 持久化配置已保存")
                print("💡 配置将在下次重启PulseAudio后生效")

                # 询问是否立即重启PulseAudio
                restart_choice = input("是否立即重启PulseAudio使配置生效? [y/N]: ").strip().lower()
                if restart_choice == 'y':
                    # 重启用户的PulseAudio服务
                    restart_result = self.run_command('pulseaudio -k && pulseaudio --start')
                    if restart_result['success']:
                        print("✅ PulseAudio已重启")
                    else:
                        print("⚠️  请手动重启PulseAudio: pulseaudio -k && pulseaudio --start")

                return True
            else:
                print(f"❌ 创建持久化配置失败: {{result.get('stderr', '未知错误')}}")
                return False

        except Exception as e:
            print(f"❌ 创建持久化配置失败: {e}")
            return False


    
    def main_menu(self):
        """主菜单"""
        print("="*60)
        print("    统一音频配置工具")
        print("    配置设备 + 音量控制 + 永久保存")
        print("="*60)

        # 检查是否为root用户
        if os.geteuid() == 0:
            print("⚠️  检测到root权限，但PulseAudio需要在普通用户环境下运行")
            print("💡 请使用普通用户权限运行此脚本")
            print("   脚本会在需要时自动请求sudo权限")
            return

        # 检查PulseAudio
        pulse_check = self.run_command('pulseaudio --check -v')
        if not pulse_check['success']:
            print("❌ PulseAudio未运行")
            print("💡 请尝试启动PulseAudio: pulseaudio --start")
            return
        
        print("✅ PulseAudio正在运行")
        
        # 获取设备列表
        print("\n🔍 扫描音频设备...")
        playback_devices = self.get_playback_devices()
        capture_devices = self.get_capture_devices()
        
        if not playback_devices:
            print("❌ 未找到播放设备")
            return
        
        if not capture_devices:
            print("❌ 未找到录音设备")
            return
        
        # 显示并选择输出设备
        self.show_devices(playback_devices, "输出")
        output_device = self.select_device(playback_devices, "输出")
        
        if not output_device:
            print("❌ 未选择输出设备")
            return
        
        # 显示并选择输入设备
        self.show_devices(capture_devices, "输入")
        input_device = self.select_device(capture_devices, "输入")
        
        if not input_device:
            print("❌ 未选择输入设备")
            return
        
        print(f"\n📋 您的选择:")
        print(f"  输出设备: {output_device['display_name']}")
        print(f"  输入设备: {input_device['display_name']}")
        
        # 设置PulseAudio默认设备
        self.set_pulseaudio_defaults(output_device, input_device)
        
        # 设置音量
        self.set_volume("输出", output_device)
        self.set_volume("输入", input_device)
        
        # 创建持久化配置
        persistent_success = self.create_persistent_config(output_device, input_device)
        
        print(f"\n" + "="*60)
        print("🎉 音频配置完成!")
        
        if persistent_success:
            print("✅ 持久化配置已保存，重启后仍然有效")
        else:
            print("⚠️  当前配置仅在本次会话有效")
            print("💡 如需持久化，请使用root权限重新运行此脚本")
        
        print("="*60)

def main():
    setup = UnifiedAudioSetup()
    setup.main_menu()

if __name__ == "__main__":
    main()
