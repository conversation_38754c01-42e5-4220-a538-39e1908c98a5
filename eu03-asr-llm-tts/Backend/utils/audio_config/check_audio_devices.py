#!/usr/bin/env python3
"""
Linux板卡音频设备检查工具
纯粹检查当前默认输入输出设备
"""

import subprocess
from typing import Dict, Any

class AudioDeviceChecker:
    """音频设备检测器"""

    def __init__(self):
        pass

    def run_command(self, cmd: str) -> Dict[str, Any]:
        """运行系统命令"""
        try:
            result = subprocess.run(
                cmd, shell=True, capture_output=True,
                text=True, timeout=10
            )
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'returncode': result.returncode
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'stdout': '',
                'stderr': ''
            }

    def get_default_devices(self) -> Dict[str, str]:
        """获取默认输入输出设备"""
        devices = {}

        # 获取默认输出设备
        sink_result = self.run_command('pactl get-default-sink')
        if sink_result['success']:
            devices['default_output'] = sink_result['stdout']
        else:
            devices['default_output'] = f"获取失败: {sink_result.get('stderr', '未知错误')}"

        # 获取默认输入设备
        source_result = self.run_command('pactl get-default-source')
        if source_result['success']:
            devices['default_input'] = source_result['stdout']
        else:
            devices['default_input'] = f"获取失败: {source_result.get('stderr', '未知错误')}"

        return devices

    def check_pulseaudio_status(self) -> bool:
        """检查PulseAudio状态"""
        result = self.run_command('pulseaudio --check -v')
        return result['success']

    def get_device_details(self, device_name: str, device_type: str) -> str:
        """获取设备详细信息"""
        if device_type == "sink":
            result = self.run_command(f'pactl list sinks')
        else:
            result = self.run_command(f'pactl list sources')

        if not result['success']:
            return "无法获取详细信息"

        lines = result['stdout'].split('\n')
        in_target_device = False
        device_info = {}

        for line in lines:
            if f"Name: {device_name}" in line:
                in_target_device = True
                continue

            if in_target_device:
                if line.startswith('Sink #') or line.startswith('Source #'):
                    break

                if 'alsa.card_name' in line:
                    device_info['card_name'] = line.split('=')[1].strip().strip('"')
                elif 'alsa.long_card_name' in line:
                    device_info['long_card_name'] = line.split('=')[1].strip().strip('"')
                elif 'device.description' in line:
                    device_info['description'] = line.split('=')[1].strip().strip('"')
                elif 'alsa.card' in line and 'alsa.card_name' not in line:
                    device_info['card_id'] = line.split('=')[1].strip().strip('"')
                elif 'alsa.device' in line:
                    device_info['device_id'] = line.split('=')[1].strip().strip('"')

        if device_info:
            hw_id = f"hw:{device_info.get('card_id', '?')},{device_info.get('device_id', '?')}"
            card_name = device_info.get('card_name', device_info.get('description', '未知设备'))
            return f"{card_name} ({hw_id})"

        return "详细信息不可用"

    def check_default_devices(self):
        """检查并显示默认音频设备"""
        print("="*60)
        print("    Linux板卡默认音频设备检查")
        print("="*60)

        # 检查PulseAudio状态
        pulse_status = self.check_pulseaudio_status()
        print(f"\nPulseAudio状态: {'运行中' if pulse_status else '未运行'}")

        if not pulse_status:
            print("警告: PulseAudio未运行，音频功能不可用")
            return

        # 获取默认设备
        defaults = self.get_default_devices()
        default_output = defaults.get('default_output', '未知')
        default_input = defaults.get('default_input', '未知')

        print(f"\n📊 默认音频设备:")
        print(f"  输出设备: {default_output}")

        # 获取输出设备详细信息
        if default_output != '未知':
            output_details = self.get_device_details(default_output, "sink")
            print(f"  对应硬件: {output_details}")

        print(f"\n  输入设备: {default_input}")

        # 获取输入设备详细信息
        if default_input != '未知':
            input_details = self.get_device_details(default_input, "source")
            print(f"  对应硬件: {input_details}")

        # 显示所有可用设备
        print(f"\n🎵 所有可用设备:")

        # 输出设备
        sinks_result = self.run_command('pactl list sinks short')
        if sinks_result['success']:
            print(f"  输出设备:")
            for line in sinks_result['stdout'].split('\n'):
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        sink_name = parts[1]
                        status = "🔊" if sink_name == default_output else "  "
                        details = self.get_device_details(sink_name, "sink")
                        print(f"    {status} {sink_name}")
                        print(f"      → {details}")

        # 输入设备
        sources_result = self.run_command('pactl list sources short')
        if sources_result['success']:
            print(f"  输入设备:")
            for line in sources_result['stdout'].split('\n'):
                if line.strip() and 'monitor' not in line:
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        source_name = parts[1]
                        status = "🎤" if source_name == default_input else "  "
                        details = self.get_device_details(source_name, "source")
                        print(f"    {status} {source_name}")
                        print(f"      → {details}")

        print("\n" + "="*60)

def main():
    """主函数"""
    checker = AudioDeviceChecker()
    checker.check_default_devices()

if __name__ == "__main__":
    main()
