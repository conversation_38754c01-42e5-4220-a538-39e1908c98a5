# 🎧 音频设备配置工具

专为ASR-LLM-TTS项目设计的Linux音频设备配置工具集，解决USB音频设备配置和重启后配置丢失的问题。

## 🎯 核心功能

- **🎛️ 交互式设备配置**: 菜单选择输入输出设备
- **🔊 音量控制**: 支持0-200%音量设置
- **💾 持久化配置**: 重启后配置不丢失
- **🔍 设备状态检查**: 显示详细的硬件信息
- **🧹 配置管理**: 清理和重置配置

## 📁 工具说明

### 🌟 主要工具

- **`unified_audio_setup.py`** - **统一配置工具**
  - 交互式选择输入输出设备
  - 设置设备音量 (0-200%)
  - 可选择永久保存配置
  - 一站式解决所有配置需求

### 🔧 辅助工具

- **`check_audio_devices.py`** - **设备状态检查**
  - 显示当前默认设备及对应硬件
  - 列出所有可用音频设备
  - 验证配置是否正确

- **`clean_audio_config.py`** - **配置清理工具**
  - 清理错误的持久化配置
  - 重置为系统默认状态
  - 故障排除时使用

### ⚙️ 底层支持

- **`audio_device_config_manager.py`** - **配置管理器**
  - 其他工具的底层支持模块
  - 提供设备扫描和配置功能
  - 用户一般不直接使用

## 🚀 快速开始

### 主要配置流程

```bash
cd Backend

# 1. 检查当前设备状态
python utils/audio_config/check_audio_devices.py

# 2. 配置音频设备 (推荐)
python utils/audio_config/unified_audio_setup.py
```

### 详细使用说明

#### 🎛️ 统一配置工具
```bash
python utils/audio_config/unified_audio_setup.py
```

**功能特点**：
- 🎯 交互式选择输入输出设备
- 🔊 设置设备音量 (0-200%)
- 💾 可选择永久保存配置
- 🔄 解决重启后配置丢失问题
- ✨ 一站式完成所有配置

**使用流程**：
1. 选择输出设备 (扬声器)
2. 选择输入设备 (麦克风)
3. 设置音量
4. 选择是否永久保存

#### 🔍 设备状态检查
```bash
python utils/audio_config/check_audio_devices.py
```

**显示信息**：
- 当前默认设备及对应硬件
- 所有可用音频设备列表
- PulseAudio运行状态

#### 🧹 配置清理 (故障排除)
```bash
python utils/audio_config/clean_audio_config.py
```

**使用场景**：
- 配置出错需要重置
- 切换到其他音频设备
- 故障排除

## ⚠️ 重启后配置丢失问题

### 问题原因
- PulseAudio使用自动检测机制，设备序号可能变化
- USB设备插入顺序影响声卡序号分配
- 临时配置重启后被默认配置覆盖

### 解决方案
使用统一配置工具的持久化功能：
```bash
python utils/audio_config/unified_audio_setup.py
# 在配置过程中选择 "y" 进行持久化配置
```

## 🔧 常见问题

### Q1: 重启后音频设备配置丢失？
**A**: 使用统一配置工具的持久化功能，会修改系统配置文件确保重启后配置保持。

### Q2: 声音很小怎么办？
**A**:
1. 使用统一配置工具设置音量为150%-200%
2. 检查ALSA音量: `alsamixer`
3. 检查PulseAudio音量: `pactl list sinks`

### Q3: 找不到USB音频设备？
**A**:
1. 检查设备连接: `lsusb`
2. 检查ALSA识别: `aplay -l` 和 `arecord -l`
3. 重启PulseAudio: `pulseaudio -k && pulseaudio --start`

### Q4: 配置出错怎么重置？
**A**: 使用清理工具重置配置:
```bash
python utils/audio_config/clean_audio_config.py
```

## 📊 支持的设备

- **UACDemoV1.0** - USB扬声器/耳机
- **UGREEN CM564 USB Audio** - USB麦克风
- **duplex-audio** - 板载音频设备
- 其他标准USB音频设备

## 💡 最佳实践

1. **首次配置**: 使用统一配置工具完成所有设置
2. **定期检查**: 使用检查工具验证配置状态
3. **故障排除**: 先使用清理工具重置，再重新配置
4. **音量调节**: 建议设置在100%-150%范围内

## 📁 文件结构

```
utils/audio_config/
├── unified_audio_setup.py      # 主配置工具
├── check_audio_devices.py      # 状态检查工具
├── clean_audio_config.py       # 配置清理工具
├── audio_device_config_manager.py  # 底层支持模块
└── README.md                   # 本文档
```
