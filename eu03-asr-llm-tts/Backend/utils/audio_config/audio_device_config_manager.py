#!/usr/bin/env python3
"""
音频设备配置管理器
支持灵活配置和管理音频输入输出设备
"""

import subprocess
import logging
import yaml
import os
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AudioDeviceConfigManager:
    """音频设备配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config = None
        self.available_devices = {
            'sinks': [],
            'sources': []
        }
        
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            logger.info(f"已加载配置文件: {self.config_path}")
            return self.config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    

    
    def run_command(self, cmd: str) -> Dict[str, Any]:
        """运行系统命令"""
        try:
            result = subprocess.run(
                cmd, shell=True, capture_output=True, 
                text=True, timeout=10
            )
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout.strip(),
                'stderr': result.stderr.strip(),
                'returncode': result.returncode
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'stdout': '',
                'stderr': ''
            }
    
    def scan_available_devices(self) -> Dict[str, List[Dict]]:
        """扫描可用的音频设备"""
        # 获取输出设备
        sinks_result = self.run_command('pactl list sinks short')
        sinks = []
        if sinks_result['success']:
            for line in sinks_result['stdout'].split('\n'):
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        sinks.append({
                            'index': parts[0],
                            'name': parts[1],
                            'driver': parts[2] if len(parts) > 2 else '',
                            'state': parts[3] if len(parts) > 3 else '',
                            'friendly_name': self._get_friendly_name(parts[1])
                        })
        
        # 获取输入设备
        sources_result = self.run_command('pactl list sources short')
        sources = []
        if sources_result['success']:
            for line in sources_result['stdout'].split('\n'):
                if line.strip() and 'monitor' not in line.lower():  # 排除monitor设备
                    parts = line.split('\t')
                    if len(parts) >= 2:
                        sources.append({
                            'index': parts[0],
                            'name': parts[1],
                            'driver': parts[2] if len(parts) > 2 else '',
                            'state': parts[3] if len(parts) > 3 else '',
                            'friendly_name': self._get_friendly_name(parts[1])
                        })
        
        self.available_devices = {
            'sinks': sinks,
            'sources': sources
        }
        
        return self.available_devices
    
    def _get_friendly_name(self, device_name: str) -> str:
        """获取设备的友好名称"""
        if 'yundea' in device_name.lower() or 'y1076' in device_name.lower():
            return "Yundea 1076 USB声卡"
        elif 'ugreen' in device_name.lower():
            if 'input' in device_name.lower() or 'mono' in device_name.lower():
                return "UGREEN USB麦克风"
            else:
                return "UGREEN USB音频"
        elif 'uacdemo' in device_name.lower():
            return "UACDemoV1.0 USB扬声器"
        elif 'pulse' in device_name.lower():
            return "PulseAudio虚拟设备"
        elif 'duplex' in device_name.lower():
            return "板载音频设备"
        else:
            return device_name.split('.')[0] if '.' in device_name else device_name
    
    def list_devices(self) -> None:
        """列出所有可用设备"""
        if not self.available_devices['sinks'] and not self.available_devices['sources']:
            self.scan_available_devices()
        
        print("\n" + "="*70)
        print("                    可用音频设备列表")
        print("="*70)
        
        print(f"\n🔊 输出设备 (扬声器/耳机):")
        for sink in self.available_devices['sinks']:
            print(f"   [{sink['index']}] {sink['friendly_name']}")
            print(f"      设备名: {sink['name']}")

        print(f"\n🎤 输入设备 (麦克风):")
        for source in self.available_devices['sources']:
            print(f"   [{source['index']}] {source['friendly_name']}")
            print(f"      设备名: {source['name']}")
        
        print("\n" + "="*70)
    
    def get_current_config_devices(self) -> Dict[str, str]:
        """获取当前配置中的设备设置"""
        if not self.config:
            self.load_config()
        
        current = {
            'output_keyword': self.config.get('audio_player', {}).get('device_keyword', ''),
            'input_keyword': self.config.get('asr', {}).get('target_microphone_keyword', ''),
            'output_device': self.config.get('audio_player', {}).get('device'),
        }
        
        return current
    
    def set_output_device(self, device_identifier: str, update_config: bool = False) -> bool:
        """
        设置输出设备
        
        Args:
            device_identifier: 设备标识符（可以是索引、名称或关键词）
            update_config: 是否同时更新配置文件
        """
        if not self.available_devices['sinks']:
            self.scan_available_devices()
        
        # 查找匹配的设备
        target_device = None
        
        # 尝试按索引匹配
        if device_identifier.isdigit():
            for sink in self.available_devices['sinks']:
                if sink['index'] == device_identifier:
                    target_device = sink
                    break
        
        # 尝试按名称或关键词匹配
        if not target_device:
            for sink in self.available_devices['sinks']:
                if (device_identifier.lower() in sink['name'].lower() or 
                    device_identifier.lower() in sink['friendly_name'].lower()):
                    target_device = sink
                    break
        
        if not target_device:
            logger.error(f"未找到匹配的输出设备: {device_identifier}")
            return False
        
        # 设置为PulseAudio默认设备
        result = self.run_command(f'pactl set-default-sink {target_device["name"]}')
        if not result['success']:
            logger.error(f"设置默认输出设备失败: {result.get('stderr', '')}")
            return False
        
        logger.info(f"✅ 已设置输出设备: {target_device['friendly_name']}")

        # 提示用户手动更新配置文件
        keyword = self._extract_keyword(target_device['name'])
        print(f"\n📝 请手动更新 config.yaml 文件:")
        print(f"   在 audio_player 部分设置:")
        print(f"   device_keyword: \"{keyword}\"")
        print(f"   device: null")

        return True
    
    def set_input_device(self, device_identifier: str, update_config: bool = False) -> bool:
        """
        设置输入设备
        
        Args:
            device_identifier: 设备标识符（可以是索引、名称或关键词）
            update_config: 是否同时更新配置文件
        """
        if not self.available_devices['sources']:
            self.scan_available_devices()
        
        # 查找匹配的设备
        target_device = None
        
        # 尝试按索引匹配
        if device_identifier.isdigit():
            for source in self.available_devices['sources']:
                if source['index'] == device_identifier:
                    target_device = source
                    break
        
        # 尝试按名称或关键词匹配
        if not target_device:
            for source in self.available_devices['sources']:
                if (device_identifier.lower() in source['name'].lower() or 
                    device_identifier.lower() in source['friendly_name'].lower()):
                    target_device = source
                    break
        
        if not target_device:
            logger.error(f"未找到匹配的输入设备: {device_identifier}")
            return False
        
        # 设置为PulseAudio默认设备
        result = self.run_command(f'pactl set-default-source {target_device["name"]}')
        if not result['success']:
            logger.error(f"设置默认输入设备失败: {result.get('stderr', '')}")
            return False
        
        logger.info(f"✅ 已设置输入设备: {target_device['friendly_name']}")

        # 提示用户手动更新配置文件
        keyword = self._extract_keyword(target_device['name'])
        print(f"\n📝 请手动更新 config.yaml 文件:")
        print(f"   在 asr 部分设置:")
        print(f"   target_microphone_keyword: \"{keyword}\"")

        return True
    
    def _extract_keyword(self, device_name: str) -> str:
        """从设备名称提取关键词"""
        if 'yundea' in device_name.lower() or 'y1076' in device_name.lower():
            return 'Yundea'
        elif 'ugreen' in device_name.lower():
            return 'UGREEN'
        elif 'uacdemo' in device_name.lower() or 'uacdemo' in device_name.lower():
            return 'UACDemoV1.0'
        elif 'pulse' in device_name.lower():
            return 'pulse'
        elif 'duplex' in device_name.lower():
            return 'duplex'
        else:
            # 提取设备名称的主要部分
            parts = device_name.split('.')
            if len(parts) > 1:
                return parts[1].split('-')[0] if '-' in parts[1] else parts[1]
            return device_name.split('-')[0]
    
    def interactive_setup(self) -> bool:
        """交互式设备配置"""
        print("\n" + "="*70)
        print("                 音频设备交互式配置")
        print("="*70)
        
        # 扫描设备
        print("\n正在扫描音频设备...")
        self.scan_available_devices()
        
        # 显示当前配置
        current = self.get_current_config_devices()
        print(f"\n📋 当前配置:")
        print(f"   输出设备关键词: {current['output_keyword'] or '未设置'}")
        print(f"   输入设备关键词: {current['input_keyword'] or '未设置'}")
        
        # 列出可用设备
        self.list_devices()
        
        # 配置输出设备
        print(f"\n🔊 配置输出设备 (扬声器/耳机):")
        print("请输入设备索引号 [x]、设备名称或关键词 (回车跳过):")
        output_choice = input("输出设备> ").strip()

        output_keyword = None
        if output_choice:
            if not self.set_output_device(output_choice):
                print("❌ 输出设备设置失败")
                return False
            else:
                # 获取设备关键词用于后续提示
                for sink in self.available_devices['sinks']:
                    if (output_choice.isdigit() and sink['index'] == output_choice) or \
                       (not output_choice.isdigit() and (output_choice.lower() in sink['name'].lower() or
                        output_choice.lower() in sink['friendly_name'].lower())):
                        output_keyword = self._extract_keyword(sink['name'])
                        break

        # 配置输入设备
        print(f"\n🎤 配置输入设备 (麦克风):")
        print("请输入设备索引号 [x]、设备名称或关键词 (回车跳过):")
        input_choice = input("输入设备> ").strip()

        input_keyword = None
        if input_choice:
            if not self.set_input_device(input_choice):
                print("❌ 输入设备设置失败")
                return False
            else:
                # 获取设备关键词用于后续提示
                for source in self.available_devices['sources']:
                    if (input_choice.isdigit() and source['index'] == input_choice) or \
                       (not input_choice.isdigit() and (input_choice.lower() in source['name'].lower() or
                        input_choice.lower() in source['friendly_name'].lower())):
                        input_keyword = self._extract_keyword(source['name'])
                        break

        # 显示完整的配置指导
        if output_choice or input_choice:
            print(f"\n" + "="*60)
            print("📝 配置汇总 - 请手动更新 config.yaml 文件:")
            print("="*60)

            if output_choice and output_keyword:
                print("在 audio_player 部分设置:")
                print(f"  device_keyword: \"{output_keyword}\"")
                print("  device: null")
                print("")

            if input_choice and input_keyword:
                print("在 asr 部分设置:")
                print(f"  target_microphone_keyword: \"{input_keyword}\"")
                print("")

            print("💡 提示: 修改后重启程序使配置生效")
            print("="*60)

        print(f"\n✅ 音频设备配置完成！")
        return True

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='音频设备配置管理器')
    parser.add_argument('--list', action='store_true', help='列出所有可用设备')
    parser.add_argument('--interactive', action='store_true', help='交互式配置')
    parser.add_argument('--output', type=str, help='设置输出设备')
    parser.add_argument('--input', type=str, help='设置输入设备')
    parser.add_argument('--config', type=str, default='config.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    manager = AudioDeviceConfigManager(args.config)
    
    if args.list:
        manager.scan_available_devices()
        manager.list_devices()
    elif args.interactive:
        manager.interactive_setup()
    elif args.output or args.input:
        if args.output:
            manager.set_output_device(args.output)
        if args.input:
            manager.set_input_device(args.input)
    else:
        # 默认显示当前配置
        manager.load_config()
        current = manager.get_current_config_devices()
        print(f"当前输出设备关键词: {current['output_keyword']}")
        print(f"当前输入设备关键词: {current['input_keyword']}")
        print("\n使用 --help 查看更多选项")

if __name__ == "__main__":
    main()
