#!/usr/bin/env python3
"""
MCP核心工具服务器
将现有的核心Function Calling工具迁移到MCP格式
"""

import sys
import logging
import signal
import os
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目路径
backend_root = str(Path(__file__).parent.parent.parent)
sys.path.append(backend_root)

from fastmcp import FastMCP, Context
from mcp_environment import get_environment_info, is_wsl2_environment

# 导入现有的工具实现
try:
    from utils.function_call.function_tools_impl import Calculator
except ImportError:
    # 如果导入失败，提供简单的实现
    class Calculator:
        @staticmethod
        def calculate(op: str, a: float, b: float) -> float:
            if op == 'add':
                return a + b
            elif op == 'sub':
                return a - b
            elif op == 'mul':
                return a * b
            elif op == 'div':
                if b == 0:
                    raise ValueError("除数不能为0")
                return a / b
            else:
                raise ValueError(f"不支持的操作: {op}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取环境信息
env_info = get_environment_info()

# 创建核心工具MCP服务器
mcp = FastMCP(
    name="ASR-LLM-TTS Core Tools",
    instructions=f"""
    ASR-LLM-TTS语音对话助手的核心工具服务器。
    
    运行环境: {env_info.type.value}
    
    提供以下核心功能：
    1. 数学计算器 - 基本四则运算
    2. 系统信息查询 - 获取系统状态和配置
    3. 文本处理 - 字符串操作和格式化
    4. 时间日期 - 时间相关的操作
    
    这些工具在所有环境（WSL2、Linux板卡）中都可用。
    """
)

# ============================================================================
# 数学计算工具
# ============================================================================

@mcp.tool
def calculator(operation: str, a: float, b: float) -> str:
    """执行基本的数学运算
    
    Args:
        operation: 运算类型 (add, sub, mul, div)
        a: 第一个数字
        b: 第二个数字
    
    Returns:
        计算结果的字符串表示
    """
    try:
        result = Calculator.calculate(operation, a, b)
        return f"计算结果: {a} {operation} {b} = {result}"
    except Exception as e:
        return f"计算错误: {str(e)}"

@mcp.tool
def advanced_calculator(expression: str) -> str:
    """执行复杂的数学表达式计算
    
    Args:
        expression: 数学表达式，如 "2 + 3 * 4"
    
    Returns:
        计算结果
    """
    try:
        # 安全的表达式求值（仅允许基本数学运算）
        allowed_chars = set('0123456789+-*/.() ')
        if not all(c in allowed_chars for c in expression):
            return "错误: 表达式包含不允许的字符"
        
        # 使用eval计算（在受控环境中）
        result = eval(expression, {"__builtins__": {}}, {})
        return f"计算结果: {expression} = {result}"
    except Exception as e:
        return f"表达式计算错误: {str(e)}"

# ============================================================================
# 系统信息工具
# ============================================================================

@mcp.tool
async def get_system_status(ctx: Context) -> Dict[str, Any]:
    """获取系统状态信息
    
    Returns:
        系统状态字典
    """
    await ctx.info("正在收集系统状态信息...")
    
    import platform
    import time
    
    status = {
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "environment": env_info.type.value,
        "platform": {
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor()
        },
        "python": {
            "version": platform.python_version(),
            "implementation": platform.python_implementation(),
            "executable": sys.executable
        }
    }
    
    # 尝试获取更多系统信息
    try:
        import psutil
        status["resources"] = {
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
                "available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
                "percent": psutil.virtual_memory().percent
            },
            "disk": {
                "total_gb": round(psutil.disk_usage('/').total / (1024**3), 2),
                "free_gb": round(psutil.disk_usage('/').free / (1024**3), 2),
                "percent": psutil.disk_usage('/').percent
            }
        }
    except ImportError:
        status["resources"] = {"note": "psutil不可用，无法获取详细资源信息"}
    
    await ctx.info("系统状态信息收集完成")
    return status

@mcp.tool
async def check_service_health(service_name: str, ctx: Context) -> Dict[str, Any]:
    """检查服务健康状态
    
    Args:
        service_name: 服务名称 (如 "audio", "network", "storage")
    
    Returns:
        服务健康状态
    """
    await ctx.info(f"检查 {service_name} 服务健康状态...")
    
    health_status = {
        "service": service_name,
        "status": "unknown",
        "details": {},
        "recommendations": []
    }
    
    if service_name == "audio":
        # 检查音频服务
        if env_info.has_audio:
            health_status["status"] = "healthy"
            health_status["details"]["audio_devices"] = "可用"
        else:
            health_status["status"] = "limited"
            health_status["details"]["audio_devices"] = "不可用或受限"
            if is_wsl2_environment():
                health_status["recommendations"].append("WSL2环境中音频功能受限是正常的")
    
    elif service_name == "network":
        # 检查网络服务
        try:
            import socket
            socket.create_connection(("8.8.8.8", 53), timeout=3)
            health_status["status"] = "healthy"
            health_status["details"]["connectivity"] = "正常"
        except:
            health_status["status"] = "unhealthy"
            health_status["details"]["connectivity"] = "网络连接异常"
            health_status["recommendations"].append("检查网络连接")
    
    elif service_name == "storage":
        # 检查存储服务
        try:
            import psutil
            disk_usage = psutil.disk_usage('/')
            free_percent = (disk_usage.free / disk_usage.total) * 100
            
            if free_percent > 20:
                health_status["status"] = "healthy"
            elif free_percent > 10:
                health_status["status"] = "warning"
                health_status["recommendations"].append("磁盘空间不足，建议清理")
            else:
                health_status["status"] = "critical"
                health_status["recommendations"].append("磁盘空间严重不足，需要立即清理")
            
            health_status["details"]["free_space_percent"] = round(free_percent, 2)
        except:
            health_status["status"] = "unknown"
            health_status["details"]["error"] = "无法获取磁盘信息"
    
    else:
        health_status["status"] = "unknown"
        health_status["details"]["error"] = f"未知的服务类型: {service_name}"
    
    await ctx.info(f"{service_name} 服务健康检查完成")
    return health_status

# ============================================================================
# 文本处理工具
# ============================================================================

@mcp.tool
def text_processor(text: str, operation: str, target: str = " ", old: str = "", new: str = "") -> str:
    """文本处理工具

    Args:
        text: 要处理的文本
        operation: 操作类型 (upper, lower, title, reverse, count, replace)
        target: 计数操作的目标字符（默认为空格）
        old: 替换操作中要被替换的文本
        new: 替换操作中的新文本

    Returns:
        处理后的文本
    """
    try:
        if operation == "upper":
            return text.upper()
        elif operation == "lower":
            return text.lower()
        elif operation == "title":
            return text.title()
        elif operation == "reverse":
            return text[::-1]
        elif operation == "count":
            count = text.count(target)
            return f"'{target}' 在文本中出现 {count} 次"
        elif operation == "replace":
            if not old:
                return "错误: 需要指定要替换的文本 (old参数)"
            result = text.replace(old, new)
            return result
        else:
            return f"错误: 不支持的操作 '{operation}'"
    except Exception as e:
        return f"文本处理错误: {str(e)}"

# ============================================================================
# 时间日期工具
# ============================================================================

@mcp.tool
def datetime_tool(operation: str, format_str: str = "%Y-%m-%d %H:%M:%S",
                 timestamp: float = 0.0, date_string: str = "",
                 days: int = 0, hours: int = 0, minutes: int = 0) -> str:
    """时间日期工具

    Args:
        operation: 操作类型 (now, format, parse, add)
        format_str: 时间格式字符串
        timestamp: 时间戳（用于format操作）
        date_string: 日期字符串（用于parse操作）
        days: 添加的天数（用于add操作）
        hours: 添加的小时数（用于add操作）
        minutes: 添加的分钟数（用于add操作）

    Returns:
        时间日期结果
    """
    try:
        import datetime

        if operation == "now":
            return datetime.datetime.now().strftime(format_str)

        elif operation == "format":
            if timestamp > 0:
                dt = datetime.datetime.fromtimestamp(timestamp)
                return dt.strftime(format_str)
            else:
                return "错误: 需要提供有效的timestamp参数"

        elif operation == "parse":
            if date_string:
                dt = datetime.datetime.strptime(date_string, format_str)
                return f"解析结果: {dt.timestamp()}"
            else:
                return "错误: 需要提供date_string参数"

        elif operation == "add":
            now = datetime.datetime.now()
            future = now + datetime.timedelta(days=days, hours=hours, minutes=minutes)
            return f"计算结果: {future.strftime('%Y-%m-%d %H:%M:%S')}"

        else:
            return f"错误: 不支持的操作 '{operation}'"

    except Exception as e:
        return f"时间日期处理错误: {str(e)}"

# ============================================================================
# 资源定义
# ============================================================================

@mcp.resource("config://core_tools")
def get_core_tools_config() -> Dict[str, Any]:
    """核心工具配置信息"""
    return {
        "server_name": "ASR-LLM-TTS Core Tools",
        "version": "1.0.0",
        "environment": env_info.type.value,
        "tools": [
            {
                "name": "calculator",
                "description": "基本数学运算",
                "category": "math"
            },
            {
                "name": "advanced_calculator", 
                "description": "复杂表达式计算",
                "category": "math"
            },
            {
                "name": "get_system_status",
                "description": "系统状态查询",
                "category": "system"
            },
            {
                "name": "check_service_health",
                "description": "服务健康检查",
                "category": "system"
            },
            {
                "name": "text_processor",
                "description": "文本处理",
                "category": "text"
            },
            {
                "name": "datetime_tool",
                "description": "时间日期处理",
                "category": "datetime"
            }
        ],
        "capabilities": {
            "math": True,
            "system_info": True,
            "text_processing": True,
            "datetime": True
        }
    }

@mcp.resource("help://core_tools")
def get_core_tools_help() -> str:
    """核心工具帮助信息"""
    return """
ASR-LLM-TTS 核心工具帮助

数学工具:
- calculator: 基本四则运算 (add, sub, mul, div)
- advanced_calculator: 复杂数学表达式计算

系统工具:
- get_system_status: 获取详细的系统状态信息
- check_service_health: 检查特定服务的健康状态

文本工具:
- text_processor: 文本处理 (upper, lower, title, reverse, count, replace)

时间工具:
- datetime_tool: 时间日期操作 (now, format, parse, add)

所有工具都支持在WSL2和Linux板卡环境中运行。
"""

def setup_signal_handler():
    """设置简单的信号处理器"""
    def signal_handler(signum, frame):
        logger.info("\n收到退出信号，正在关闭MCP核心工具服务器...")
        logger.info("感谢使用ASR-LLM-TTS MCP核心工具服务器！")
        # 直接退出，不做复杂处理
        os._exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def main():
    """主函数"""
    logger.info(f"启动核心工具MCP服务器 (环境: {env_info.type.value})...")

    # 设置信号处理器
    setup_signal_handler()

    # 显示环境信息
    logger.info(f"平台: {env_info.platform} {env_info.architecture}")
    logger.info(f"内存: {env_info.memory_mb}MB")
    logger.info(f"音频支持: {env_info.has_audio}")
    logger.info(f"GPIO支持: {env_info.has_gpio}")

    logger.info("MCP核心工具服务器准备就绪")
    logger.info("按 Ctrl+C 退出服务器")

    try:
        mcp.run()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器运行出错: {e}")
        return 1
    finally:
        logger.info("MCP核心工具服务器已关闭")

    return 0

if __name__ == "__main__":
    exit(main())
