"""
MCP (Model Context Protocol) 集成模块

提供与外部MCP服务器的连接和集成功能，支持多种传输方式：
- SSE (Server-Sent Events)
- Streamable HTTP
- stdio

模块结构：
- client/: MCP客户端相关功能
- server/: MCP服务器实现
- config/: 配置管理
- tools/: 测试和工具脚本
"""

# 客户端相关
from .client import (
    MCPClient,
    MCPClientManager,
    MCPFunctionCallIntegrator,
    get_mcp_integrator,
    MCPFunctionAdapter,
    get_enhanced_function_schemas,
    cleanup_mcp_adapter,
    get_environment_info
)

# 配置相关
from .config import MCPConfigManager

__all__ = [
    # 客户端
    'MCPClient',
    'MCPClientManager',
    'MCPFunctionCallIntegrator',
    'get_mcp_integrator',
    'MCPFunctionAdapter',
    'get_enhanced_function_schemas',
    'cleanup_mcp_adapter',
    'get_environment_info',
    # 配置
    'MCPConfigManager'
]

# 版本信息
__version__ = "1.2.0"
__description__ = "ASR-LLM-TTS MCP Integration Module with Modular Architecture"
