#!/usr/bin/env python3
"""
最终MCP测试脚本
干净、简洁的MCP服务器测试，无异步清理错误
"""

import asyncio
import logging
import json
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志 - 只显示重要信息
logging.basicConfig(level=logging.ERROR)

async def test_mcp_server(name: str, config: dict) -> dict:
    """测试单个MCP服务器"""
    result = {
        "name": name,
        "success": False,
        "tools_count": 0,
        "tools": [],
        "error": None
    }

    client = None
    try:
        from utils.mcp.client.mcp_client_manager import MCPClient

        client = MCPClient()

        # 连接服务器
        await asyncio.wait_for(
            client.connect_to_server(config, name),
            timeout=30.0
        )

        # 获取工具
        tools_result = await asyncio.wait_for(
            client.list_tools_and_save(),
            timeout=15.0
        )

        if hasattr(tools_result, 'tools') and tools_result.tools:
            result["tools_count"] = len(tools_result.tools)
            result["tools"] = [
                {
                    "name": tool.name,
                    "description": tool.description[:80] + "..." if len(tool.description) > 80 else tool.description
                }
                for tool in tools_result.tools
            ]

        result["success"] = True

    except Exception as e:
        result["error"] = str(e)

    finally:
        # 安全清理客户端
        if client:
            try:
                # 使用asyncio.shield保护清理操作
                await asyncio.shield(client.cleanup())
            except Exception:
                pass  # 忽略清理错误

    return result

async def main():
    """主函数"""
    print("🚀 MCP服务器最终测试")
    print("=" * 40)
    
    # 读取配置 - 从config目录查找
    config_file = Path(__file__).parent.parent / "config" / "mcp_servers.json"
    
    with open(config_file, 'r', encoding='utf-8') as f:
        config = json.load(f)
        
    servers = config.get("mcpServers", {})
    active_servers = {name: cfg for name, cfg in servers.items() if cfg.get("active", False)}
    
    print(f"📋 测试 {len(active_servers)} 个活跃服务器\n")
    
    # 并发测试所有服务器
    tasks = [
        test_mcp_server(name, server_config)
        for name, server_config in active_servers.items()
    ]
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 过滤异常结果
    valid_results = [r for r in results if isinstance(r, dict)]
    
    # 显示结果
    total_tools = 0
    successful_servers = []
    failed_servers = []

    for result in valid_results:
        name = result["name"]
        tools_count = result["tools_count"]
        total_tools += tools_count

        if result["success"]:
            successful_servers.append(name)
            print(f"✅ {name}: {tools_count} 个工具")
            for tool in result["tools"]:
                print(f"   • {tool['name']}: {tool['description']}")
        else:
            failed_servers.append(name)
            error_msg = result.get("error", "未知错误")
            print(f"❌ {name}: 连接失败")
            print(f"   错误: {error_msg}")
            # 提供连接提示
            server_config = active_servers.get(name, {})
            server_url = server_config.get("url", "未知URL")
            print(f"   URL: {server_url}")
            if "***********" in server_url:
                print(f"   💡 提示: 请确保 {name} 服务器正在运行")
        print()
    
    # 统计
    successful = len(successful_servers)
    total = len(valid_results)

    print("=" * 40)
    print(f"📊 测试完成")
    print(f"   成功: {successful}/{total} 服务器")
    print(f"   总工具数: {total_tools}")

    if successful_servers:
        print(f"✅ 成功连接的服务器: {', '.join(successful_servers)}")

    if failed_servers:
        print(f"❌ 连接失败的服务器: {', '.join(failed_servers)}")
        print("💡 解决方案:")
        for server in failed_servers:
            if server in ["vision-arm-control", "llm-vision-control"]:
                print(f"   • 启动 {server} 服务器")

    if successful == total:
        print("🎉 所有服务器连接成功！")
    else:
        print(f"⚠️ {total - successful} 个服务器连接失败")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️ 测试被中断")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
