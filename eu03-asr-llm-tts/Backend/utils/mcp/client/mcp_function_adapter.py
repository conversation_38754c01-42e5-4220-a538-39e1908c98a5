"""
MCP Function Calling适配器
将MCP集成器适配到现有的Function Calling系统中
提供与FunctionRegistry兼容的接口
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Callable

# 项目内部导入
from .mcp_integration import get_mcp_integrator, MCPFunctionCallIntegrator

# 安全导入Function Calling模块
try:
    from ...function_call.function_call_tools import FunctionRegistry
    FUNCTION_REGISTRY_AVAILABLE = True
except (ImportError, OSError) as e:
    logging.warning(f"Function Calling模块导入失败: {e}")
    class FunctionRegistry:
        @classmethod
        async def call(cls, name, **kwargs):
            raise RuntimeError("Function Calling功能在当前环境中不可用")
    FUNCTION_REGISTRY_AVAILABLE = False

logger = logging.getLogger(__name__)

class MCPFunctionAdapter:
    """MCP Function Calling适配器
    
    将MCP工具适配为Function Calling系统可以使用的格式
    """
    
    def __init__(self):
        self.integrator: Optional[MCPFunctionCallIntegrator] = None
        self._initialized = False
        
    async def initialize(self) -> None:
        """初始化适配器"""
        if self._initialized:
            return
            
        logger.info("初始化MCP Function Calling适配器...")
        
        try:
            self.integrator = await get_mcp_integrator()
            self._initialized = True
            logger.info("MCP Function Calling适配器初始化成功")
        except Exception as e:
            logger.error(f"MCP Function Calling适配器初始化失败: {e}")
            raise
            
    async def get_enhanced_tools_schema(self) -> List[Dict[str, Any]]:
        """获取增强的工具schema，包含本地工具和MCP工具"""
        if not self._initialized:
            await self.initialize()
            
        # 获取原有的本地工具schema
        local_schemas = []
        if FUNCTION_REGISTRY_AVAILABLE:
            try:
                from ...function_call.function_schema import get_all_schemas
                local_schemas = get_all_schemas()
            except (ImportError, OSError) as e:
                logger.warning(f"无法获取本地工具schema: {e}")
                local_schemas = []
        
        # 获取MCP工具schema
        mcp_schemas = []
        if self.integrator:
            mcp_tools = self.integrator.get_mcp_tools()
            for tool in mcp_tools:
                if tool.active:
                    # 按照火山引擎API要求的格式构造schema
                    schema = {
                        "type": "function",
                        "function": {
                            "name": tool.name,
                            "description": tool.description,
                            "parameters": tool.parameters
                        }
                    }
                    mcp_schemas.append(schema)
                    
        # 合并schema
        all_schemas = local_schemas + mcp_schemas
        logger.info(f"获取到 {len(local_schemas)} 个本地工具和 {len(mcp_schemas)} 个MCP工具")
        
        return all_schemas
        
    async def call_tool_enhanced(self, tool_name: str, **kwargs) -> Any:
        """增强的工具调用，支持本地工具和MCP工具"""
        if not self._initialized:
            await self.initialize()
            
        # 检查是否为MCP工具（以mcp:开头）
        if tool_name.startswith("mcp:"):
            if not self.integrator:
                raise RuntimeError("MCP集成器未初始化")
            return await self.integrator.call_tool(tool_name, kwargs)
        else:
            # 调用本地工具
            if not FUNCTION_REGISTRY_AVAILABLE:
                raise RuntimeError("Function Registry不可用，无法调用本地工具")
            return await FunctionRegistry.call(tool_name, **kwargs)
            
    def is_mcp_tool(self, tool_name: str) -> bool:
        """检查是否为MCP工具"""
        return tool_name.startswith("mcp:")
        
    async def add_mcp_server(self, name: str, config: dict) -> None:
        """添加MCP服务器"""
        if not self._initialized:
            await self.initialize()
            
        if self.integrator:
            await self.integrator.add_mcp_server(name, config)
            logger.info(f"已添加MCP服务器: {name}")
        else:
            logger.error("MCP集成器未初始化，无法添加服务器")
            
    async def remove_mcp_server(self, name: str) -> None:
        """移除MCP服务器"""
        if not self._initialized:
            return
            
        if self.integrator:
            await self.integrator.remove_mcp_server(name)
            logger.info(f"已移除MCP服务器: {name}")
            
    def get_mcp_servers_status(self) -> Dict[str, Any]:
        """获取MCP服务器状态"""
        if not self._initialized or not self.integrator:
            return {"status": "not_initialized", "servers": []}
            
        mcp_tools = self.integrator.get_mcp_tools()
        servers = {}
        
        for tool in mcp_tools:
            server_name = tool.server_name
            if server_name not in servers:
                servers[server_name] = {
                    "name": server_name,
                    "tools": [],
                    "active_tools": 0,
                    "total_tools": 0
                }
            
            servers[server_name]["tools"].append({
                "name": tool.name,
                "description": tool.description,
                "active": tool.active
            })
            servers[server_name]["total_tools"] += 1
            if tool.active:
                servers[server_name]["active_tools"] += 1
                
        return {
            "status": "initialized",
            "total_servers": len(servers),
            "servers": list(servers.values())
        }
        
    async def cleanup(self) -> None:
        """清理资源"""
        if self.integrator:
            await self.integrator.cleanup()
        self._initialized = False
        logger.info("MCP Function Calling适配器已清理")

# 全局适配器实例
_adapter: Optional[MCPFunctionAdapter] = None

async def get_mcp_adapter() -> MCPFunctionAdapter:
    """获取全局MCP适配器实例"""
    global _adapter
    if _adapter is None:
        _adapter = MCPFunctionAdapter()
        await _adapter.initialize()
    return _adapter

async def cleanup_mcp_adapter() -> None:
    """清理全局MCP适配器"""
    global _adapter
    if _adapter:
        await _adapter.cleanup()
        _adapter = None

# 便捷函数，用于替换现有的Function Calling调用
async def enhanced_function_call(tool_name: str, **kwargs) -> Any:
    """增强的函数调用，自动处理本地工具和MCP工具"""
    adapter = await get_mcp_adapter()
    return await adapter.call_tool_enhanced(tool_name, **kwargs)

async def get_enhanced_function_schemas() -> List[Dict[str, Any]]:
    """获取增强的函数schema列表"""
    adapter = await get_mcp_adapter()
    return await adapter.get_enhanced_tools_schema()

# 用于向后兼容的函数，可以替换现有的FunctionRegistry.call
async def compatible_function_call(tool_name: str, *args, **kwargs) -> Any:
    """向后兼容的函数调用"""
    try:
        adapter = await get_mcp_adapter()
        return await adapter.call_tool_enhanced(tool_name, **kwargs)
    except Exception as e:
        logger.warning(f"MCP增强调用失败，回退到原始调用: {e}")
        if FUNCTION_REGISTRY_AVAILABLE:
            return await FunctionRegistry.call(tool_name, *args, **kwargs)
        else:
            raise RuntimeError("Function Registry和MCP都不可用")
