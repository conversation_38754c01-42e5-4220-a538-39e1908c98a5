# MCP (Model Context Protocol) 集成模块

## 🎯 概述

ASR-LLM-TTS项目的MCP集成模块，提供与外部MCP服务器的连接和集成功能。采用模块化架构，支持多种传输方式和完整的配置管理。

## 📁 模块结构

```
utils/mcp/
├── README.md                    # 📖 说明文档
├── __init__.py                  # 🚪 主模块入口
├── client/                      # 🔌 MCP客户端相关
│   ├── __init__.py
│   ├── mcp_client_manager.py    # 客户端管理器
│   ├── mcp_integration.py       # 集成模块
│   ├── mcp_function_adapter.py  # Function Calling适配器
│   └── mcp_environment.py       # 环境检测
├── server/                      # 🚀 MCP服务器相关
│   ├── __init__.py
│   └── mcp_core_tools.py        # 核心工具服务器
├── config/                      # ⚙️ 配置相关
│   ├── __init__.py
│   ├── mcp_servers.json         # 服务器配置
│   ├── mcp_config.yaml          # 详细配置
│   └── mcp_config_manager.py    # 配置管理器
└── tools/                       # 🧪 工具和测试
    ├── __init__.py
    └── final_mcp_test.py         # 测试脚本
```

## ✨ 功能特性

- ✅ **多传输支持**: SSE、Streamable HTTP、stdio
- ✅ **无缝集成**: 与现有Function Calling系统完美融合
- ✅ **环境自适应**: 自动检测WSL2/Linux板卡环境
- ✅ **异步架构**: 高性能异步连接管理和工具调用
- ✅ **完整日志**: 详细的错误处理和调试信息
- ✅ **配置管理**: 便捷的服务器配置和管理工具
- ✅ **模块化设计**: 清晰的代码结构，易于维护和扩展

## 🌐 支持的MCP服务器


### 高德地图服务
- **类型**: SSE
- **功能**: 地图服务和路径规划
- **工具**: 12个地图相关工具（地理编码、路径规划、天气查询等）
- **URL**: https://mcp.api-inference.modelscope.net/14ffca24064c4d/sse

### LLM视觉控制系统
- **类型**: Streamable HTTP
- **功能**: 基于大语言模型的智能视觉控制
- **工具**: `detect_objects`, `get_latest_measurements`, `get_system_status`, `get_video_stream_url`
- **URL**: http://***********:8003/mcp/

## 🚀 快速开始

### 1. 环境要求

```bash
# 安装MCP依赖
pip install mcp fastmcp httpx-sse

# 或使用项目requirements.txt
pip install -r requirements.txt
```

### 2. 使用MCP工具启动器

```bash
# 查看帮助
python mcp_tools.py --help

# 查看所有MCP服务器配置
python mcp_tools.py config list

# 测试所有活跃服务器
python mcp_tools.py test

# 启动本地MCP核心工具服务器
python mcp_tools.py core
```

## ⚙️ 配置管理

### 查看服务器配置
```bash
python mcp_tools.py config list
```

### 添加新的MCP服务器
```bash
# 添加SSE类型服务器
python mcp_tools.py config add myserver sse \
  --url https://example.com/sse \
  --description "我的MCP服务器"

# 添加Streamable HTTP服务器
python mcp_tools.py config add context7 streamable-http \
  --url https://mcp.context7.com/mcp \
  --description "Context7文档检索服务器"
```

### 启用/禁用服务器
```bash
# 启用服务器
python mcp_tools.py config enable myserver

# 禁用服务器
python mcp_tools.py config disable myserver
```

### 删除服务器
```bash
python mcp_tools.py config remove myserver
```

## 🧪 测试和调试

### 测试所有活跃服务器
```bash
python mcp_tools.py test
```

### 手动测试单个服务器
```python
from utils.mcp.client import MCPClient

# 创建客户端
client = MCPClient()

# 连接服务器
config = {
    "type": "sse",
    "url": "https://example.com/sse",
    "timeout": 5
}
await client.connect_to_server(config, "test_server")

# 获取工具列表
tools = await client.list_tools_and_save()
print(f"发现 {len(tools.tools)} 个工具")
```

## 🔧 开发和扩展

### 添加新的传输方式
1. 在 `client/mcp_client_manager.py` 中添加新的传输类型
2. 实现相应的连接逻辑
3. 更新配置验证

### 添加新的工具服务器
1. 在 `server/` 目录下创建新的服务器文件
2. 继承基础MCP服务器类
3. 实现工具逻辑

### 自定义配置
编辑 `config/mcp_config.yaml` 文件来自定义默认配置。

## 📝 配置文件格式

### mcp_servers.json
```json
{
  "mcpServers": {
    "server_name": {
      "active": true,
      "type": "sse",
      "url": "https://example.com/sse",
      "transport": "sse",
      "timeout": 5,
      "sse_read_timeout": 300,
      "headers": {
        "Accept": "text/event-stream",
        "Cache-Control": "no-cache"
      },
      "description": "服务器描述"
    }
  }
}
```

## 🐛 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 增加timeout设置
   - 验证服务器URL

2. **导入错误**
   - 确保已安装所有依赖
   - 检查Python路径设置

3. **工具调用失败**
   - 检查工具参数格式
   - 查看详细日志
   - 验证服务器状态

### 调试模式
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🔄 集成到主程序

MCP模块已自动集成到主程序中：

```python
# 在server.py中自动加载MCP增强功能
from utils.mcp import get_enhanced_function_schemas

# 获取包含MCP工具的完整工具列表
tools_schema = await get_enhanced_function_schemas()
```

## 📚 相关文档

- [MCP协议规范](https://modelcontextprotocol.io/)
- [FastMCP文档](https://github.com/jlowin/fastmcp)
- [项目主文档](../../README.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进MCP集成模块！

---

**版本**: 1.2.0
**更新时间**: 2025-07-18
**维护者**: ASR-LLM-TTS团队