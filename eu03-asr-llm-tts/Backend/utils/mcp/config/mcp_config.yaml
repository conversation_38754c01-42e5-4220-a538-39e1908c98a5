# MCP (Model Context Protocol) 配置文件
# 支持WSL2开发环境和Linux板卡部署环境的通用配置

# 环境检测和适配
environment:
  # 自动检测环境类型
  auto_detect: true
  
  # 环境类型: development (WSL2), production (Linux板卡), testing
  type: "auto"  # auto, development, production, testing
  
  # 环境特定配置
  development:  # WSL2开发环境
    enable_audio: false
    enable_hardware: false
    enable_remote_services: true
    log_level: "DEBUG"
    
  production:   # Linux板卡生产环境
    enable_audio: true
    enable_hardware: true
    enable_remote_services: true
    log_level: "INFO"
    
  testing:      # 测试环境
    enable_audio: false
    enable_hardware: false
    enable_remote_services: false
    log_level: "DEBUG"

# MCP服务器配置
mcp_server:
  # 服务器基本信息
  name: "ASR-LLM-TTS MCP Server"
  version: "1.0.0"
  description: "语音对话助手的MCP服务器，支持WSL2和Linux板卡"
  
  # 服务器指令
  instructions: |
    这是ASR-LLM-TTS语音对话助手的MCP服务器。
    
    主要功能：
    1. 基础计算工具
    2. 音频控制和播放 (Linux板卡)
    3. 远程设备控制
    4. 智能视觉控制 (物体检测、视频流)
    5. 系统状态监控
    6. 配置信息访问
    
    环境适配：
    - WSL2: 提供核心MCP功能，用于开发测试
    - Linux板卡: 提供完整功能，包括音频和硬件控制

  # 传输配置
  transport:
    default: "stdio"
    
    http:
      host: "0.0.0.0"  # 允许外部访问
      port: 8001
      path: "/mcp"

  # 模块化服务器配置
  modules:
    core_tools:
      enabled: true
      description: "核心工具：计算器、系统信息"
      
    audio_control:
      enabled_on: ["production"]  # 仅在生产环境启用
      description: "音频控制：音乐播放、TTS控制"
      
    remote_control:
      enabled: true
      description: "远程控制：摄像头、机械臂"
      
    hardware_interface:
      enabled_on: ["production"]  # 仅在生产环境启用
      description: "硬件接口：GPIO、串口"

# MCP客户端配置
mcp_client:
  connection:
    timeout: 30.0
    retry_attempts: 3
    retry_delay: 1.0

  tool_calling:
    timeout: 60.0
    max_concurrent: 5
    retry_on_error: true

# 外部MCP服务器配置
external_mcp_servers:
  # Context7 MCP服务器示例
  context7:
    enabled: true
    type: "streamable-http"
    url: "https://mcp.context7.com/mcp"
    transport: "streamable_http"
    timeout: 30
    sse_read_timeout: 300
    headers: {}
    description: "Context7 MCP服务器，提供代码库上下文和文档检索功能"

  # LLM视觉控制MCP服务器
  llm_vision_control:
    enabled: true
    type: "streamable-http"
    url: "http://***********:8003/mcp/"
    transport: "streamable_http"
    timeout: 30
    sse_read_timeout: 300
    health_check_timeout: 2
    terminate_on_close: true
    headers:
      "Accept": "application/json, text/event-stream"
      "Content-Type": "application/json"
    description: "基于大语言模型的智能视觉控制系统MCP服务器，提供物体检测、视频流等功能"

  # 本地MCP服务器示例
  local_weather:
    enabled: false
    type: "stdio"
    command: "python"
    args: ["weather_server.py"]
    cwd: "./mcp_servers/weather"
    description: "本地天气查询MCP服务器"

  # SSE连接示例
  example_sse:
    enabled: false
    type: "sse"
    url: "https://example.com/mcp"
    transport: "sse"
    timeout: 5
    sse_read_timeout: 300
    headers:
      Authorization: "Bearer your-token-here"
    description: "SSE连接的MCP服务器示例"

  # 自定义HTTP MCP服务器
  custom_http:
    enabled: false
    type: "streamable-http"
    url: "http://localhost:8080/mcp"
    transport: "streamable_http"
    timeout: 15
    sse_read_timeout: 180
    terminate_on_close: true
    headers:
      "X-API-Key": "your-api-key"
      "Content-Type": "application/json"
    description: "自定义HTTP MCP服务器"

# 迁移配置
migration:
  # 迁移模式: gradual (渐进式), full (完全替换)
  mode: "gradual"

  # 向后兼容
  backward_compatibility:
    keep_function_calling: true
    compatibility_duration_days: 30

  # 迁移阶段
  phases:
    phase1:
      name: "核心工具迁移"
      enabled: true
      tools: ["calculator", "system_info"]

    phase2:
      name: "音频控制迁移"
      enabled: false
      tools: ["music_player_*", "tts_*"]
      condition: "production"  # 仅在生产环境

    phase3:
      name: "远程控制迁移"
      enabled: false
      tools: ["yolo_*", "robot_arm_*"]

# 集成配置
integration:
  # 工具命名策略
  tool_naming:
    mcp_prefix: "mcp"  # MCP工具前缀
    separator: ":"     # 分隔符
    format: "{prefix}:{server}:{tool}"  # 格式: mcp:server:tool_name

  # 工具冲突处理
  conflict_resolution:
    strategy: "prefix"  # prefix (添加前缀), rename (重命名), skip (跳过)
    prefer_local: true  # 冲突时优先使用本地工具

  # 自动重连配置
  auto_reconnect:
    enabled: true
    max_attempts: 3
    retry_delay: 5.0
    exponential_backoff: true

  # 工具过滤
  tool_filtering:
    enabled: true
    include_patterns: ["*"]  # 包含的工具模式
    exclude_patterns: []     # 排除的工具模式
    max_tools_per_server: 50 # 每个服务器最大工具数

  # 性能优化
  performance:
    lazy_loading: true       # 延迟加载工具
    cache_tool_schemas: true # 缓存工具schema
    parallel_initialization: true # 并行初始化客户端

# 平台适配配置
platform_adaptation:
  # WSL2特定配置
  wsl2:
    mock_audio_devices: true
    simulate_hardware: true
    enable_development_tools: true
    
  # Linux板卡特定配置
  linux_board:
    audio_system: "alsa"  # alsa, pulseaudio
    gpio_library: "gpiozero"  # RPi.GPIO, gpiozero
    enable_systemd_service: true
    
  # 通用Linux配置
  linux:
    check_audio_devices: true
    validate_hardware_access: true

# 安全配置
security:
  tool_execution:
    dangerous_tools: []
    require_confirmation: []
    execution_timeout: 60.0
    
  resource_access:
    allowed_patterns:
      - "config://*"
      - "status://*"
      - "help://*"
    forbidden_patterns:
      - "secret://*"
      - "private://*"

# 监控配置
monitoring:
  enabled: true
  metrics:
    - "tool_call_duration"
    - "resource_access_time"
    - "memory_usage"
  
  thresholds:
    tool_call_timeout: 30.0
    memory_limit_mb: 512
    
  reporting:
    interval: 300
    log_performance: true

# 开发配置
development:
  debug_mode: true
  hot_reload: true
  
  testing:
    test_data_dir: "./test_data"
    simulate_delay: 0
    coverage_threshold: 80.0
