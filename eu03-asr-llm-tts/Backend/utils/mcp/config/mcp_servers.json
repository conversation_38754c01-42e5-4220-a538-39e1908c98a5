{"mcpServers": {"amap-maps": {"active": true, "type": "sse", "url": "https://mcp.api-inference.modelscope.net/14ffca24064c4d/sse", "transport": "sse", "timeout": 5, "sse_read_timeout": 300, "headers": {"Accept": "text/event-stream", "Cache-Control": "no-cache"}, "description": "高德地图MCP服务器 - SSE连接"}, "vision-arm-control": {"active": true, "type": "streamable-http", "url": "http://***********:8002/mcp/", "transport": "streamable_http", "timeout": 10, "health_check_timeout": 2, "sse_read_timeout": 300, "terminate_on_close": true, "headers": {"Accept": "application/json, text/event-stream", "Content-Type": "application/json"}, "description": "智能视觉机械臂控制系统MCP服务器 - Streamable HTTP连接"}}}