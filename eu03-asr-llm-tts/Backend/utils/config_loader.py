import os
from pathlib import Path
from typing import Any, Dict, Optional
import yaml
from dataclasses import dataclass
import logging
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

@dataclass
class TTSConfig:
    """TTS配置类"""
    app_id: str
    access_token: str
    resource_id: str
    ws_url: str
    max_size: int
    format: str
    sample_rate: int
    default_speaker: str
    available_speakers: list
    user_id: str
    namespace: str

@dataclass
class LLMConfig:
    """LLM配置类"""
    api_key: str
    model: str
    api_url: str
    temperature: float
    top_p: float
    max_tokens: int
    stream: bool = True
    timeout: int = 30

@dataclass
class AudioPlayerConfig:
    """音频播放器配置类"""
    device: Any
    device_keyword: Optional[str]
    blocksize: int
    buffer_size: int
    min_prebuffer: int
    volume: float

@dataclass
class ASRConfig:
    """ASR 配置类"""
    sample_rate: int
    channels: int
    model_dir: str
    num_threads: int
    decoding_method: str
    endpoint_rule1_min_trailing_silence: float
    endpoint_rule2_min_trailing_silence: float
    endpoint_rule3_min_utterance_length: float
    output_dir: str
    save_temp_wav: bool = False
    target_microphone_keyword: str = ""
    model_files: Dict[str, str] = None
    vad_model_path: str = ""
    feature_dim: int = 80

# 新增：YOLO配置类
@dataclass
class YoloConfig:
    """远程 YOLO Demo 控制配置类"""
    base_url: str
    timeout: float

# 新增：音乐播放器配置类
@dataclass
class MusicPlayerConfig:
    """音乐播放器配置类"""
    resource_dir: str
    volume: float
    supported_formats: list

class ConfigLoader:
    """配置加载器"""
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            # 默认配置文件路径
            config_path = str(Path(__file__).parent.parent / "config.yaml")

        self.config_path = config_path
        self._config: Dict[str, Any] = {}

        # Determine the path to the .env file in the Backend directory
        backend_dir = Path(__file__).resolve().parent.parent
        dotenv_path = backend_dir / ".env"
        if dotenv_path.exists():
            logger.info(f"Loading environment variables from: {dotenv_path}")
            load_dotenv(dotenv_path=dotenv_path)
        else:
            logger.warning(f".env file not found at {dotenv_path}. API keys might be missing if not set in environment.")

        self.load_config()

    def load_config(self) -> None:
        """加载配置文件"""
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")

        with open(self.config_path, 'r', encoding='utf-8') as f:
            self._config = yaml.safe_load(f)

    def get_tts_config(self) -> TTSConfig:
        """获取TTS配置"""
        if not self._config or 'tts' not in self._config:
            raise ValueError("配置文件中缺少TTS配置")

        tts_config = self._config['tts']
        volcengine = tts_config['volcengine']
        audio = tts_config['audio']
        voice = tts_config['voice']
        session = tts_config['session']

        return TTSConfig(
            app_id=os.getenv("VOLCENGINE_TTS_APP_ID", volcengine.get('app_id')),
            access_token=os.getenv("VOLCENGINE_TTS_ACCESS_TOKEN", volcengine.get('access_token')),
            resource_id=volcengine['resource_id'],
            ws_url=volcengine['ws_url'],
            max_size=volcengine['max_size'],
            format=audio['format'],
            sample_rate=audio['sample_rate'],
            default_speaker=voice['default_speaker'],
            available_speakers=voice['available_speakers'],
            user_id=session['user_id'],
            namespace=session['namespace']
        )

    def get_llm_config(self) -> LLMConfig:
        """获取LLM配置"""
        if not self._config or 'llm' not in self._config:
            raise ValueError("配置文件中缺少LLM配置")

        llm_config = self._config['llm']
        volcengine = llm_config['volcengine']
        model = llm_config['model']
        parameters = llm_config['parameters']

        return LLMConfig(
            api_key=os.getenv("VOLCENGINE_LLM_API_KEY", volcengine.get('api_key')),
            api_url=volcengine['api_url'],
            model=model['name'],
            temperature=parameters['temperature'],
            top_p=parameters['top_p'],
            max_tokens=parameters['max_tokens'],
            stream=parameters['stream'],
            timeout=parameters.get('timeout', 30)
        )

    def get_audio_player_config(self) -> AudioPlayerConfig:
        """获取音频播放器配置"""
        if not self._config or 'audio_player' not in self._config:
            raise ValueError("配置文件中缺少audio_player配置")
        ap_cfg = self._config['audio_player']
        return AudioPlayerConfig(
            device=ap_cfg.get('device', None),
            device_keyword=ap_cfg.get('device_keyword', None),
            blocksize=ap_cfg.get('blocksize', 1024),
            buffer_size=ap_cfg.get('buffer_size', 50),
            min_prebuffer=ap_cfg.get('min_prebuffer', 2),
            volume=ap_cfg.get('volume', 1.0)
        )

    def get_asr_config(self) -> ASRConfig:
        """获取ASR配置 (支持onnx paraformer+vad方案)"""
        if not self._config or 'asr' not in self._config:
            logger.error("配置文件中缺少ASR配置!")
            raise ValueError("配置文件中缺少ASR配置")

        asr_config = self._config['asr']
        model_dir = asr_config.get('model_dir')
        if not model_dir:
            raise ValueError("ASR 配置中缺少 'model_dir'")

        project_root = Path(__file__).parent.parent
        absolute_model_dir = project_root / model_dir

        # 兼容onnx paraformer方案
        default_model_files = {
            "tokens": "tokens.txt",
            "paraformer": "model.onnx",
            # ncnn参数保留但不强制
            "encoder_param": "encoder_jit_trace-pnnx.ncnn.param",
            "encoder_bin": "encoder_jit_trace-pnnx.ncnn.bin",
            "decoder_param": "decoder_jit_trace-pnnx.ncnn.param",
            "decoder_bin": "decoder_jit_trace-pnnx.ncnn.bin",
            "joiner_param": "joiner_jit_trace-pnnx.ncnn.param",
            "joiner_bin": "joiner_jit_trace-pnnx.ncnn.bin",
        }
        model_files_config = asr_config.get('model_files', {})
        model_files = default_model_files.copy()
        model_files.update(model_files_config)

        tokens_file_path = absolute_model_dir / model_files["tokens"]
        if not tokens_file_path.exists():
            logger.warning(f"ASR 配置文件中指定的 tokens 文件 '{tokens_file_path}' 可能不存在。请检查配置。")

        rule1 = asr_config.get('endpoint_rule1_min_trailing_silence', 2.4)
        rule2 = asr_config.get('endpoint_rule2_min_trailing_silence', 1.2)
        rule3 = asr_config.get('endpoint_rule3_min_utterance_length', 0.3)

        return ASRConfig(
            sample_rate=asr_config.get('sample_rate', 16000),
            channels=asr_config.get('channels', 1),
            model_dir=model_dir,
            num_threads=asr_config.get('num_threads', 4),
            decoding_method=asr_config.get('decoding_method', "greedy_search"),
            endpoint_rule1_min_trailing_silence=rule1,
            endpoint_rule2_min_trailing_silence=rule2,
            endpoint_rule3_min_utterance_length=rule3,
            output_dir=asr_config.get('output_dir', "./output_wavs_asr"),
            save_temp_wav=asr_config.get('save_temp_wav', False),
            target_microphone_keyword=asr_config.get('target_microphone_keyword', ""),
            model_files=model_files,
            vad_model_path=asr_config.get('vad_model_path', ""),
            feature_dim=asr_config.get('feature_dim', 80)
        )

    # 新增：获取YOLO配置的方法
    def get_yolo_config(self) -> YoloConfig:
        """获取远程 YOLO Demo 控制配置"""
        if not self._config or 'remote_yolo' not in self._config:
            raise ValueError("配置文件中缺少 remote_yolo 配置")

        yolo_cfg = self._config['remote_yolo']
        return YoloConfig(
            base_url=yolo_cfg.get('base_url', ''),
            timeout=yolo_cfg.get('timeout', 5.0)
        )

    # 新增：获取音乐播放器配置的方法
    def get_music_player_config(self) -> MusicPlayerConfig:
        """获取音乐播放器配置"""
        if not self._config or 'music_player' not in self._config:
            logger.warning("配置文件中缺少 music_player 配置，使用默认值")
            return MusicPlayerConfig(
                resource_dir="./backend/resource",
                volume=0.7,
                supported_formats=[".mp3", ".wav", ".flac"]
            )

        mp_cfg = self._config['music_player']
        return MusicPlayerConfig(
            resource_dir=mp_cfg.get('resource_dir', './backend/resource'),
            volume=mp_cfg.get('volume', 0.7),
            supported_formats=mp_cfg.get('supported_formats', [".mp3", ".wav", ".flac"])
        )

    def get_input_mode(self) -> str:
        """获取输入模式 ('keyboard' or 'asr')"""
        if not self._config or 'app' not in self._config or 'input_mode' not in self._config['app']:
            logger.warning("配置文件中缺少 app.input_mode 配置，默认为 'keyboard'")
            return "keyboard" # Default to keyboard if not specified

        mode = self._config['app'].get('input_mode', 'keyboard').lower()
        if mode not in ["keyboard", "asr"]:
            logger.warning(f"无效的 input_mode '{mode}'，将使用 'keyboard'")
            return "keyboard"
        return mode

    def update_config(self, new_config: Dict[str, Any]) -> None:
        """更新配置"""
        self._config.update(new_config)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            yaml.safe_dump(self._config, f, allow_unicode=True)

    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config.copy()

def load_config(config_path: Optional[str] = None) -> ConfigLoader:
    """便捷函数：创建并返回配置加载器实例"""
    return ConfigLoader(config_path)