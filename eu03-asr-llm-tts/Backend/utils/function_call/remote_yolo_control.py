# -*- coding: utf-8 -*-
import httpx
import logging
import asyncio
from typing import Optional, Dict, Any

# 导入配置加载器
from utils.config_loader import load_config, YoloConfig

logger = logging.getLogger("REMOTE_YOLO_CONTROL")

# --- 配置 --- 
# 全局变量，用于存储加载的YOLO配置
_yolo_config: Optional[YoloConfig] = None

async def _get_yolo_config() -> YoloConfig:
    """获取或加载 YOLO 配置"""
    global _yolo_config
    if _yolo_config is None:
        try:
            config_loader = load_config() # 使用默认路径加载
            _yolo_config = config_loader.get_yolo_config()
            if not _yolo_config.base_url:
                 logger.error("从配置文件加载的远程YOLO服务地址为空，请检查 config.yaml")
                 raise ValueError("远程YOLO服务地址未配置")
        except Exception as e:
            logger.error(f"加载远程YOLO配置失败: {e}", exc_info=True)
            # 提供一个默认值以允许程序继续运行，但会记录错误
            _yolo_config = YoloConfig(base_url="http://<yolo-service-not-configured>:5001", timeout=5.0)
            # 抛出异常阻止函数调用可能更安全，或者返回错误信息
            raise ValueError(f"加载远程YOLO配置失败: {e}") from e
    return _yolo_config

# --- 辅助函数 --- 
async def _make_api_request(method: str, endpoint: str, json_data: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """发送异步 HTTP 请求到 YOLO Demo API"""
    try:
        config = await _get_yolo_config()
    except ValueError as e:
        # 如果配置加载失败，直接返回错误
        return {"success": False, "message": str(e)}
        
    url = f"{config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
    headers = {'Content-Type': 'application/json'}
    timeout = config.timeout
    
    try:
        async with httpx.AsyncClient(timeout=timeout) as client:
            if method.upper() == 'GET':
                response = await client.get(url, headers=headers)
            elif method.upper() == 'POST':
                response = await client.post(url, headers=headers, json=json_data)
            else:
                return {"success": False, "message": f"不支持的 HTTP 方法: {method}"}

            response.raise_for_status() # 如果状态码不是 2xx，则引发异常
            return response.json()
            
    except httpx.TimeoutException:
        logger.error(f"请求 {url} 超时")
        return {"success": False, "message": "请求远程控制服务超时"}
    except httpx.RequestError as e:
        logger.error(f"请求 {url} 发生网络错误: {e}")
        return {"success": False, "message": f"连接远程控制服务失败: {e}"}
    except httpx.HTTPStatusError as e:
        logger.error(f"请求 {url} 返回错误状态码 {e.response.status_code}: {e.response.text}")
        try:
            # 尝试解析服务端的错误信息
            error_json = e.response.json()
            return {"success": False, "message": error_json.get("message", f"远程服务返回错误 {e.response.status_code}")}
        except:
             return {"success": False, "message": f"远程服务返回错误 {e.response.status_code}"}
    except Exception as e:
        logger.error(f"调用远程 API {url} 时发生意外错误: {e}", exc_info=True)
        return {"success": False, "message": f"调用远程控制时发生内部错误: {e}"}

# --- Function Call 实现 --- 

async def yolo_move_servo_relative(axis: str, delta: float):
    """控制远程 YOLO Demo 的舵机相对移动"""
    logger.info(f"请求远程舵机相对移动: axis={axis}, delta={delta}")
    if axis not in ['pan', 'tilt']:
        return {"success": False, "message": "错误：无效的舵机轴，请指定 'pan' (左右) 或 'tilt' (上下)。"}
        
    payload = {"axis": axis, "delta": delta}
    result = await _make_api_request('POST', '/move_servo_relative', json_data=payload)
    
    if result.get("success"):
        return result.get("message", "舵机移动成功。")
    else:
        return result

async def yolo_reset_servo():
    """让远程 YOLO Demo 的舵机云台回中"""
    logger.info("请求远程舵机回中")
    result = await _make_api_request('POST', '/reset_position')
    if result.get("success"):
        return result.get("message", "舵机回中成功。")
    else:
        return result

async def yolo_toggle_tracking(enabled: bool):
    """开启或关闭远程 YOLO Demo 的人脸自动追踪功能"""
    action = "启动" if enabled else "停止"
    logger.info(f"请求远程 {action} 人脸追踪")
    payload = {"enabled": enabled}
    result = await _make_api_request('POST', '/toggle_tracking', json_data=payload)
    if result.get("success"):
        return result.get("message", f"人脸追踪{action}成功。")
    else:
        return result

async def yolo_get_face_count():
    """获取远程 YOLO Demo 当前画面中的人脸数量"""
    logger.info("请求远程获取人脸数量")
    result = await _make_api_request('GET', '/get_face_count')
    
    if result.get("success"):
        count = result.get('face_count', '未知')
        detected = result.get('face_detected', False)
        is_fresh = result.get('is_fresh', False)
        if count == 0:
            return "当前画面中没有检测到人脸。"
        elif count == '未知':
            return "无法获取人脸数量信息。"
        else:
            fresh_text = " (实时)" if is_fresh else " (可能已过期)"
            return f"当前画面中检测到 {count} 张人脸{fresh_text}。"
    else:
        # 返回错误格式，而不是直接返回字符串
        return result  # result 已经是 {"success": False, "message": "..."} 格式 