"""
函数调用 schema 配置文件
"""

from .function_call_tools import FunctionRegistry

def get_calculator_schema():
    """计算器函数 schema"""
    return {
        "type": "function",
        "function": {
            "name": "calculator",
            "description": FunctionRegistry.get_description('calculator'),
            "parameters": {
                "type": "object",
                "properties": {
                    "op": {"type": "string", "enum": ["add", "sub", "mul", "div"], "description": "操作类型：加add、减sub、乘mul、除div"},
                    "a": {"type": "number", "description": "第一个数字"},
                    "b": {"type": "number", "description": "第二个数字"}
                },
                "required": ["op", "a", "b"]
            }
        }
    }

def get_music_player_schemas():
    """音乐播放器相关函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "music_player_list",
                "description": FunctionRegistry.get_description('music_player_list'),
                "parameters": {"type": "object", "properties": {}}
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_play_random",
                "description": FunctionRegistry.get_description('music_player_play_random'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "sample_rate": {"type": "number", "description": "采样率", "default": 48000}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_play",
                "description": FunctionRegistry.get_description('music_player_play'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "music_name": {"type": "string", "description": "音乐文件名"},
                        "sample_rate": {"type": "number", "description": "采样率", "default": 48000}
                    },
                    "required": ["music_name"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_stop",
                "description": FunctionRegistry.get_description('music_player_stop'),
                "parameters": {"type": "object", "properties": {}}
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_increase_volume",
                "description": FunctionRegistry.get_description('music_player_increase_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量增加步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_decrease_volume",
                "description": FunctionRegistry.get_description('music_player_decrease_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量减少步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "music_player_set_volume",
                "description": FunctionRegistry.get_description('music_player_set_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "value": {"type": "number", "description": "目标音量，范围0.0~1.0"}
                    },
                    "required": ["value"]
                }
            }
        }
    ]

# --- 新增 TTS 音量控制 Schema ---
def get_tts_player_schemas():
    """TTS播放器（语音助手）音量控制函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "tts_player_increase_volume",
                "description": FunctionRegistry.get_description('tts_player_increase_volume'), # 从注册处获取描述
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量增加步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "tts_player_decrease_volume",
                "description": FunctionRegistry.get_description('tts_player_decrease_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "step": {"type": "number", "description": "音量减少步长，默认0.1", "default": 0.1}
                    }
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "tts_player_set_volume",
                "description": FunctionRegistry.get_description('tts_player_set_volume'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        # 保持与 music_player_set_volume 一致，让LLM更容易理解 value 的用法
                        "value": {"type": "string", "description": "目标音量：数字(如 0.8, 50)，百分比('50%'), 或描述性词语('最大','最小','一半','静音')"}
                    },
                    "required": ["value"]
                }
            }
        }
    ]

# --- 新增 Remote YOLO Demo 控制 Schema ---
def get_remote_yolo_schemas():
    """远程 YOLO Demo 控制函数 schemas"""
    return [
        {
            "type": "function",
            "function": {
                "name": "yolo_move_servo_relative",
                "description": FunctionRegistry.get_description('yolo_move_servo_relative'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "axis": {
                            "type": "string",
                            "enum": ["pan", "tilt"],
                            "description": "要移动的舵机轴：'pan' (左右) 或 'tilt' (上下)"
                        },
                        "delta": {
                            "type": "number",
                            "description": "要移动的角度变化量（度）。正数通常表示向右或向上，负数表示向左或向下。"
                        }
                    },
                    "required": ["axis", "delta"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "yolo_reset_servo",
                "description": FunctionRegistry.get_description('yolo_reset_servo'),
                "parameters": {"type": "object", "properties": {}} # 无参数
            }
        },
        {
            "type": "function",
            "function": {
                "name": "yolo_toggle_tracking",
                "description": FunctionRegistry.get_description('yolo_toggle_tracking'),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "enabled": {
                            "type": "boolean",
                            "description": "设置为 true 以开启人脸追踪，设置为 false 以关闭。"
                        }
                    },
                    "required": ["enabled"]
                }
            }
        },
        {
            "type": "function",
            "function": {
                "name": "yolo_get_face_count",
                "description": FunctionRegistry.get_description('yolo_get_face_count'),
                "parameters": {"type": "object", "properties": {}} # 无参数
            }
        }
    ]

# --- Shape Detection System Schema (已迁移到MCP) ---
# 形状检测功能已迁移到MCP服务器，相关schema已移除

# --- Intelligent Vision Arm Control Schema (已迁移到MCP) ---
# 智能视觉机械臂控制功能已迁移到MCP服务器，相关schema已移除
def get_robot_arm_control_schemas():
    """智能视觉机械臂控制函数 schemas - 已迁移到MCP"""
    return []  # 功能已迁移到MCP服务器


def get_all_schemas():
    """获取所有函数 schemas"""
    schemas = []
    schemas.append(get_calculator_schema())
    schemas.extend(get_music_player_schemas())
    schemas.extend(get_tts_player_schemas())
    schemas.extend(get_remote_yolo_schemas()) # 添加 Remote YOLO 控制 schemas
    # 注释掉机械臂相关的schemas，这些功能已迁移到MCP
    # schemas.extend(get_shape_detection_schemas()) # 已迁移到MCP
    # schemas.extend(get_robot_arm_control_schemas()) # 已迁移到MCP
    return schemas