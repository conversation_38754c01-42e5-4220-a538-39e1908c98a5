import os
from typing import List, Optional, AsyncGenerator
import io
import asyncio
import logging # 导入 logging
import random # 新增用于随机选择
from concurrent.futures import ThreadPoolExecutor # 新增用于异步加载音频
import pathlib
import threading

# 条件导入音频相关模块（在WSL2等环境中可能不可用）
try:
    from ..audio_new_player import AudioNewPlayer, AudioPlayerError
    from ..audio_device_manager import audio_device_manager, AudioDeviceType
    _audio_player_available = True
except (ImportError, OSError) as e:
    # 创建占位符类
    class AudioNewPlayer:
        def __init__(self, *args, **kwargs):
            raise RuntimeError(f"音频播放器在当前环境中不可用: {e}")

    class AudioPlayerError(Exception):
        pass

    audio_device_manager = None
    AudioDeviceType = None
    _audio_player_available = False

try:
    from pydub import AudioSegment
    _pydub_available = True
except ImportError:
    AudioSegment = None
    _pydub_available = False

try:
    import soundfile as sf  # 新增用于获取wav声道数
    _soundfile_available = True
except ImportError:
    sf = None
    _soundfile_available = False

try:
    import pygame
    _pygame_available = True
except ImportError:
    pygame = None
    _pygame_available = False

from ..config_loader import load_config, MusicPlayerConfig

logger = logging.getLogger(__name__) # 获取 logger

# 创建线程池用于异步加载音频
audio_loader_executor = ThreadPoolExecutor(max_workers=2)

# 1. 计算器功能
class Calculator:
    @staticmethod
    def calculate(op: str, a: float, b: float) -> float:
        if op == 'add':
            return a + b
        elif op == 'sub':
            return a - b
        elif op == 'mul':
            return a * b
        elif op == 'div':
            if b == 0:
                raise ValueError('除数不能为0')
            return a / b
        else:
            raise ValueError(f'不支持的操作: {op}')

# 2. 音乐播放器功能
class MusicPlayer:
    def __init__(self, audio_device_manager=None):
        self.audio_device_manager = audio_device_manager
        self.current_music = None
        self._music_list = None
        self._monitor_thread = None
        self._stop_monitor = threading.Event()
        self.player = None  # AudioNewPlayer实例，由外部设置

        # 从配置文件加载音乐播放器配置
        try:
            config_loader = load_config()
            self.config = config_loader.get_music_player_config()
            self._volume = self.config.volume
            logger.info(f"已加载音乐播放器配置: 资源目录={self.config.resource_dir}, 音量={self._volume}")
        except Exception as e:
            logger.error(f"加载音乐播放器配置失败: {e}", exc_info=True)
            # 使用默认配置
            self.config = MusicPlayerConfig(
                resource_dir="./backend/resource",
                volume=0.7,
                supported_formats=[".mp3", ".wav", ".flac"]
            )
            self._volume = 0.7
            logger.warning(f"使用默认音乐播放器配置: 资源目录={self.config.resource_dir}, 音量={self._volume}")

        # 初始化pygame mixer，使用与AudioNewPlayer相同的设备配置
        self._init_pygame_mixer()

        # 注册到设备管理器
        audio_device_manager.register_volume_callback(
            AudioDeviceType.MUSIC_PLAYER,
            self._set_volume_for_device_manager
        )

        # 设置原始音量
        audio_device_manager.set_original_volume(AudioDeviceType.MUSIC_PLAYER, self._volume)

    def _init_pygame_mixer(self):
        """
        初始化pygame mixer，动态尝试多个设备名称，并回退到默认设备。
        这是为了解决在某些Linux环境下SDL无法通过名称找到特定设备的问题。
        """
        # 根据分析报告，尝试一个包含多种可能名称的列表
        # UACDemoV1.0 和 UACDemoV10 是描述性名称
        # hw:0,0 和 plughw:0,0 是ALSA设备名
        # None 代表默认设备，作为最后的选择
        potential_devices = ['UACDemoV1.0', 'UACDemoV10', 'hw:0,0', 'plughw:0,0', None]
        
        os.environ['SDL_AUDIODRIVER'] = 'pulse'
        
        for device in potential_devices:
            try:
                logger.info(f"正在尝试使用设备 '{device}' 初始化 Pygame Mixer...")
                pygame.mixer.pre_init(
                    frequency=44100,
                    size=-16,
                    channels=2,
                    buffer=4096
                )
                # 如果device是None，则不传入devicename参数
                if device:
                    pygame.mixer.init(devicename=device)
                else:
                    pygame.mixer.init()
                
                logger.info(f"✅ Pygame Mixer 使用设备 '{device or '默认'}' 初始化成功！")
                return  # 初始化成功，退出函数
            except pygame.error as e:
                logger.warning(f"❌ 尝试设备 '{device}' 失败: {e}")
                # 清理失败的初始化，以便下次尝试
                pygame.mixer.quit()
        
        # 如果所有尝试都失败了
        logger.error("所有尝试都失败了，无法初始化 Pygame Mixer。")
        raise RuntimeError("无法初始化 Pygame Mixer，请检查音频设备和驱动。")

    def _load_music_list(self) -> List[str]:
        if self._music_list is not None:
            return self._music_list

        # 获取 Backend 目录的绝对路径
        backend_root = pathlib.Path(__file__).resolve().parent.parent.parent
        # 使用配置文件中的资源目录路径
        music_dir = backend_root / self.config.resource_dir

        logger.info(f"尝试加载音乐目录: {music_dir}")

        if not os.path.exists(music_dir):
            logger.warning(f'音乐目录不存在: {music_dir}')
            return []

        try:
            files = os.listdir(music_dir)
            # 使用配置文件中的支持格式
            music_files = [f for f in files if any(f.lower().endswith(ext) for ext in self.config.supported_formats)]
            self._music_list = music_files
            logger.info(f"找到音乐文件: {music_files}")
            return music_files
        except Exception as e:
            logger.error(f"扫描音乐目录失败: {music_dir}, Error: {e}", exc_info=True)
            return []

    def list_music(self):
        return self._load_music_list()

    def _set_volume_for_device_manager(self, volume: float):
        """设备管理器调用的音量控制方法"""
        try:
            # 不更新内部音量变量，只是临时调整pygame音量
            pygame.mixer.music.set_volume(volume)
            logger.info(f"设备管理器调整音乐音量到: {volume}")
        except Exception as e:
            logger.error(f"设备管理器调整音乐音量时出错: {e}")

    def _monitor_music_playback(self):
        """监控音乐播放状态，播放结束时释放设备权限"""
        while not self._stop_monitor.wait(1.0):  # 每秒检查一次
            try:
                if not pygame.mixer.music.get_busy():
                    # 音乐播放结束
                    logger.info("检测到音乐播放结束，释放设备访问权限")
                    self.current_music = None

                    # 释放设备访问权限
                    try:
                        self._release_device_access_sync()
                    except Exception as e:
                        logger.warning(f"监控线程释放设备访问权限时出错: {e}")

                    # 恢复原始音量设置
                    try:
                        pygame.mixer.music.set_volume(self._volume)
                    except Exception:
                        pass

                    break
            except Exception as e:
                logger.error(f"监控音乐播放状态时出错: {e}")
                break

        logger.debug("音乐播放监控线程结束")

    def _start_monitor_thread(self):
        """启动监控线程"""
        self._stop_monitor_thread()  # 先停止之前的监控线程
        self._stop_monitor.clear()
        self._monitor_thread = threading.Thread(target=self._monitor_music_playback, daemon=True)
        self._monitor_thread.start()
        logger.debug("音乐播放监控线程已启动")

    def _stop_monitor_thread(self):
        """停止监控线程"""
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._stop_monitor.set()
            self._monitor_thread.join(timeout=2.0)
            logger.debug("音乐播放监控线程已停止")

    def _release_device_access_sync(self):
        """同步方式释放设备访问权限"""
        try:
            audio_device_manager.release_device_access_sync(AudioDeviceType.MUSIC_PLAYER)
        except Exception as e:
            logger.error(f"同步释放设备访问权限时出错: {e}")

    def play_music(self, music_name, sample_rate=48000):
        # 请求音频设备访问权限（同步方式）
        try:
            device_info = {"music_name": music_name}
            access_granted = audio_device_manager.request_device_access_sync(
                AudioDeviceType.MUSIC_PLAYER,
                device_info,
                force_stop_others=True
            )

            if not access_granted:
                return "无法获得音频设备访问权限，请稍后再试。"
        except Exception as e:
            logger.error(f"请求设备访问权限时出错: {e}")
            return "音频设备忙碌中，请稍后再试。"

        music_list = self._load_music_list()
        if not music_list:
            # 释放设备访问权限
            try:
                self._release_device_access_sync()
            except Exception:
                pass
            return "很抱歉，当前没有可播放的音乐。"
        # 匹配音乐名
        found_music_name = None
        if music_name in music_list:
            found_music_name = music_name
        else:
            name_without_ext, _ = os.path.splitext(music_name)
            candidates_no_ext = [f for f in music_list if os.path.splitext(f)[0] == name_without_ext]
            if candidates_no_ext:
                found_music_name = candidates_no_ext[0]
            else:
                candidates_prefix = [f for f in music_list if os.path.splitext(f)[0].startswith(name_without_ext)]
                if candidates_prefix:
                    found_music_name = candidates_prefix[0]
                else:
                    candidates_contain = [f for f in music_list if name_without_ext in os.path.splitext(f)[0]]
                    if candidates_contain:
                        found_music_name = candidates_contain[0]
        if not found_music_name:
            # 释放设备访问权限
            try:
                self._release_device_access_sync()
            except Exception:
                pass
            return f"很抱歉，歌曲《{music_name}》暂时无法播放。"
        music_name = found_music_name

        # 获取 Backend 目录的绝对路径
        backend_root = pathlib.Path(__file__).resolve().parent.parent.parent
        # 使用配置文件中的资源目录路径
        music_path = backend_root / self.config.resource_dir / music_name

        logger.info(f"尝试播放音乐: {music_path}")

        try:
            pygame.mixer.music.load(music_path)
            pygame.mixer.music.set_volume(self._volume)
            pygame.mixer.music.play()
            self.current_music = music_name

            # 启动监控线程
            self._start_monitor_thread()

            logger.info(f"成功播放音乐: {music_name}, 音量: {self._volume}")
            return f"已为您播放《{os.path.splitext(music_name)[0]}》，祝您聆听愉快！"
        except Exception as e:
            logger.error(f"播放音乐失败: {music_path}, Error: {e}", exc_info=True)
            # 播放失败时释放设备访问权限
            try:
                self._release_device_access_sync()
            except Exception:
                pass
            return f"播放失败: {e}"

    def play_random_music(self, sample_rate=48000):
        music_list = self._load_music_list()
        if not music_list:
            return "很抱歉，当前没有可播放的音乐。"
        random_music = random.choice(music_list)
        return self.play_music(random_music, sample_rate)

    def stop_music(self):
        try:
            pygame.mixer.music.stop()
            self.current_music = None

            # 停止监控线程
            self._stop_monitor_thread()

            # 恢复原始音量
            try:
                pygame.mixer.music.set_volume(self._volume)
            except Exception:
                pass

            # 释放设备访问权限
            try:
                self._release_device_access_sync()
            except Exception as e:
                logger.warning(f"释放设备访问权限时出错: {e}")

            return "音乐已停止。"
        except Exception as e:
            logger.error(f"停止音乐失败: {e}", exc_info=True)
            return f"停止音乐失败: {e}"

    def is_playing(self) -> bool:
        try:
            return pygame.mixer.music.get_busy()
        except Exception:
            return False

    def set_volume(self, value: float) -> dict:
        value = max(0.0, min(1.0, value))
        self._volume = value
        try:
            pygame.mixer.music.set_volume(value)
            logger.info(f"用户手动设置背景音乐音量为: {value:.2f}")

            # 使用注入的 audio_device_manager
            if self.audio_device_manager:
                self.audio_device_manager.update_music_player_volume(value)
                logger.info(f"通过注入的 AudioDeviceManager 更新音量为: {value:.2f}")
                return {"status": "success", "volume": value}
            else:
                logger.warning("AudioDeviceManager 未注入，无法同步音量状态")
                # 即使没有注入管理器，也应该成功设置本地音量
                return {"status": "success", "volume": value, "message": "AudioDeviceManager not available, volume updated locally."}

        except Exception as e:
            logger.error(f"设置音量失败: {e}", exc_info=True)
            return {"status": "error", "message": str(e)}

    def increase_volume(self, step: float = 0.1) -> dict:
        return self.set_volume(self._volume + step)

    def decrease_volume(self, step: float = 0.1) -> dict:
        return self.set_volume(self._volume - step)

    def set_audio_player(self, player: AudioNewPlayer):
        """设置共享的AudioNewPlayer实例"""
        self.player = player
        logger.info("音乐播放器已设置共享的AudioNewPlayer实例")

    def get_player(self) -> AudioNewPlayer:
        """返回底层的 AudioPlayer 实例"""
        return self.player