import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
import logging
import os
import sys
import asyncio
import queue
import subprocess
import time
from typing import Optional, List, Dict, Any
from contextlib import asynccontextmanager
import multiprocessing # Added for ASR process management

# Adjust import paths
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, project_root)

from llm.client import LLMClient
from tts.client import TTSClient
from utils.audio_new_player import AudioNewPlayer
from utils.config_loader import load_config, ConfigLoader, LLMConfig, TTSConfig, AudioPlayerConfig, ASRConfig # Added ASRConfig
from utils.function_call.function_call_tools import initialize_music_player, set_shared_audio_player
from utils.function_call.function_tools_impl import MusicPlayer
from utils.function_call.function_schema import get_all_schemas
# MCP增强支持
try:
    from utils.mcp.client.mcp_function_adapter import get_enhanced_function_schemas
    MCP_ENHANCED_AVAILABLE = True
except ImportError:
    MCP_ENHANCED_AVAILABLE = False
from utils.audio_device_manager import AudioDeviceManager
from app import shared_state
from app import turn_manager
# Conditionally import ASR service runner
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
)
logger = logging.getLogger("SERVER_APP")

try:
    from asr.asr_service import run_asr_service
except ImportError:
    run_asr_service = None
    logger.warning("asr.asr_service not found, ASR input mode will not be available.")

# 全局变量声明
config_loader = None
llm_client = None
tts_client = None
audio_player = None
tools_schema = None
system_prompt = None
tts_config_obj = None
asr_service_process = None
asr_result_queue = None
asr_listener_task = None
input_mode = None
mcp_initialization_task = None


def set_system_volume(logger):
    """
    Dynamically finds a suitable audio control and sets its volume to 100%.
    """
    try:
        # Step 1: Find available controls
        logger.info("Searching for available audio controls...")
        scontrols_cmd = "amixer scontrols"
        result = subprocess.run(scontrols_cmd, shell=True, check=True, capture_output=True, text=True)
        
        # Step 2: Parse the output to find control names
        import re
        available_controls = re.findall(r"Simple mixer control '([^']*)'", result.stdout)
        if not available_controls:
            logger.warning("Could not find any simple mixer controls via 'amixer scontrols'. Volume not set.")
            return
        logger.info(f"Found controls: {available_controls}")

        # Step 3: Find a suitable control from a preferred list
        preferred_controls = ['Master', 'PCM', 'Speaker', 'Headphone']
        control_to_set = None
        for control in preferred_controls:
            if control in available_controls:
                control_to_set = control
                logger.info(f"Found preferred control: '{control_to_set}'")
                break
        
        if not control_to_set:
            logger.warning(f"Could not find any of the preferred controls {preferred_controls} in the available list. Volume not set.")
            return

        # Step 4: Set the volume for the found control
        logger.info(f"Attempting to set volume for '{control_to_set}' to 100%...")
        # Use single quotes around the control name to handle names with spaces, although amixer is usually fine.
        set_volume_cmd = f"amixer set '{control_to_set}' 100%"
        set_result = subprocess.run(set_volume_cmd, shell=True, check=True, capture_output=True, text=True)
        logger.info(f"Successfully set volume for '{control_to_set}' to 100%.")
        logger.debug(f"amixer set output: {set_result.stdout}")

    except FileNotFoundError:
        logger.error("`amixer` command not found. Could not set system volume. Please ensure 'alsa-utils' is installed.")
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to execute amixer command. Command '{e.cmd}' returned non-zero exit status {e.returncode}. Volume may not be set.")
        logger.error(f"Stderr: {e.stderr}")
    except Exception as e:
        logger.error(f"An unexpected error occurred while setting system volume: {e}", exc_info=True)


async def initialize_mcp_tools_async():
    """异步初始化MCP工具，不阻塞主服务启动"""
    global tools_schema

    if not MCP_ENHANCED_AVAILABLE:
        logger.info("MCP功能不可用，跳过MCP工具初始化")
        return

    try:
        logger.info("开始异步加载MCP工具...")
        start_time = time.time()

        # 等待MCP连接完成
        enhanced_tools_schema = await get_enhanced_function_schemas()

        if enhanced_tools_schema:
            # 更新全局工具schema
            tools_schema = enhanced_tools_schema

            # 统计工具数量
            mcp_tools = [tool for tool in tools_schema if tool.get('name', '').startswith('mcp:')]
            local_tools = [tool for tool in tools_schema if not tool.get('name', '').startswith('mcp:')]

            elapsed_time = time.time() - start_time
            logger.info(f"MCP工具加载完成，耗时: {elapsed_time:.2f}秒")
            logger.info(f"工具统计: 本地工具 {len(local_tools)} 个, MCP工具 {len(mcp_tools)} 个")
            logger.info(f"总工具数量: {len(tools_schema)}")
        else:
            logger.warning("MCP工具加载失败，继续使用本地工具")

    except Exception as e:
        logger.error(f"MCP工具异步加载失败: {e}", exc_info=True)
        logger.info("继续使用本地工具")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Application lifespan manager. Handles startup and shutdown events.
    """
    # Startup logic
    global config_loader, llm_client, tts_client, audio_player, tools_schema, system_prompt, tts_config_obj
    global asr_service_process, asr_result_queue, asr_listener_task, input_mode # ASR globals
    global mcp_initialization_task # MCP异步任务
    logger.info("Starting up application and initializing components...")

    try:
        config_loader = load_config()
        logger.info("Configuration loaded successfully.")

        input_mode = config_loader.get_input_mode() # Get input_mode
        logger.info(f"Application input mode set to: {input_mode}")

        llm_config_dict = config_loader.get_config().get('llm', {})
        fetched_tts_config = config_loader.get_tts_config()
        if fetched_tts_config is None:
            logger.error("TTSConfig could not be loaded. TTS functionality will be impaired.")
        tts_config_obj = fetched_tts_config
        player_config_obj = config_loader.get_audio_player_config()
        
        system_prompt = llm_config_dict.get('system_prompt', '')
        logger.info(f"System prompt loaded: '{system_prompt[:50]}...'")

        # Initialize clients
        llm_client = LLMClient(llm_config_dict)
        logger.info("LLMClient initialized.")

        if tts_config_obj:
            tts_client = TTSClient(tts_config_obj)
            logger.info("TTSClient initialized.")
        else:
            logger.error("TTSClient not initialized due to missing TTSConfig.")

        if player_config_obj:
            audio_player = AudioNewPlayer(
                device=player_config_obj.device,
                device_keyword=player_config_obj.device_keyword,
                blocksize=player_config_obj.blocksize,
                buffer_size=player_config_obj.buffer_size,
                min_prebuffer=player_config_obj.min_prebuffer,
                volume=player_config_obj.volume
            )
            logger.info(f"AudioNewPlayer initialized with device: {player_config_obj.device}, device_keyword: {player_config_obj.device_keyword}, volume: {player_config_obj.volume}")

            # --- 依赖注入设置 ---
            # 1. 创建 AudioDeviceManager
            audio_device_manager = AudioDeviceManager()
            shared_state.audio_device_manager = audio_device_manager
            logger.info("AudioDeviceManager initialized and stored in shared_state.")

            # 2. 创建 MusicPlayer 并注入依赖
            music_player = MusicPlayer(audio_device_manager=audio_device_manager)
            logger.info("MusicPlayer instance created with injected AudioDeviceManager.")

            # 3. 将带有依赖的 music_player 实例共享给工具层
            initialize_music_player(music_player)
            logger.info("Injected MusicPlayer instance shared with function_call_tools.")

            # 4. 为 MusicPlayer 设置共享的 AudioNewPlayer 实例
            set_shared_audio_player(audio_player)
            logger.info("AudioNewPlayer instance shared with the initialized MusicPlayer tool.")
        else:
            logger.error("AudioPlayer not initialized due to missing AudioPlayerConfig.")

        # 先加载本地工具schema，确保服务可以立即启动
        tools_schema = get_all_schemas()
        logger.info(f"本地工具schema已加载，工具数量: {len(tools_schema if tools_schema else [])}")

        # 启动MCP工具异步加载任务（不阻塞主服务启动）
        if MCP_ENHANCED_AVAILABLE:
            try:
                logger.info("启动MCP工具异步加载任务...")
                mcp_initialization_task = asyncio.create_task(initialize_mcp_tools_async())
                logger.info("MCP工具将在后台异步加载，不影响服务启动")
            except Exception as e:
                logger.error(f"启动MCP异步加载任务失败: {e}")
        else:
            logger.info("MCP功能不可用，仅使用本地工具")

        # --- ASR Service Initialization (if applicable) ---
        if input_mode == "asr":
            if run_asr_service is None:
                logger.error("ASR input mode selected, but run_asr_service is not available (import failed). ASR will not function.")
            else:
                asr_config_obj: Optional[ASRConfig] = config_loader.get_asr_config()
                if asr_config_obj:
                    logger.info("ASR mode enabled. Initializing ASR service...")
                    asr_result_queue = multiprocessing.Queue(maxsize=20) # Create queue for ASR results
                    
                    # Note: audio_queue for VAD->ASR inter-process is handled within asr_service now.
                    # We only need the result_queue here.
                    asr_service_process = multiprocessing.Process(
                        target=run_asr_service,
                        args=(asr_config_obj, None, asr_result_queue), # audio_queue is None
                        name="ASRServiceProcess",
                        daemon=True # Ensure process exits when main app exits
                    )
                    asr_service_process.start()
                    logger.info(f"ASR service process started with PID: {asr_service_process.pid}")

                    # Start the task to listen for ASR results and forward them
                    asr_listener_task = asyncio.create_task(listen_and_forward_asr_results())
                    logger.info("ASR result listener and forwarder task created.")
                else:
                    logger.error("ASR input mode selected, but ASR configuration is missing. ASR will not function.")
        else:
            logger.info("ASR input mode is not 'asr'. ASR service will not be started.")
        # --- End ASR Initialization ---

        logger.info("All components initialized successfully (or with warnings for missing configs/ASR).")

        # --- Set System Volume Dynamically ---
        set_system_volume(logger)
        # --- End Set System Volume ---

    except Exception as e:
        logger.error(f"Error during startup initialization: {e}", exc_info=True)
        # Potentially raise to stop the app if critical components fail
        # For now, log and continue, but some features might not work.

    yield

    # Shutdown logic
    logger.info("Application shutting down...")

    # Stop ASR listener task
    if asr_listener_task and not asr_listener_task.done():
        logger.info("Cancelling ASR result listener task...")
        asr_listener_task.cancel()
        try:
            await asyncio.wait_for(asr_listener_task, timeout=2.0)
            logger.info("ASR result listener task cancelled successfully.")
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for ASR listener task to cancel.")
        except asyncio.CancelledError:
            logger.info("ASR listener task was already cancelled.")
        except Exception as e_cancel_asr_listener:
            logger.error(f"Error cancelling ASR listener task: {e_cancel_asr_listener}")

    # Stop MCP initialization task
    if mcp_initialization_task and not mcp_initialization_task.done():
        logger.info("Cancelling MCP initialization task...")
        mcp_initialization_task.cancel()
        try:
            await asyncio.wait_for(mcp_initialization_task, timeout=2.0)
            logger.info("MCP initialization task cancelled successfully.")
        except asyncio.TimeoutError:
            logger.warning("Timeout waiting for MCP initialization task to cancel.")
        except asyncio.CancelledError:
            logger.info("MCP initialization task was already cancelled.")
        except Exception as e_cancel_mcp:
            logger.error(f"Error cancelling MCP initialization task: {e_cancel_mcp}")

    # Terminate ASR service process
    if asr_service_process and asr_service_process.is_alive():
        logger.info(f"Terminating ASR service process (PID: {asr_service_process.pid})...")
        try:
            asr_service_process.terminate() # Send SIGTERM
            # Wait for a short period for the process to terminate gracefully
            await asyncio.to_thread(asr_service_process.join, timeout=3.0)
            if asr_service_process.is_alive():
                logger.warning(f"ASR service process (PID: {asr_service_process.pid}) did not terminate gracefully after 3s. Sending SIGKILL.")
                asr_service_process.kill() # Force kill if still alive
                await asyncio.to_thread(asr_service_process.join, timeout=1.0)
                if asr_service_process.is_alive():
                    logger.error(f"ASR service process (PID: {asr_service_process.pid}) could not be killed.")
                else:
                    logger.info(f"ASR service process (PID: {asr_service_process.pid}) terminated successfully.")
            else:
                logger.info(f"ASR service process (PID: {asr_service_process.pid}) terminated successfully.")
        except Exception as e_terminate_asr:
            logger.error(f"Error terminating ASR service process: {e_terminate_asr}")

    # Close TTS client
    if tts_client:
        logger.info("Closing TTS client...")
        try:
            await tts_client.close()
            logger.info("TTS client closed successfully.")
        except Exception as e_close_tts:
            logger.error(f"Error closing TTS client: {e_close_tts}")
    
    # Stop Audio Player
    if audio_player and hasattr(audio_player, 'close'):
        logger.info("Closing AudioNewPlayer...")
        try:
            await audio_player.close()
            logger.info("AudioNewPlayer closed successfully.")
        except Exception as e_close_player:
            logger.error(f"Error closing AudioNewPlayer: {e_close_player}")

    # Cleanup MCP resources
    if MCP_ENHANCED_AVAILABLE:
        logger.info("Cleaning up MCP resources...")
        try:
            from utils.mcp.client.mcp_function_adapter import cleanup_mcp_adapter
            await cleanup_mcp_adapter()
            logger.info("MCP resources cleaned up successfully.")
        except Exception as e_cleanup_mcp:
            logger.error(f"Error cleaning up MCP resources: {e_cleanup_mcp}")

    logger.info("Application shutdown complete.")


app = FastAPI(lifespan=lifespan)

# WebSocket Message Types (inspired by plan.md)
# Client to Server (C2S)
MSG_TYPE_USER_TEXT_INPUT = "USER_TEXT_INPUT"
MSG_TYPE_INTERRUPT_TURN = "INTERRUPT_TURN"
MSG_TYPE_TOGGLE_ASR_LISTENING = "TOGGLE_ASR_LISTENING" # For later ASR integration

# Server to Client (S2C)
MSG_TYPE_SYSTEM_STATUS = "SYSTEM_STATUS"
MSG_TYPE_LLM_CHUNK = "LLM_CHUNK"
MSG_TYPE_LLM_FINAL_RESPONSE = "LLM_FINAL_RESPONSE"
MSG_TYPE_TTS_STATUS = "TTS_STATUS"
MSG_TYPE_ASR_PARTIAL_RESULT = "ASR_PARTIAL_RESULT" # For later ASR integration
MSG_TYPE_ASR_FINAL_RESULT = "ASR_FINAL_RESULT"   # For later ASR integration
MSG_TYPE_ERROR_MESSAGE = "ERROR_MESSAGE"
MSG_TYPE_TURN_ENDED = "TURN_ENDED"
MSG_TYPE_TURN_INTERRUPTED = "TURN_INTERRUPTED"
MSG_TYPE_TOOL_CALL_START = "TOOL_CALL_START"
MSG_TYPE_TOOL_CALL_RESULT = "TOOL_CALL_RESULT"

# Global variables to hold core components
config_loader: Optional[ConfigLoader] = None
llm_client: Optional[LLMClient] = None
tts_client: Optional[TTSClient] = None
audio_player: Optional[AudioNewPlayer] = None
tools_schema: Optional[List[Dict[str, Any]]] = None
system_prompt: str = ""
tts_config_obj: Optional[TTSConfig] = None

# ASR related globals
asr_service_process: Optional[multiprocessing.Process] = None
asr_result_queue: Optional[multiprocessing.Queue] = None
asr_listener_task: Optional[asyncio.Task] = None
input_mode: str = "keyboard" # Default input mode

# List to keep track of active WebSocket connections
active_connections: List[WebSocket] = []

async def listen_and_forward_asr_results():
    global asr_result_queue, active_connections
    logger.info("Starting ASR result listener and forwarder task...")
    if not asr_result_queue:
        logger.error("ASR result queue is not initialized. Cannot listen for ASR results.")
        return

    while True:
        try:
            # Using a small timeout to prevent blocking the asyncio loop indefinitely
            # and to allow the task to be cancellable.
            asr_text = await asyncio.to_thread(asr_result_queue.get, timeout=0.5) 
            if asr_text:
                logger.info(f"ASR RESULT from queue: '{asr_text}'")
                # Iterate over a copy of the list in case it's modified during iteration
                # (though in this specific loop, direct modification by other parts is less likely for active_connections)
                connections_to_send = list(active_connections) # Create a shallow copy
                for connection in connections_to_send:
                    try:
                        # Ensure client is still connected before sending
                        if connection.client_state.value == 1: # WebSocketState.CONNECTED = 1 (based on common library patterns)
                           logger.info(f"Forwarding ASR result to client {connection.client.host}:{connection.client.port}")
                           await connection.send_json({
                               "type": MSG_TYPE_ASR_FINAL_RESULT,
                               "payload": {"text": asr_text}
                           })
                        else:
                            logger.warning(f"ASR: Client {connection.client.host}:{connection.client.port} is not in connected state. Skipping send.")
                            # Optionally remove disconnected clients here or rely on main disconnect handler
                    except WebSocketDisconnect:
                        logger.warning(f"ASR: Client {connection.client.host}:{connection.client.port} disconnected during ASR result send. Removing.")
                        if connection in active_connections: active_connections.remove(connection)
                    except Exception as e_send_asr:
                        logger.error(f"ASR: Error sending ASR result to {connection.client.host}:{connection.client.port}: {e_send_asr}")
            else:
                # This case (empty string or None from queue) might indicate a sentinel or ignorable message
                logger.debug("Received empty or None ASR result from queue, ignoring.")

        except queue.Empty: # Catch timeout from asr_result_queue.get(timeout=0.5)
            # This is expected when no ASR results are available. Allows the loop to periodically check for cancellation.
            pass 
        except asyncio.CancelledError:
            logger.info("ASR result listener task cancelled.")
            break
        except Exception as e:
            logger.error(f"Error in ASR result listener: {e}", exc_info=True)
            await asyncio.sleep(1) # Prevent rapid error looping


@app.get("/")
async def read_root():
    initialized_components = []
    if llm_client: initialized_components.append("LLMClient")
    if tts_client: initialized_components.append("TTSClient")
    if audio_player: initialized_components.append("AudioNewPlayer")
    
    if len(initialized_components) == 3:
        return {"message": "ASR-LLM-TTS Backend is running and core components are initialized."}
    else:
        return {"message": f"ASR-LLM-TTS Backend is running, but some components may have failed to initialize. Initialized: {', '.join(initialized_components) if initialized_components else 'None'}. Check logs."}

@app.websocket("/ws/chat")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    active_connections.append(websocket)
    client_host = websocket.client.host if websocket.client else "unknown_host"
    client_port = websocket.client.port if websocket.client else "unknown_port"
    logger.info(f"WebSocket connection established with {client_host}:{client_port}")
    logger.info(f"Number of active connections: {len(active_connections)}")
    try:
        while True:
            data = await websocket.receive_json()
            logger.debug(f"Received JSON from {client_host}:{client_port}: {data}")

            message_type = data.get("type")
            payload = data.get("payload", {}) # Default to empty dict if payload is None

            if message_type == MSG_TYPE_USER_TEXT_INPUT:
                user_text = payload.get("text")
                if user_text is not None: # Check for presence of text, can be empty string
                    logger.info(f"Received USER_TEXT_INPUT from {client_host}:{client_port}: '{user_text}'")

                    # --- Interruption handling for previous turn ---
                    if shared_state.current_turn_task and not shared_state.current_turn_task.done():
                        task_name = shared_state.current_turn_task.get_name()
                        logger.info(f"New input '{user_text[:20]}' received. Previous turn task '{task_name}' is active. Requesting interruption.")
                        if shared_state.current_turn_stop_event and not shared_state.current_turn_stop_event.is_set():
                            shared_state.current_turn_stop_event.set()
                        
                        if audio_player:
                            logger.info(f"Stopping audio player for task '{task_name}' due to new input.")
                            try:
                                await audio_player.stop("Interrupted by new user input")
                            except Exception as e_player_stop:
                                logger.error(f"Error stopping audio_player for '{task_name}': {e_player_stop}")
                        
                        try:
                            logger.info(f"Waiting for previous turn task '{task_name}' to complete cancellation...")
                            await asyncio.wait_for(shared_state.current_turn_task, timeout=2.0) # Shorter timeout
                            logger.info(f"Previous turn task '{task_name}' finished or timed out after stop signal.")
                        except asyncio.TimeoutError:
                            logger.warning(f"Timeout waiting for '{task_name}' to complete. Forcing cancellation.")
                            shared_state.current_turn_task.cancel()
                            # Give cancellation a moment to propagate
                            await asyncio.sleep(0.1)
                        except asyncio.CancelledError:
                            logger.info(f"Previous turn task '{task_name}' was cancelled.")
                        except Exception as e_task_wait:
                            logger.error(f"Error waiting for '{task_name}' to complete: {e_task_wait}")
                        finally:
                             shared_state.current_turn_task = None
                    # --- End Interruption ---

                    shared_state.current_turn_stop_event = asyncio.Event()
                    # text_queue_for_tts is created inside turn_manager now or passed if pre-created
                    # For this task, let's create it here and pass it.
                    text_queue_for_tts_this_turn = asyncio.Queue(maxsize=200)


                    async def send_to_client_callback(ws: WebSocket, event_type: str, data_payload: dict):
                        # Determine client_host and client_port from ws for accurate logging per call
                        current_client_host = ws.client.host if ws.client else "unknown_host"
                        current_client_port = ws.client.port if ws.client else "unknown_port"

                        log_preview = str(data_payload)[:100]
                        log_msg = f"CALLBACK -> To {current_client_host}:{current_client_port} - Type: {event_type}, Payload: {log_preview}{'...' if len(str(data_payload)) > 100 else ''}"
                        if event_type == MSG_TYPE_LLM_CHUNK and not data_payload.get("content_delta"):
                            logger.debug(f"CALLBACK -> To {current_client_host}:{current_client_port} - Type: {event_type} (empty delta)")
                        elif event_type == MSG_TYPE_LLM_CHUNK:
                            logger.debug(log_msg)
                        else:
                            logger.info(log_msg)
                        
                        try:
                            if ws.client_state.value != 1: # WebSocketState.CONNECTED = 1
                                logger.warning(f"Client {current_client_host}:{current_client_port} disconnected (state check). Cannot send: {event_type}")
                                if ws in active_connections: active_connections.remove(ws)
                                return

                            await ws.send_json({"type": event_type, "payload": data_payload})
                        except WebSocketDisconnect:
                            logger.warning(f"Client {current_client_host}:{current_client_port} disconnected during send for {event_type}. Removing.")
                            if ws in active_connections: active_connections.remove(ws)
                        except Exception as e_ws_send:
                            logger.error(f"Error sending WebSocket message to {current_client_host}:{current_client_port} ({event_type}): {e_ws_send}", exc_info=False)


                    async def turn_wrapper():
                        turn_task_name = f"TurnTask-{user_text[:20].replace(' ', '_')}"
                        current_task = asyncio.current_task()
                        if current_task: current_task.set_name(turn_task_name)
                        
                        logger.info(f"Starting {turn_task_name} for input: '{user_text}'")
                        await send_to_client_callback(websocket, MSG_TYPE_SYSTEM_STATUS, 
                                                      {"module": "turn_handler", 
                                                       "status": "processing_started", 
                                                       "user_input": user_text})
                        try:
                            if not all([llm_client, tts_client, audio_player, tools_schema is not None, tts_config_obj is not None, system_prompt is not None]):
                                missing_comps = []
                                if not llm_client: missing_comps.append("LLMClient")
                                if not tts_client: missing_comps.append("TTSClient")
                                if not audio_player: missing_comps.append("AudioPlayer")
                                if tools_schema is None: missing_comps.append("ToolsSchema")
                                if tts_config_obj is None: missing_comps.append("TTSConfig")
                                # system_prompt can be empty, so not checking its content, just its presence if it were optional.
                                error_msg = f"Core component(s) not initialized: {', '.join(missing_comps)}. Cannot handle turn."
                                logger.error(error_msg)
                                await send_to_client_callback(websocket, MSG_TYPE_ERROR_MESSAGE, {"source": "server_setup", "message": error_msg})
                                await send_to_client_callback(websocket, MSG_TYPE_TURN_ENDED, {"status": "error", "details": error_msg})
                                return

                            await turn_manager.handle_turn(
                                user_input=user_text,
                                llm=llm_client,
                                tts=tts_client,
                                player=audio_player,
                                text_queue=text_queue_for_tts_this_turn, # Pass the queue for this turn
                                stop_event=shared_state.current_turn_stop_event,
                                tools=tools_schema,
                                tts_config=tts_config_obj,
                                system_prompt=system_prompt,
                                websocket_callback=lambda event_type, data_payload: send_to_client_callback(websocket, event_type, data_payload)
                            )
                            
                            # TURN_ENDED is sent based on handle_turn's own logic or this wrapper's exception handling.
                            # If handle_turn completes without stop_event set, it means a natural end.
                            # If stop_event was set (e.g. by LLM stream interruption inside handle_turn that was caught and handled),
                            # handle_turn should have sent appropriate messages.
                            # The key is that this wrapper sends a TURN_ENDED unless an unhandled exception occurs or it's cancelled.
                            if not shared_state.current_turn_stop_event.is_set():
                                logger.info(f"{turn_task_name} completed. Sending TURN_ENDED.")
                                await send_to_client_callback(websocket, MSG_TYPE_TURN_ENDED, {"status": "completed"})
                            else:
                                # If stop_event is set, it implies an interruption handled either internally by turn_manager
                                # (which should send its own final messages) or by the cancellation logic below.
                                # Task 5.1 will handle specific INTERRUPT_TURN messages.
                                logger.info(f"{turn_task_name} finished with stop_event set. Turn_manager or cancellation logic should handle final messages.")
                                # Avoid sending a generic "completed" if it was, for instance, an error that set the stop_event.
                                # For now, if it was stopped, we rely on error messages or upcoming interrupt messages.
                                # We could send a specific "stopped" status if needed.
                                await send_to_client_callback(websocket, MSG_TYPE_TURN_ENDED, {"status": "stopped_or_interrupted"})


                        except asyncio.CancelledError:
                            logger.info(f"{turn_task_name} was cancelled (likely by new input or server shutdown).")
                            await send_to_client_callback(websocket, MSG_TYPE_ERROR_MESSAGE, {"source": "turn_execution", "message": "Turn was cancelled."})
                            # Task 5.1 will send TURN_INTERRUPTED specifically for user interrupts.
                            await send_to_client_callback(websocket, MSG_TYPE_TURN_ENDED, {"status": "cancelled"})
                        except Exception as e:
                            error_detail = f"Unhandled error in {turn_task_name}: {e}"
                            logger.error(error_detail, exc_info=True)
                            await send_to_client_callback(websocket, MSG_TYPE_ERROR_MESSAGE, {"source": "turn_execution", "message": str(e)})
                            await send_to_client_callback(websocket, MSG_TYPE_TURN_ENDED, {"status": "error", "details": str(e)})
                        finally:
                            logger.info(f"{turn_task_name} execution block finished.")
                            # Clear the task from shared_state if this is the one that was running
                            # Compare by object identity
                            if shared_state.current_turn_task is asyncio.current_task():
                                shared_state.current_turn_task = None
                                logger.info(f"Cleared shared_state.current_turn_task for {turn_task_name}.")
                            elif shared_state.current_turn_task:
                                logger.warning(f"Current task {turn_task_name} is not the same as shared_state.current_turn_task ({shared_state.current_turn_task.get_name()}). Not clearing shared_state task from here.")
                            else:
                                logger.info(f"shared_state.current_turn_task was already None when {turn_task_name} finished.")
                    # End of turn_wrapper definition

                    shared_state.current_turn_task = asyncio.create_task(turn_wrapper())
                    # Name is set inside turn_wrapper now
                    logger.info(f"Created and started task for user input: '{user_text}' (Task Name will be set by wrapper)")

                else: # user_text is None
                    logger.warning(f"Received USER_TEXT_INPUT from {client_host}:{client_port} with missing 'text' in payload: {payload}")
                    await websocket.send_json({
                        "type": MSG_TYPE_ERROR_MESSAGE,
                        "payload": {"message": "Invalid USER_TEXT_INPUT payload: 'text' field is missing or null."}
                    })
            
            elif message_type == MSG_TYPE_INTERRUPT_TURN:
                logger.info(f"Received INTERRUPT_TURN from {client_host}:{client_port}")
                interrupted_task_name = "N/A"
                if shared_state.current_turn_task and not shared_state.current_turn_task.done():
                    interrupted_task_name = shared_state.current_turn_task.get_name()
                    logger.info(f"Attempting to interrupt active turn task: {interrupted_task_name}")
                    
                    if shared_state.current_turn_stop_event and not shared_state.current_turn_stop_event.is_set():
                        shared_state.current_turn_stop_event.set()
                        logger.info(f"Stop event set for task {interrupted_task_name}.")
                    else:
                        logger.info(f"Stop event for task {interrupted_task_name} was already set or not available.")

                    # Stop audio player immediately
                    if audio_player:
                        logger.info(f"Stopping audio player due to INTERRUPT_TURN for task {interrupted_task_name}.")
                        try:
                            await audio_player.stop("INTERRUPT_TURN received from client")
                            logger.info(f"Audio player stop requested for task {interrupted_task_name}.")
                        except Exception as e_player_stop:
                            logger.error(f"Error stopping audio_player during INTERRUPT_TURN for task {interrupted_task_name}: {e_player_stop}")
                    
                    await send_to_client_callback(websocket, MSG_TYPE_TURN_INTERRUPTED, 
                                                  {"message": f"Interrupt signal sent for turn task {interrupted_task_name}."})
                    logger.info(f"TURN_INTERRUPTED message sent to client for task {interrupted_task_name}.")
                else: # Corresponds to: if shared_state.current_turn_task and not shared_state.current_turn_task.done()
                    logger.info("Received INTERRUPT_TURN, but no active turn task found or task already done.")
                    await send_to_client_callback(websocket, MSG_TYPE_SYSTEM_STATUS, 
                                                  {"message": "Interrupt signal received, but no active turn to interrupt."})

            else: # Unknown message_type
                logger.warning(f"Received unknown message type from {client_host}:{client_port}: {message_type}")
                await websocket.send_json({
                    "type": MSG_TYPE_ERROR_MESSAGE,
                    "payload": {"message": f"Unknown message type: {message_type}"}
                })

    except WebSocketDisconnect:
        if websocket in active_connections: active_connections.remove(websocket)
        logger.info(f"WebSocket connection with {client_host}:{client_port} closed by client.")
    except Exception as e:
        if websocket in active_connections: active_connections.remove(websocket)
        logger.error(f"Unhandled error in WebSocket connection with {client_host}:{client_port}: {e}", exc_info=True)
        try: # Attempt to inform client if connection is still somehow writable
            if websocket.client_state != WebSocketDisconnect :
                await websocket.send_json({
                    "type": MSG_TYPE_ERROR_MESSAGE,
                    "payload": {"message": f"Server error in WebSocket handler: {str(e)}"}
                })
        except Exception as e_send_final_error:
            logger.error(f"Could not send final error to client {client_host}:{client_port}: {e_send_final_error}")
    finally:
        if websocket in active_connections: # Ensure removal if not already done
            active_connections.remove(websocket)
        logger.info(f"WebSocket connection cleanup for {client_host}:{client_port}. Active connections: {len(active_connections)}")


if __name__ == "__main__":
    # Ensure the configuration for Uvicorn is correct, especially if using reload
    # For simple run:
    uvicorn.run(app, host="0.0.0.0", port=8000) 
    # For development with reload:
    # uvicorn.run("server:app", host="0.0.0.0", port=8000, reload=True) # Make sure 'server' is the filename (server.py) 