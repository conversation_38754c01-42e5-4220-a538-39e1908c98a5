#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可视化工具
Visualization Utils

提供图像可视化功能，如绘制边界框等
"""

import cv2
import numpy as np
from typing import List, Tuple, Optional
import logging

logger = logging.getLogger("Visualization")


def draw_bounding_boxes(
    image: np.ndarray, 
    coordinates: List[List[int]], 
    color: Tuple[int, int, int] = (0, 255, 0),
    thickness: int = 2,
    show_labels: bool = True
) -> np.ndarray:
    """
    在图像上绘制边界框
    
    Args:
        image: 输入图像
        coordinates: 坐标列表 [[x1, y1, x2, y2], ...]
        color: 边界框颜色 (B, G, R)
        thickness: 线条粗细
        show_labels: 是否显示标签
        
    Returns:
        绘制了边界框的图像
    """
    if image is None:
        logger.warning("输入图像为None")
        return np.zeros((480, 640, 3), dtype=np.uint8)
    
    # 复制图像以避免修改原图
    result_image = image.copy()
    
    if not coordinates:
        return result_image
    
    try:
        for i, coord in enumerate(coordinates):
            if len(coord) != 4:
                logger.warning(f"无效坐标格式: {coord}")
                continue
            
            x1, y1, x2, y2 = coord
            
            # 确保坐标为整数
            x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
            
            # 绘制矩形
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, thickness)
            
            # 绘制标签（如果启用）
            if show_labels:
                label = f"Object {i+1}"
                label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)[0]
                
                # 计算标签背景位置
                label_bg_x1 = x1
                label_bg_y1 = y1 - label_size[1] - 10
                label_bg_x2 = x1 + label_size[0] + 10
                label_bg_y2 = y1
                
                # 确保标签在图像范围内
                if label_bg_y1 < 0:
                    label_bg_y1 = y2
                    label_bg_y2 = y2 + label_size[1] + 10
                
                # 绘制标签背景
                cv2.rectangle(result_image, 
                             (label_bg_x1, label_bg_y1), 
                             (label_bg_x2, label_bg_y2), 
                             color, -1)
                
                # 绘制标签文本
                text_x = x1 + 5
                text_y = label_bg_y1 + label_size[1] + 5 if label_bg_y1 >= 0 else y2 + label_size[1] + 5
                
                cv2.putText(result_image, label, 
                           (text_x, text_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        logger.debug(f"成功绘制 {len(coordinates)} 个边界框")
        
    except Exception as e:
        logger.error(f"绘制边界框时发生错误: {e}")
    
    return result_image


def draw_detection_info(
    image: np.ndarray,
    detection_count: int,
    inference_time: float,
    position: Tuple[int, int] = (10, 30)
) -> np.ndarray:
    """
    在图像上绘制检测信息
    
    Args:
        image: 输入图像
        detection_count: 检测到的物体数量
        inference_time: 推理时间
        position: 文本位置 (x, y)
        
    Returns:
        绘制了信息的图像
    """
    if image is None:
        return np.zeros((480, 640, 3), dtype=np.uint8)
    
    result_image = image.copy()
    
    try:
        # 准备文本信息
        info_text = f"Objects: {detection_count} | Time: {inference_time:.2f}s"
        
        # 获取文本大小
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        thickness = 1
        text_size = cv2.getTextSize(info_text, font, font_scale, thickness)[0]
        
        # 绘制背景
        bg_x1, bg_y1 = position[0] - 5, position[1] - text_size[1] - 5
        bg_x2, bg_y2 = position[0] + text_size[0] + 5, position[1] + 5
        
        cv2.rectangle(result_image, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)
        
        # 绘制文本
        cv2.putText(result_image, info_text, position, font, font_scale, (255, 255, 255), thickness)
        
    except Exception as e:
        logger.error(f"绘制检测信息时发生错误: {e}")
    
    return result_image


def create_status_image(
    width: int = 640,
    height: int = 480,
    message: str = "Camera Not Available",
    color: Tuple[int, int, int] = (128, 128, 128)
) -> np.ndarray:
    """
    创建状态图像
    
    Args:
        width: 图像宽度
        height: 图像高度
        message: 状态消息
        color: 背景颜色 (B, G, R)
        
    Returns:
        状态图像
    """
    # 创建纯色背景
    image = np.full((height, width, 3), color, dtype=np.uint8)
    
    try:
        # 计算文本位置（居中）
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 1.0
        thickness = 2
        text_size = cv2.getTextSize(message, font, font_scale, thickness)[0]
        
        text_x = (width - text_size[0]) // 2
        text_y = (height + text_size[1]) // 2
        
        # 绘制文本
        cv2.putText(image, message, (text_x, text_y), font, font_scale, (255, 255, 255), thickness)
        
    except Exception as e:
        logger.error(f"创建状态图像时发生错误: {e}")
    
    return image
