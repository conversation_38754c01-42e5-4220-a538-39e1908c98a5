#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置加载器
Configuration Loader

负责从文件、环境变量等加载配置
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from .settings import Settings

logger = logging.getLogger("ConfigLoader")


def load_config(config_path: Optional[str] = None) -> Settings:
    """
    加载配置

    Args:
        config_path: 配置文件路径，如果为None则使用默认配置

    Returns:
        Settings: 配置对象
    """
    # 首先加载环境变量（包括.env文件）
    load_dotenv()

    # 获取API密钥并去除可能的引号
    api_key = os.getenv('SILICONFLOW_API_KEY', '').strip('"\'')

    # 如果还是没有API密钥，尝试直接从.env文件读取
    if not api_key:
        env_file = Path('.env')
        if env_file.exists():
            try:
                with open(env_file, 'r') as f:
                    for line in f:
                        if line.startswith('SILICONFLOW_API_KEY='):
                            api_key = line.split('=', 1)[1].strip().strip('"\'')
                            break
            except Exception as e:
                logger.warning(f"读取.env文件失败: {e}")

    # 默认配置
    default_config = {
        'web': {
            'host': os.getenv('HOST', '0.0.0.0'),
            'port': int(os.getenv('PORT', 5003)),
            'debug': os.getenv('DEBUG', 'false').lower() == 'true'
        },
        'mcp': {
            'name': 'llm-vision-control',
            'host': '0.0.0.0',
            'port': 8003,
            'timeout': 30.0
        },
        'camera': {
            'path': os.getenv('CAMERA_PATH', '/dev/video0'),
            'width': int(os.getenv('FRAME_WIDTH', 640)),
            'height': int(os.getenv('FRAME_HEIGHT', 480)),
            'fps': 30
        },
        'llm': {
            'api_key': api_key,
            'api_url': os.getenv('SILICONFLOW_API_URL', 'https://api.siliconflow.cn/v1/chat/completions'),
            'model_name': os.getenv('MODEL_NAME', 'Qwen/Qwen2-VL-72B-Instruct'),
            'max_tokens': 1024,
            'temperature': 0.1,
            'default_prompt': "请仔细观察图片中的所有主要物体，并使用JSON格式返回它们的边界框坐标。"
        },
        'logging': {
            'level': os.getenv('LOG_LEVEL', 'INFO'),
            'format': '[%(name)s] [%(asctime)s] [%(levelname)s] %(message)s',
            'date_format': '%Y-%m-%d %H:%M:%S',
            'file_path': os.getenv('LOG_FILE'),
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5
        }
    }
    
    # 如果指定了配置文件，则加载并合并
    if config_path:
        config_file = Path(config_path)
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    file_config = yaml.safe_load(f)
                    if file_config:
                        # 深度合并配置
                        default_config = _deep_merge(default_config, file_config)
                        # 确保API密钥不被配置文件覆盖
                        if api_key and 'llm' in default_config:
                            default_config['llm']['api_key'] = api_key
                        logger.info(f"已加载配置文件: {config_path}")
            except Exception as e:
                logger.warning(f"加载配置文件失败: {e}，使用默认配置")
        else:
            logger.warning(f"配置文件不存在: {config_path}，使用默认配置")
    
    # 验证必要的配置
    final_api_key = default_config['llm']['api_key']
    if not final_api_key:
        logger.warning("未设置SILICONFLOW_API_KEY，某些功能可能无法正常工作")
    else:
        logger.info("已成功加载SILICONFLOW_API_KEY")
    
    return Settings.from_dict(default_config)


def _deep_merge(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
    """
    深度合并两个字典
    
    Args:
        base: 基础字典
        override: 覆盖字典
        
    Returns:
        合并后的字典
    """
    result = base.copy()
    
    for key, value in override.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = _deep_merge(result[key], value)
        else:
            result[key] = value
    
    return result


def save_config(config: Settings, config_path: str):
    """
    保存配置到文件
    
    Args:
        config: 配置对象
        config_path: 配置文件路径
    """
    config_file = Path(config_path)
    config_file.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config.to_dict(), f, default_flow_style=False, allow_unicode=True)
        logger.info(f"配置已保存到: {config_path}")
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        raise
