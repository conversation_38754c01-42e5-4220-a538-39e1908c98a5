#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设置类定义
Settings Class Definition

定义系统配置的数据结构
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class WebConfig:
    """Web服务配置"""
    host: str = "0.0.0.0"
    port: int = 5003
    debug: bool = False


@dataclass
class MCPConfig:
    """MCP服务配置"""
    name: str = "llm-vision-control"
    host: str = "0.0.0.0"
    port: int = 8003
    timeout: float = 30.0


@dataclass
class CameraConfig:
    """摄像头配置"""
    path: str = "/dev/video0"
    width: int = 640
    height: int = 480
    fps: int = 30


@dataclass
class LLMConfig:
    """大语言模型配置"""
    api_key: str = ""
    api_url: str = "https://api.siliconflow.cn/v1/chat/completions"
    model_name: str = "Qwen/Qwen2-VL-72B-Instruct"
    max_tokens: int = 1024
    temperature: float = 0.1
    default_prompt: str = "请仔细观察图片中的所有主要物体，并使用JSON格式返回它们的边界框坐标。"


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "[%(name)s] [%(asctime)s] [%(levelname)s] %(message)s"
    date_format: str = "%Y-%m-%d %H:%M:%S"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class Settings:
    """系统设置"""
    web: WebConfig
    mcp: MCPConfig
    camera: CameraConfig
    llm: LLMConfig
    logging: LoggingConfig
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Settings':
        """从字典创建设置实例"""
        return cls(
            web=WebConfig(**data.get('web', {})),
            mcp=MCPConfig(**data.get('mcp', {})),
            camera=CameraConfig(**data.get('camera', {})),
            llm=LLMConfig(**data.get('llm', {})),
            logging=LoggingConfig(**data.get('logging', {}))
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'web': self.web.__dict__,
            'mcp': self.mcp.__dict__,
            'camera': self.camera.__dict__,
            'llm': self.llm.__dict__,
            'logging': self.logging.__dict__
        }
