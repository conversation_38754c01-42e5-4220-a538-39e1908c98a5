#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视觉系统
Vision System

整合摄像头管理、检测引擎和数据存储的核心系统
"""

import logging
import time
from typing import Optional, Tuple, List

from ..config.settings import Settings
from .camera_manager import CameraManager
from .detection_engine import DetectionEngine
from .data_store import DataStore

logger = logging.getLogger("VisionSystem")


class VisionSystem:
    """视觉系统类"""
    
    def __init__(self, config: Settings):
        """
        初始化视觉系统
        
        Args:
            config: 系统配置
        """
        self.config = config
        
        # 初始化组件
        self.camera_manager = CameraManager(config.camera)
        self.detection_engine = DetectionEngine(config.llm)
        self.data_store = DataStore()
        
        logger.info("视觉系统初始化完成")
    
    def start(self):
        """启动视觉系统"""
        try:
            # 启动摄像头
            self.camera_manager.start()
            logger.info("视觉系统启动成功")
        except Exception as e:
            logger.error(f"视觉系统启动失败: {e}")
            raise
    
    def stop(self):
        """停止视觉系统"""
        try:
            # 停止摄像头
            self.camera_manager.stop()
            logger.info("视觉系统已停止")
        except Exception as e:
            logger.error(f"停止视觉系统时出错: {e}")
    
    def detect_objects(self, prompt: str = None) -> Tuple[bool, dict]:
        """
        检测物体
        
        Args:
            prompt: 检测提示词，如果为None则使用默认提示词
            
        Returns:
            元组包含：
            - 是否成功
            - 结果字典
        """
        start_time = time.time()
        
        try:
            # 获取最新帧
            frame = self.camera_manager.get_latest_frame()
            if frame is None:
                error_msg = "无法从摄像头获取图像帧"
                logger.error(error_msg)
                return False, {"error": error_msg}
            
            # 使用默认提示词（如果未提供）
            if prompt is None:
                prompt = self.config.llm.default_prompt
            
            logger.info(f"开始检测，提示词: '{prompt}'")
            
            # 执行检测
            coordinates, description = self.detection_engine.detect(frame, prompt)
            inference_time = time.time() - start_time
            
            if coordinates is not None:
                # 验证坐标
                validated_coords = self.detection_engine.validate_coordinates(
                    coordinates, frame.shape
                )
                
                # 更新数据存储
                self.data_store.update(
                    prompt=prompt,
                    coordinates=validated_coords,
                    description=description or "",
                    inference_time=inference_time
                )
                
                logger.info(f"检测成功，找到 {len(validated_coords)} 个物体，耗时 {inference_time:.2f}s")
                
                return True, self.data_store.get_latest()
            else:
                error_msg = "检测失败，未返回有效坐标"
                logger.warning(error_msg)
                return False, {"error": error_msg}
                
        except Exception as e:
            error_msg = f"检测过程中发生异常: {e}"
            logger.error(error_msg)
            return False, {"error": error_msg}
    
    def get_latest_measurements(self) -> dict:
        """
        获取最新的测量结果
        
        Returns:
            最新的测量结果字典
        """
        return self.data_store.get_latest()
    
    def get_system_status(self) -> dict:
        """
        获取系统状态
        
        Returns:
            系统状态字典
        """
        latest_data = self.data_store.get_latest()
        camera_info = self.camera_manager.get_frame_info()
        
        return {
            "camera_running": self.camera_manager.is_running(),
            "camera_info": camera_info,
            "last_detection_timestamp": latest_data.get("last_update_timestamp"),
            "last_inference_time_s": latest_data.get("inference_time"),
            "detected_objects_count": latest_data.get("detection_count"),
            "has_detection_data": self.data_store.has_data()
        }
    
    def get_latest_frame(self):
        """
        获取最新图像帧
        
        Returns:
            最新的图像帧或None
        """
        return self.camera_manager.get_latest_frame()
    
    def get_coordinates(self) -> List[List[int]]:
        """
        获取最新的检测坐标
        
        Returns:
            坐标列表
        """
        return self.data_store.get_coordinates()
