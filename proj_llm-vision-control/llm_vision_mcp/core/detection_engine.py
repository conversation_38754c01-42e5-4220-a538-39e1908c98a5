#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检测引擎
Detection Engine

基于大语言模型的物体检测引擎
"""

import base64
import requests
import logging
import re
import json
from typing import List, Dict, Any, Optional, Tuple
import cv2
import numpy as np
from PIL import Image
import io

from ..config.settings import LLMConfig

logger = logging.getLogger("DetectionEngine")


class DetectionEngine:
    """检测引擎类"""
    
    def __init__(self, config: LLMConfig):
        """
        初始化检测引擎
        
        Args:
            config: LLM配置
        """
        self.config = config
        
        # 验证API密钥
        if not config.api_key:
            logger.warning("未设置API密钥，检测功能可能无法正常工作")
        
        logger.info(f"检测引擎初始化完成，模型: {config.model_name}")
    
    def detect(self, image: np.ndarray, prompt: str = None) -> Tuple[Optional[List[List[int]]], Optional[str]]:
        """
        检测图像中的物体
        
        Args:
            image: 输入图像（numpy数组）
            prompt: 检测提示词，如果为None则使用默认提示词
            
        Returns:
            元组包含：
            - 坐标列表 [[x1, y1, x2, y2], ...]
            - 模型返回的原始文本描述
        """
        if prompt is None:
            prompt = self.config.default_prompt
        
        logger.info(f"开始检测，提示词: '{prompt[:50]}...'")
        
        try:
            # 将图像转换为base64
            base64_image = self._image_to_base64(image)
            
            # 构建请求
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.config.model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": base64_image}}
                        ]
                    }
                ],
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature
            }

            # 发送请求
            response = requests.post(self.config.api_url, headers=headers, json=payload)
            response.raise_for_status()
            
            # 解析响应
            result = response.json()
            response_text = result['choices'][0]['message']['content']
            logger.info(f"模型响应: '{response_text[:100]}...'")
            
            # 解析坐标
            coordinates = self._parse_coordinates(response_text)
            
            return coordinates, response_text

        except requests.RequestException as e:
            logger.error(f"API请求失败: {e}")
        except Exception as e:
            logger.error(f"检测过程中发生异常: {e}")
            
        return None, None
    
    def _image_to_base64(self, image: np.ndarray) -> str:
        """
        将numpy数组图像转换为base64字符串
        
        Args:
            image: 输入图像
            
        Returns:
            base64编码的图像字符串
        """
        try:
            # 从BGR（OpenCV）转换为RGB（Pillow）
            image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pil_img = Image.fromarray(image_rgb)
            
            # 转换为JPEG格式的base64
            buffer = io.BytesIO()
            pil_img.save(buffer, format="JPEG")
            img_str = base64.b64encode(buffer.getvalue()).decode("utf-8")
            return f"data:image/jpeg;base64,{img_str}"
        except Exception as e:
            logger.error(f"图像转换base64失败: {e}")
            raise
    
    def _parse_coordinates(self, response_text: str) -> Optional[List[List[int]]]:
        """
        从模型响应文本中解析坐标
        
        Args:
            response_text: 模型响应文本
            
        Returns:
            坐标列表或None
        """
        try:
            # 尝试查找JSON对象
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    data = json.loads(json_match.group())
                    if 'coordinates' in data and isinstance(data['coordinates'], list):
                        return data['coordinates']
                except json.JSONDecodeError:
                    logger.warning("找到JSON格式文本但解析失败")

            # 回退到正则表达式匹配数组
            array_pattern = r'\[\s*(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\s*\]'
            matches = re.findall(array_pattern, response_text)
            if matches:
                return [[int(p) for p in match] for match in matches]

            return None
        except Exception as e:
            logger.error(f"解析坐标失败: {e}")
            return None
    
    def validate_coordinates(self, coordinates: List[List[int]], image_shape: Tuple[int, int]) -> List[List[int]]:
        """
        验证和修正坐标
        
        Args:
            coordinates: 坐标列表
            image_shape: 图像形状 (height, width)
            
        Returns:
            验证后的坐标列表
        """
        if not coordinates:
            return []
        
        height, width = image_shape[:2]
        validated_coords = []
        
        for coord in coordinates:
            if len(coord) != 4:
                logger.warning(f"无效坐标格式: {coord}")
                continue
            
            x1, y1, x2, y2 = coord
            
            # 确保坐标在图像范围内
            x1 = max(0, min(x1, width))
            y1 = max(0, min(y1, height))
            x2 = max(0, min(x2, width))
            y2 = max(0, min(y2, height))
            
            # 确保x1 < x2, y1 < y2
            if x1 >= x2 or y1 >= y2:
                logger.warning(f"无效边界框: [{x1}, {y1}, {x2}, {y2}]")
                continue
            
            validated_coords.append([x1, y1, x2, y2])
        
        return validated_coords
