#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据存储
Data Store

线程安全的数据存储，用于保存检测结果
"""

import threading
import time
from typing import Dict, Any, Optional, List

import logging

logger = logging.getLogger("DataStore")


class DataStore:
    """数据存储类"""
    
    def __init__(self):
        """初始化数据存储"""
        self._data = {}
        self._lock = threading.Lock()
        
        # 初始化默认数据
        self._reset_data()
        
        logger.info("数据存储初始化完成")
    
    def _reset_data(self):
        """重置数据为默认值"""
        self._data = {
            "prompt": "",
            "coordinates": [],
            "description": "",
            "inference_time": 0.0,
            "last_update_timestamp": 0.0,
            "detection_count": 0
        }
    
    def update(self, prompt: str, coordinates: List[List[int]], description: str, inference_time: float):
        """
        更新检测结果
        
        Args:
            prompt: 检测提示词
            coordinates: 检测到的坐标列表
            description: 模型返回的描述
            inference_time: 推理时间（秒）
        """
        with self._lock:
            self._data.update({
                "prompt": prompt,
                "coordinates": coordinates,
                "description": description,
                "inference_time": inference_time,
                "last_update_timestamp": time.time(),
                "detection_count": len(coordinates) if coordinates else 0
            })
        
        logger.info(f"数据存储已更新，检测到 {len(coordinates) if coordinates else 0} 个物体")
    
    def get_latest(self) -> Dict[str, Any]:
        """
        获取最新的检测结果
        
        Returns:
            包含最新检测结果的字典
        """
        with self._lock:
            return self._data.copy()
    
    def clear(self):
        """清空数据存储"""
        with self._lock:
            self._reset_data()
        
        logger.info("数据存储已清空")
    
    def get_coordinates(self) -> List[List[int]]:
        """
        获取最新的坐标列表
        
        Returns:
            坐标列表
        """
        with self._lock:
            return self._data.get("coordinates", []).copy()
    
    def has_data(self) -> bool:
        """
        检查是否有有效数据
        
        Returns:
            True如果有有效数据，否则False
        """
        with self._lock:
            return (
                self._data.get("last_update_timestamp", 0) > 0 and
                len(self._data.get("coordinates", [])) > 0
            )
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取数据摘要
        
        Returns:
            数据摘要字典
        """
        with self._lock:
            return {
                "has_data": self.has_data(),
                "detection_count": self._data.get("detection_count", 0),
                "last_update_timestamp": self._data.get("last_update_timestamp", 0),
                "inference_time": self._data.get("inference_time", 0)
            }
