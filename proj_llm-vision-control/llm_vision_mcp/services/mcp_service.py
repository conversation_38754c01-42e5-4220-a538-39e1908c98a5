#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MCP服务
MCP Service

基于FastMCP的MCP协议服务器，提供：
- AI助手集成接口
- 异步HTTP客户端
- 工具函数封装
- 生命周期管理
"""

import logging
import asyncio
from contextlib import asynccontextmanager
from typing import Dict, Any, Optional, List

import httpx
from fastmcp import FastMCP

from ..config.settings import Settings

logger = logging.getLogger("MCPService")


class MCPService:
    """MCP服务类"""
    
    def __init__(self, web_service_url: str, config: Settings):
        """
        初始化MCP服务
        
        Args:
            web_service_url: Web服务的基础URL
            config: 系统配置
        """
        self.web_service_url = web_service_url.rstrip('/')
        self.config = config
        self.http_client: Optional[httpx.AsyncClient] = None

        # 停止事件和服务器任务
        self._stop_event = asyncio.Event()
        self._server_task = None

        # 创建FastMCP实例
        self.mcp = FastMCP(
            name=config.mcp.name,
            lifespan=self._lifespan
        )

        # 注册MCP工具
        self._register_tools()

        logger.info("MCP服务初始化完成")
    
    @asynccontextmanager
    async def _lifespan(self, app):
        """MCP应用生命周期管理"""
        # 启动时创建HTTP客户端
        self.http_client = httpx.AsyncClient(timeout=self.config.mcp.timeout)
        logger.info("MCP HTTP客户端已创建")
        
        try:
            yield
        finally:
            # 关闭时释放HTTP客户端
            if self.http_client:
                await self.http_client.aclose()
                logger.info("MCP HTTP客户端已关闭")
    
    def _register_tools(self):
        """注册MCP工具函数"""
        
        @self.mcp.tool
        async def detect_objects(prompt: str = None) -> Dict[str, Any]:
            """
            检测图像中的物体
            
            Args:
                prompt: 检测提示词，如果不提供则使用默认提示词
            """
            try:
                payload = {}
                if prompt:
                    payload["prompt"] = prompt
                
                response = await self.http_client.post(
                    f"{self.web_service_url}/api/detect",
                    json=payload
                )
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"检测物体失败: {e}")
                return {"success": False, "error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"检测物体异常: {e}")
                return {"success": False, "error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def get_latest_measurements() -> Dict[str, Any]:
            """获取最新的检测结果"""
            try:
                response = await self.http_client.get(f"{self.web_service_url}/api/measurements")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"获取测量数据失败: {e}")
                return {"success": False, "error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"获取测量数据异常: {e}")
                return {"success": False, "error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def get_system_status() -> Dict[str, Any]:
            """获取系统状态"""
            try:
                response = await self.http_client.get(f"{self.web_service_url}/api/status")
                response.raise_for_status()
                return response.json()
            except httpx.HTTPError as e:
                logger.error(f"获取系统状态失败: {e}")
                return {"success": False, "error": f"HTTP error occurred: {e}"}
            except Exception as e:
                logger.error(f"获取系统状态异常: {e}")
                return {"success": False, "error": f"An error occurred: {e}"}
        
        @self.mcp.tool
        async def get_video_stream_url() -> Dict[str, Any]:
            """获取视频流URL"""
            try:
                video_url = f"{self.web_service_url}/video_feed"
                return {
                    "success": True,
                    "data": {
                        "video_stream_url": video_url,
                        "description": "实时视频流地址，可在浏览器中查看"
                    }
                }
            except Exception as e:
                logger.error(f"获取视频流URL异常: {e}")
                return {"success": False, "error": f"An error occurred: {e}"}
        
        logger.info("MCP工具函数注册完成")
    
    async def run(self):
        """运行MCP服务"""
        try:
            logger.info(f"MCP服务启动: http://{self.config.mcp.host}:{self.config.mcp.port}")

            # 创建服务器任务
            self._server_task = asyncio.create_task(
                self.mcp.run_async(
                    transport="http",
                    host=self.config.mcp.host,
                    port=self.config.mcp.port
                )
            )

            # 等待服务器任务完成或停止事件
            done, pending = await asyncio.wait(
                [self._server_task, asyncio.create_task(self._stop_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )

            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        except asyncio.CancelledError:
            logger.info("MCP服务被取消")
        except Exception as e:
            logger.error(f"MCP服务运行错误: {e}")
            raise
    
    async def stop(self):
        """停止MCP服务"""
        logger.info("正在停止MCP服务...")
        self._stop_event.set()
        
        if self._server_task and not self._server_task.done():
            self._server_task.cancel()
            try:
                await self._server_task
            except asyncio.CancelledError:
                pass
        
        logger.info("MCP服务已停止")
