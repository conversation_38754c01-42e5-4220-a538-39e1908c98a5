#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LLM Vision Control MCP Package
大模型视觉控制系统 MCP 包

基于FastMCP的MCP协议服务器，提供：
- 实时视频流处理
- 动态AI检测
- 按需触发检测
- 标准化API接口
"""

__version__ = "1.0.0"
__author__ = "LLM Vision Control Team"
__description__ = "LLM Vision Control MCP Server"

from .config import load_config, setup_logging
from .services import WebService, MCPService

__all__ = [
    "load_config",
    "setup_logging", 
    "WebService",
    "MCPService"
]
