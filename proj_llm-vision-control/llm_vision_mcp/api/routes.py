#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API路由
API Routes

定义Flask应用的所有API路由
"""

import cv2
import time
import logging
from flask import Flask, Response, jsonify, request

from ..core.vision_system import VisionSystem
from ..utils.visualization import draw_bounding_boxes

logger = logging.getLogger("APIRoutes")


def register_routes(app: Flask, vision_system: VisionSystem):
    """
    注册所有API路由
    
    Args:
        app: Flask应用实例
        vision_system: 视觉系统实例
    """
    
    def video_stream_generator():
        """
        视频流生成器，生成带有边界框的视频帧
        """
        while True:
            frame = vision_system.get_latest_frame()
            if frame is None:
                time.sleep(0.1)
                continue

            # 获取最新坐标并绘制边界框
            coordinates = vision_system.get_coordinates()
            processed_frame = draw_bounding_boxes(frame, coordinates)

            # 编码为JPEG
            ret, buffer = cv2.imencode('.jpg', processed_frame)
            if not ret:
                continue
            
            frame_bytes = buffer.tobytes()
            
            # 生成多部分响应
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
            time.sleep(0.03)  # 限制到约30fps

    @app.route('/video_feed')
    def video_feed():
        """视频流路由"""
        return Response(video_stream_generator(),
                        mimetype='multipart/x-mixed-replace; boundary=frame')

    @app.route('/api/detect', methods=['POST'])
    def detect():
        """
        按需检测端点，获取最新帧并运行检测
        """
        try:
            # 获取请求数据
            data = request.get_json() or {}
            prompt = data.get('prompt')
            
            if prompt is not None and not prompt.strip():
                return jsonify({"success": False, "error": "提示词不能为空"}), 400

            logger.info(f"收到检测请求，提示词: '{prompt or '使用默认提示词'}'")

            # 执行检测
            success, result = vision_system.detect_objects(prompt)
            
            if success:
                logger.info(f"检测成功，找到 {result.get('detection_count', 0)} 个物体")
                return jsonify({
                    "success": True,
                    "data": result
                })
            else:
                logger.warning(f"检测失败: {result.get('error', '未知错误')}")
                return jsonify({
                    "success": False, 
                    "error": result.get('error', '检测失败')
                }), 500

        except Exception as e:
            logger.exception("检测过程中发生异常")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route('/api/measurements', methods=['GET'])
    def get_measurements():
        """获取最新检测结果"""
        try:
            measurements = vision_system.get_latest_measurements()
            return jsonify({"success": True, "data": measurements})
        except Exception as e:
            logger.exception("获取测量数据时发生异常")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route('/api/status', methods=['GET'])
    def get_status():
        """获取系统状态"""
        try:
            status = vision_system.get_system_status()
            return jsonify({"success": True, "data": status})
        except Exception as e:
            logger.exception("获取系统状态时发生异常")
            return jsonify({"success": False, "error": str(e)}), 500

    @app.route('/api/health', methods=['GET'])
    def health_check():
        """健康检查端点"""
        try:
            status = vision_system.get_system_status()
            is_healthy = status.get('camera_running', False)
            
            return jsonify({
                "success": True,
                "data": {
                    "status": "healthy" if is_healthy else "unhealthy",
                    "timestamp": time.time(),
                    "camera_running": is_healthy
                }
            }), 200 if is_healthy else 503
        except Exception as e:
            logger.exception("健康检查时发生异常")
            return jsonify({
                "success": False,
                "error": str(e),
                "status": "unhealthy"
            }), 503

    @app.route('/', methods=['GET'])
    def index():
        """首页路由"""
        return jsonify({
            "name": "LLM Vision Control System",
            "version": "1.0.0",
            "description": "基于大语言模型的智能视觉控制系统",
            "endpoints": {
                "video_feed": "/video_feed",
                "detect": "/api/detect",
                "measurements": "/api/measurements", 
                "status": "/api/status",
                "health": "/api/health"
            }
        })

    logger.info("API路由注册完成")
