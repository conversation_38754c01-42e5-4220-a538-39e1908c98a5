# LLM Vision Control MCP服务器依赖
# LLM Vision Control MCP Server Dependencies

# 核心依赖
fastmcp>=0.2.0           # FastMCP框架
flask>=2.3.0             # Web框架
flask-cors>=4.0.0        # CORS支持

# 计算机视觉
opencv-python>=4.8.0     # OpenCV
Pillow>=10.0.0           # 图像处理

# HTTP客户端
httpx>=0.24.0            # 异步HTTP客户端
requests>=2.31.0         # 同步HTTP客户端

# 配置和环境
python-dotenv>=1.0.0     # 环境变量加载
PyYAML>=6.0              # YAML配置文件解析

# 数值计算
numpy>=1.24.0            # 数值计算

# 开发和测试（可选）
pytest>=7.0.0            # 测试框架
pytest-asyncio>=0.21.0   # 异步测试支持
