import cv2
import numpy as np
from typing import List

from src.vision.data_store import data_store

def draw_bounding_boxes(frame: np.ndarray, coordinates: List[List[int]]) -> np.ndarray:
    """
    Draws bounding boxes on a given frame.

    Args:
        frame: The image frame (numpy array).
        coordinates: A list of coordinates [[x1, y1, x2, y2], ...].

    Returns:
        The frame with bounding boxes drawn on it.
    """
    if not coordinates:
        return frame

    vis_frame = frame.copy()
    
    # Qwen2.5-VL returns coordinates in [0, 1000] space. We need to convert them.
    height, width, _ = frame.shape
    
    for coord in coordinates:
        if len(coord) >= 4:
            x1_norm, y1_norm, x2_norm, y2_norm = coord
            
            # Convert from 1000x1000 normalized space to original image space
            x1 = int(x1_norm * width / 1000)
            y1 = int(y1_norm * height / 1000)
            x2 = int(x2_norm * width / 1000)
            y2 = int(y2_norm * height / 1000)
            
            # Draw the rectangle
            cv2.rectangle(vis_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            
    return vis_frame

def get_frame_with_boxes(frame: np.ndarray) -> np.ndarray:
    """
    Gets the latest coordinates from the data store and draws them on the frame.
    """
    latest_coords = data_store.get_coordinates()
    return draw_bounding_boxes(frame, latest_coords)