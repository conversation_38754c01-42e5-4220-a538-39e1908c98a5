import logging
import cv2
import time
from flask import Flask, Response, jsonify, request
from flask_cors import CORS

from src.config import config
from src.vision.camera_manager import camera_manager
from src.vision.data_store import data_store
from src.vision.detection_engine import run_detection
from src.utils.visualization import draw_bounding_boxes, get_frame_with_boxes

# Configure logging
logging.basicConfig(level=logging.INFO,
                    format='[%(name)s] [%(asctime)s] [%(levelname)s] %(message)s',
                    datefmt='%Y-%m-%d %H:%M:%S')

logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

def video_stream_generator():
    """
    Generator for the video stream. It yields frames with the latest
    bounding boxes drawn on them.
    """
    while True:
        frame = camera_manager.get_latest_frame()
        if frame is None:
            time.sleep(0.1)
            continue

        # Get the latest coordinates and draw them on the frame
        processed_frame = get_frame_with_boxes(frame)

        # Encode the frame as JPEG
        ret, buffer = cv2.imencode('.jpg', processed_frame)
        if not ret:
            continue
        
        frame_bytes = buffer.tobytes()
        
        # Yield the frame in the multipart format
        yield (b'--frame\r\n'
               b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
        time.sleep(0.03) # Limit to ~30fps

@app.route('/video_feed')
def video_feed():
    """Video streaming route."""
    return Response(video_stream_generator(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/api/detect', methods=['POST'])
def detect():
    """
    On-demand detection endpoint. Grabs the latest frame, runs detection,
    and returns the results.
    """
    start_time = time.time()
    
    # Get prompt from request, or use default
    data = request.get_json()
    prompt = data.get('prompt', config.DEFAULT_PROMPT)
    if not prompt:
        return jsonify({"success": False, "error": "Prompt cannot be empty."}), 400

    logger.info(f"Received detection request with prompt: '{prompt}'")

    # Get the latest frame from the camera
    frame = camera_manager.get_latest_frame()
    if frame is None:
        logger.error("Could not get frame from camera for detection.")
        return jsonify({"success": False, "error": "Could not get frame from camera."}), 503

    # Run the detection
    try:
        coordinates, description = run_detection(frame, prompt)
        inference_time = time.time() - start_time
        
        if coordinates is not None:
            # Update the data store with the new results
            data_store.update(
                prompt=prompt,
                coordinates=coordinates,
                description=description,
                inference_time=inference_time
            )
            logger.info(f"Detection successful. Found {len(coordinates)} objects.")
            return jsonify({
                "success": True,
                "data": data_store.get_latest()
            })
        else:
            logger.warning("Detection ran but returned no coordinates.")
            return jsonify({"success": False, "error": "Detection failed to return coordinates."})

    except Exception as e:
        logger.exception("An error occurred during the detection process.")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/measurements', methods=['GET'])
def get_measurements():
    """Returns the latest detection measurements."""
    return jsonify({"success": True, "data": data_store.get_latest()})

@app.route('/api/status', methods=['GET'])
def get_status():
    """Returns the system status."""
    latest_data = data_store.get_latest()
    status = {
        "camera_running": camera_manager._is_running,
        "last_detection_timestamp": latest_data.get("last_update_timestamp"),
        "last_inference_time_s": latest_data.get("inference_time"),
        "detected_objects_count": latest_data.get("detection_count")
    }
    return jsonify({"success": True, "data": status})

def start_app():
    """Starts the camera and the Flask application."""
    logger.info("Starting camera manager...")
    camera_manager.start()
    
    # Give the camera a moment to initialize
    time.sleep(2)
    
    logger.info(f"Starting Flask server on http://{config.HOST}:{config.PORT}")
    app.run(host=config.HOST, port=config.PORT, threaded=True)

def stop_app():
    """Stops the camera manager."""
    logger.info("Stopping application...")
    camera_manager.stop()

if __name__ == '__main__':
    try:
        start_app()
    except KeyboardInterrupt:
        stop_app()