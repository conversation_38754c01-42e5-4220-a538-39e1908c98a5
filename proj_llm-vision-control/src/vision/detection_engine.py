import base64
import requests
import logging
import re
import json
from typing import List, Dict, Any, Optional, Tuple
import cv2
import numpy as np
from PIL import Image
import io

from src.config import config

logger = logging.getLogger(__name__)

def _image_to_base64(image: np.ndarray) -> str:
    """Encodes a numpy array image to a base64 string."""
    try:
        # Convert from BGR (OpenCV) to RGB (Pillow)
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        pil_img = Image.fromarray(image_rgb)
        
        buffer = io.BytesIO()
        pil_img.save(buffer, format="JPEG")
        img_str = base64.b64encode(buffer.getvalue()).decode("utf-8")
        return f"data:image/jpeg;base64,{img_str}"
    except Exception as e:
        logger.error(f"Failed to encode image to base64: {e}")
        raise

def _parse_coordinates(response_text: str) -> Optional[List[List[int]]]:
    """Parses coordinates from the model's text response."""
    try:
        # Try to find a JSON object in the response
        json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
        if json_match:
            try:
                data = json.loads(json_match.group())
                if 'coordinates' in data and isinstance(data['coordinates'], list):
                    return data['coordinates']
            except json.JSONDecodeError:
                logger.warning("Found a JSON-like string, but failed to decode.")

        # Fallback to regex for arrays if JSON fails
        array_pattern = r'\[\s*(\d+),\s*(\d+),\s*(\d+),\s*(\d+)\s*\]'
        matches = re.findall(array_pattern, response_text)
        if matches:
            return [[int(p) for p in match] for match in matches]

        return None
    except Exception as e:
        logger.error(f"Failed to parse coordinates: {e}")
        return None

def run_detection(image: np.ndarray, prompt: str) -> Tuple[Optional[List[List[int]]], Optional[str]]:
    """
    Runs detection on a single image frame using the Qwen2.5-VL model.

    Args:
        image: The image frame (numpy array).
        prompt: The detection prompt.

    Returns:
        A tuple containing:
        - A list of coordinates [[x1, y1, x2, y2], ...].
        - The raw text description from the model.
    """
    logger.info(f"Running detection with prompt: '{prompt[:50]}...'")
    
    try:
        base64_image = _image_to_base64(image)
        
        headers = {
            "Authorization": f"Bearer {config.SILICONFLOW_API_KEY}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": config.MODEL_NAME,
            "messages": [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": base64_image}}
                    ]
                }
            ],
            "max_tokens": 1024,
            "temperature": 0.1 # Lower temperature for more deterministic output
        }

        response = requests.post(config.SILICONFLOW_API_URL, headers=headers, json=payload)
        response.raise_for_status()
        
        result = response.json()
        response_text = result['choices'][0]['message']['content']
        logger.info(f"Model response: '{response_text[:100]}...'")
        
        coordinates = _parse_coordinates(response_text)
        
        return coordinates, response_text

    except requests.RequestException as e:
        logger.error(f"API request failed: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during detection: {e}")
        
    return None, None