# 2025-08-02
fix(server): 修复服务器优雅关闭时的异步事件循环嵌套错误

修复了统一MCP服务器在接收到停止信号（Ctrl+C）时出现的 `RuntimeError: This event loop is already running` 错误，实现了完全的优雅关闭机制。

### 1. 异步事件循环修复 (fix)
- **信号处理优化**: 重构信号处理器，使用 `shutdown_event` 事件机制替代直接的 `loop.run_until_complete()` 调用
- **嵌套循环避免**: 解决在已运行的事件循环中尝试启动新循环导致的 `RuntimeError`
- **事件驱动关闭**: 采用异步事件驱动的方式处理服务器关闭请求

### 2. 服务器生命周期管理 (feat)
- **任务协调**: 使用 `asyncio.wait()` 和 `FIRST_COMPLETED` 策略管理服务器任务和关闭事件
- **优雅停止**: 实现服务器任务的优雅取消和资源清理机制
- **超时保护**: 添加5秒超时机制，防止服务器停止过程无限等待

### 3. MCP服务停止机制完善 (feat)
- **停止事件**: 在 `MCPService` 中添加 `_stop_event` 异步事件
- **任务管理**: 添加 `_server_task` 属性跟踪服务器运行任务
- **取消逻辑**: 完善任务取消和异常处理逻辑，确保资源正确释放

### 4. 主服务器架构优化 (refactor)
- **全局事件**: 添加全局 `shutdown_event` 用于跨模块的关闭协调
- **启动流程**: 重构服务器启动流程，将MCP服务包装为可取消的异步任务
- **信号处理**: 简化信号处理器逻辑，避免在信号上下文中执行复杂异步操作

### 5. 错误处理增强 (feat)
- **异常捕获**: 完善 `asyncio.CancelledError` 和其他异步异常的处理
- **日志完整**: 确保关闭过程中的所有步骤都有相应的日志记录
- **状态跟踪**: 改进服务器状态跟踪，确保停止过程的可观测性

### 6. 资源清理保证 (feat)
- **任务清理**: 确保所有未完成的异步任务都能被正确取消
- **连接关闭**: 保证HTTP客户端连接和其他资源的正确释放
- **进程退出**: 确保进程能够以正确的退出码（0）正常结束

### 7. 测试验证 (feat)
- **关闭测试**: 验证使用 `Ctrl+C` 能够正常关闭服务器
- **错误消除**: 确认不再出现 `RuntimeError` 和 `RuntimeWarning` 
- **日志验证**: 验证完整的启动和关闭日志输出

### 8. 兼容性保持 (feat)
- **API不变**: 所有外部API接口保持不变
- **配置兼容**: 配置文件格式和参数完全兼容
- **功能完整**: 修复过程中未影响任何现有功能

### 修复前的错误信息:
- RuntimeError: This event loop is already running
- sys:1: RuntimeWarning: coroutine 'UnifiedServer.stop' was never awaited

### 修复后的错误信息:
- INFO: Shutting down
- INFO: Application shutdown complete.
- [VisionArmMCP] [19:17:41.648] [INFO] 接收到停止信号，正在关闭服务器...
- [MCPService] [19:17:41.650] [INFO] MCP服务正在停止...
- [MCPService] [19:17:41.651] [INFO] MCP服务已停止
- [VisionArmMCP] [19:17:41.652] [INFO] 统一服务器已停止

---

# 2025-08-02
feat(mcp): 完善MCP服务器接口，实现与EU03-ASR-LLM-TTS项目的旧Function Calling完全兼容

基于之前的统一MCP服务器架构，本次更新为MCP服务器添加了完整的机械臂控制接口，确保与旧Function Calling系统100%兼容，并完成了ASR-LLM-TTS项目的Function Calling到MCP的迁移。

### 1. MCP接口完善 (feat)
- **机械臂初始化**: 新增 `init_action` MCP工具和对应的 `/api/arm/init_action` HTTP接口
- **特殊放置模式**: 新增 `normal_put_object` 和 `stack_put_object` MCP工具，支持普通放置（60mm）和堆叠放置（140mm）
- **方向控制**: 新增6个方向控制MCP工具：`move_forward`、`move_back`、`move_left`、`move_right`、`move_up`、`move_down`
- **参数兼容**: 所有MCP工具参数格式与旧Function Calling完全一致，确保无缝迁移

### 2. HTTP API扩展 (feat)
- **初始化接口**: `POST /api/arm/init_action` - 机械臂初始化动作
- **放置接口**: `POST /api/arm/normal_put_object` 和 `POST /api/arm/stack_put_object` - 特殊放置模式
- **方向控制接口**: 6个方向移动接口，支持厘米级精确控制
- **参数验证**: 所有接口都包含完整的参数验证和错误处理

### 3. ASR-LLM-TTS项目迁移 (refactor)
- **Function Calling注释**: 注释掉机械臂相关的Function Calling注册，保留其他功能（音乐、TTS、YOLO等）
- **MCP配置优化**: 完善MCP客户端配置，确保连接稳定性
- **FC和MCP共存**: 实现Function Calling和MCP协议并存的架构，支持渐进式迁移
- **工具映射**: 建立完整的旧Function Calling到新MCP工具的映射关系

### 4. 接口覆盖验证 (feat)
- **完整覆盖**: MCP服务器现提供14个工具，完全覆盖旧Function Calling的所有功能
- **功能对等**: 每个MCP工具都与对应的Function Calling功能保持完全一致
- **参数兼容**: 保持相同的参数名称、类型和默认值，确保LLM调用的连续性

### 5. 工具清单更新 (feat)
- **物体检测**: `get_measurements`、`get_measurement_by_id`
- **系统状态**: `get_system_status`
- **基础控制**: `catch_object`、`put_object`
- **初始化**: `init_action`
- **特殊放置**: `normal_put_object`、`stack_put_object`
- **方向控制**: `move_forward`、`move_back`、`move_left`、`move_right`、`move_up`、`move_down`

### 6. 架构优化 (refactor)
- **简化方案**: 采用直接让LLM调用MCP工具的方式，避免创建复杂的适配器
- **错误处理**: 完善所有MCP工具的异常处理和日志记录
- **性能优化**: 优化HTTP客户端连接和请求处理逻辑

### 7. 兼容性保证 (feat)
- **向后兼容**: 旧的HTTP API接口保持不变，新增接口不影响现有功能
- **渐进迁移**: 支持Function Calling和MCP工具同时存在，便于逐步迁移
- **参数一致**: 确保所有接口参数格式与原始实现完全一致

### 8. 测试准备 (feat)
- **接口完整性**: 所有14个MCP工具都已实现并可供测试
- **连接验证**: MCP客户端连接已验证正常，可以调用远程工具
- **功能验证**: 准备进行完整的功能测试，验证迁移效果

---

# 2025-07-30
refactor(architecture): 重构项目架构为统一MCP服务器，优化配置管理和测试体系

基于之前的MCP服务器功能，本次更新进行了全面的架构重构，将项目转换为统一的MCP服务器架构，并大幅简化了配置管理和测试流程。

### 1. 架构重构 (refactor)
- **统一服务器架构**: 删除独立的 `fastmcp_server.py` 和 `main.py`，创建新的 `vision_arm_mcp/` 模块化架构
- **模块化设计**: 重新组织代码结构，将 `src/` 目录重构为 `vision_arm_mcp/` 包，包含核心逻辑、服务层、API层和配置管理
- **服务集成**: 在 `vision_arm_mcp/main.py` 中实现统一入口点，同时管理Web服务和MCP服务的生命周期
- **代码迁移**: 将所有核心模块从 `src/` 迁移到 `vision_arm_mcp/core/`，保持功能完整性

### 2. 配置管理优化 (feat)
- **配置简化**: 删除多个配置文件，统一使用 `config/default.yaml` 作为唯一配置文件
- **配置加载器**: 新增 `vision_arm_mcp/config/config_loader.py`，提供统一的配置加载和验证
- **类型安全**: 使用Pydantic进行配置数据验证和类型检查
- **日志优化**: 实现双级日志系统，文件记录全部日志，终端仅显示重要信息，并支持启动时清空日志文件

### 3. 服务层重构 (feat)
- **Web服务**: 重构 `vision_arm_mcp/services/web_service.py`，集成Flask应用和视频流处理
- **MCP服务**: 新增 `vision_arm_mcp/services/mcp_service.py`，基于FastMCP实现完整的MCP协议支持
- **视频服务**: 独立的 `vision_arm_mcp/services/video_service.py`，专门处理视频流和检测逻辑
- **API路由**: 新增 `vision_arm_mcp/api/routes.py` 和 `vision_arm_mcp/api/schemas.py`，提供结构化的API定义

### 4. 网络访问增强 (feat)
- **动态IP检测**: 实现自动检测所有网络接口，支持局域网访问
- **多接口显示**: 启动时显示所有可用的访问地址，包括localhost和LAN IP
- **网络配置**: 优化Web服务配置，支持0.0.0.0绑定以允许外部访问

### 5. 测试体系完善 (feat)
- **连通性测试**: 新增 `bin/test-services.sh`，提供全面的服务连通性测试
- **端点验证**: 测试所有HTTP端点、MCP协议连接和端口状态
- **智能检测**: 自动从配置文件读取端口信息，支持跨平台兼容
- **测试文档**: 新增 `docs/TESTING.md`，提供详细的测试指南和故障排除说明

### 6. 部署和运维 (feat)
- **启动脚本**: 新增 `bin/start-server.sh` 和 `bin/stop-server.sh`，简化服务管理
- **统一测试**: 新增 `tests/test_unified_server.py`，替代原有的分散测试文件
- **文档完善**: 新增多个文档文件，包括架构说明、配置指南、网络访问和清理报告

### 7. 代码清理 (chore)
- **删除冗余**: 移除旧的 `src/` 目录结构、独立的MCP服务器文件和过时的测试脚本
- **依赖更新**: 更新 `requirements.txt`，确保所有必要依赖的正确版本
- **文档更新**: 更新 `README.md`，反映新的架构和使用方式

### 8. 破坏性变更 (BREAKING CHANGE)
- **启动方式变更**: 原有的 `python main.py` 和 `python fastmcp_server.py` 启动方式已废弃
- **新启动方式**: 使用 `./bin/start-server.sh` 或 `python -m vision_arm_mcp.main` 启动统一服务器
- **配置文件变更**: 不再支持多环境配置文件，统一使用 `config/default.yaml`
- **API路径变更**: 部分API路径可能发生变化，请参考新的API文档

---

# 2025-07-29
feat(mcp): 重新集成MCP服务器功能，使用fastmcp包实现

在之前的提交中，我们撤销了MCP服务器功能集成，因为使用了错误的mcp python包。本次更新重新集成了MCP服务器功能，使用了正确的fastmcp包。

### 1. 新功能 (feat)
- **MCP服务器**: 新增 `fastmcp_server.py` 脚本，它使用 `FastMCP` 库将本项目的核心RESTful API（如 `/api/measurements`, `/api/arm/catch_object`）封装成MCP工具。
- **网络服务**: MCP服务器被配置为通过SSE（Server-Sent Events）在8002端口上运行，使其可以作为独立的网络服务被客户端连接。
- **测试脚本**: 新增 `test/test_mcp_server.py` 脚本，用于测试MCP服务器是否正常运行，包括启动服务器、连接服务器以及调用各个工具的测试。

### 2. 构建与环境 (chore)
- **依赖更新**: 在 `requirements.txt` 中添加了 `fastmcp` 依赖，以支持MCP服务器的运行。

---

# 2025-07-29
revert: 撤销MCP服务器功能集成

由于用错了mcp的python包，后续计划替换为fastmcp包。故本次更新是为了撤销于 2025-07-08 集成的MCP服务器功能。

### 1. 代码回滚 (revert)
- **删除MCP服务器**: 移除了 `vision_arm_server.py` 文件，该文件用于将项目API封装为MCP工具。
- **依赖清理**: 从 `requirements.txt` 中删除了 `httpx` 和 `mcp[cli]` 依赖，这些是MCP服务器所需的库。

---

# 2025-07-08
feat(mcp): 集成MCP服务，适配大语言模型Function Calling

为了将智能视觉机械臂的功能暴露给大语言模型，本次更新引入了模型上下文协议（MCP）的支持。通过创建一个独立的MCP服务器，本项目的核心API能够以工具（Tools）的形式被外部系统（如LLM）发现和调用。

### 1. 新功能 (feat)
- **MCP服务器**: 新增 `vision_arm_server.py` 脚本，它使用 `FastMCP` 库将本项目的核心RESTful API（如 `/api/measurements`, `/api/arm/catch_object`）封装成MCP工具。
- **网络服务**: MCP服务器被配置为通过SSE（Server-Sent Events）在8002端口上运行，使其可以作为独立的网络服务被客户端连接。

### 2. 构建与环境 (chore)
- **依赖更新**: 在 `requirements.txt` 中添加了 `httpx` 和 `mcp[cli]` 依赖，以支持MCP服务器的运行和网络通信。

---

# 2025-06-24
feat(project): 初始化智能视觉机械臂控制项目，建立基础框架和文档

本次提交是项目的首次提交，完成了从零到一的搭建，包括核心功能模块的初始化、基础文档的创建以及项目构建环境的配置。

### 1. 新功能 (feat)
- **核心模块**: 搭建了项目的基础框架，包括图像处理、机械臂控制和目标检测等核心功能模块的初步实现。
- **接口定义**: 定义了主要的类和函数接口，为后续的模块开发奠定了基础。

### 2. 文档 (docs)
- **README**: 创建了 `README.md` 文件，详细描述了项目目标、功能、安装指南和基本使用方法。
- **文档修正**: 对初始文档进行了校对和格式化，提升了可读性。

### 3. 构建与环境 (chore)
- **依赖管理**: 创建了 `requirements.txt` 文件，明确了项目运行所需的全部Python依赖库。
- **启动脚本**: 编写了基础的启动脚本，简化了项目的启动和测试流程。

---