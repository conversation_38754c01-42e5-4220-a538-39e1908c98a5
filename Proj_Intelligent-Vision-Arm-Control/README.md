# 智能视觉机械臂控制系统 (Intelligent Vision Arm Control System)

本项目是一个集成了实时物体检测与四自由度机械臂控制功能的智能系统。它利用YOLO11模型进行精确的视觉识别（例如形状、颜色分类），并通过API驱动机械臂执行如抓取、放置等自动化任务。系统支持通过USB摄像头输入，可在浏览器中流式展示检测结果，并提供丰富的API接口进行控制与数据交互。本系统基于RDK X5平台开发，现已重构为统一的MCP服务器架构。

## 🚀 新架构特性 (v2.0.0)

- 🎯 **统一MCP服务器**: 集成Web服务和MCP服务的统一入口点
- 🏗️ **模块化架构**: 清晰的代码组织和分层设计
- ⚙️ **YAML配置管理**: 灵活的配置系统，支持多环境
- 🔄 **异步服务协调**: 优雅的服务生命周期管理
- 🛠️ **MCP协议支持**: 与AI助手无缝集成
- 📊 **类型安全**: 使用Pydantic进行数据验证
- 🚦 **优雅关闭**: 完善的信号处理和资源清理

## 功能特性

- 🔍 实时视频输入支持
- 🎯 基于YOLO11的形状检测
- 📊 Web界面性能统计展示
- 🌐 浏览器实时查看检测结果
- 💻 与RDK X5硬件平台兼容
- 📏 坐标系测量功能（计算物体与中心距离）
- 📐 物体尺寸测量（宽度和高度）
- 🌍 四象限标注系统
- 🔄 RESTful API接口支持
- 🦾 机械臂控制接口 (抓取/放置，通过API调用)
- 🤖 MCP协议支持，可与AI助手集成

## 系统要求

- 硬件：RDK X5开发板或兼容的ARM架构处理器
- 操作系统：Ubuntu 20.04或更高版本
- Python 3.6+
- USB摄像头

## 快速开始

### 安装

1. 克隆仓库：

```bash
git clone https://github.com/your-username/Proj_Intelligent-Vision-Arm-Control.git
cd Proj_Intelligent-Vision-Arm-Control
```

2. 安装依赖：

```bash
pip install -r requirements.txt
```

3. 准备模型文件：
将量化后的BPU模型文件(.bin)放置在`models/`目录下

### 启动系统

#### 方式一：使用启动脚本（推荐）

```bash
# 启动统一服务器
./bin/start-server.sh

# 停止服务器
./bin/stop-server.sh
```

#### 方式二：直接运行Python模块

```bash
# 使用默认配置启动
python -m vision_arm_mcp.main

# 指定配置文件
python -m vision_arm_mcp.main --config config/custom.yaml
```

### 访问服务

启动后，系统会自动检测并显示所有可用的访问地址：

**本地访问:**
- **Web界面**: http://127.0.0.1:5002
- **MCP服务**: http://127.0.0.1:8002/mcp/
- **API文档**: http://127.0.0.1:5002/api/status

**局域网访问:**
- **Web界面**: http://************:5002 (主要)
- **MCP服务**: http://************:8002/mcp/ (主要)
- 其他网络接口地址...

> 💡 **提示**: 系统会动态检测所有网络接口的IP地址，标记为"主要"的IP地址是推荐使用的局域网访问地址。详细的网络配置说明请参考 [网络访问配置文档](docs/NETWORK_ACCESS.md)。

### 配置管理

系统使用YAML配置文件：

- `config/default.yaml`: 默认配置文件

可以通过环境变量覆盖配置（前缀为 `VISION_ARM_`）：

```bash
export VISION_ARM_WEB_PORT=8080
export VISION_ARM_MCP_PORT=9090
export VISION_ARM_LOGGING_CONSOLE_LEVEL=WARNING
```

## API接口

系统提供以下RESTful API接口：

#### 1. 获取全部测量数据

- **URL**: `/api/measurements`
- **方法**: GET
- **返回**: 包含所有检测对象测量数据的JSON数组

示例响应：
```json
{
  "measurements": [
    {
      "id": 1,
      "class": "red_shape",
      "confidence": 0.92,
      "position": {
        "x": 320,
        "y": 240
      },
      "distance_from_center": 150.2,
      "dimensions": {
        "width": 120,
        "height": 80
      },
      "quadrant": "第一象限",
      "timestamp": "2023-07-25T15:30:45"
    },
    ...
  ],
  "count": 3
}
```

#### 2. 获取特定ID的测量数据

- **URL**: `/api/measurements/<object_id>`
- **方法**: GET
- **参数**: `object_id` - 对象ID
- **返回**: 包含特定对象测量数据的JSON对象

示例响应：
```json
{
  "id": 1,
  "class": "red_shape",
  "confidence": 0.92,
  "position": {
    "x": 320,
    "y": 240
  },
  "distance_from_center": 150.2,
  "dimensions": {
    "width": 120,
    "height": 80
  },
  "quadrant": "第一象限",
  "timestamp": "2023-07-25T15:30:45"
}
```

#### 3. 获取实时视频流

- **URL**: `/video_feed`
- **方法**: GET
- **返回**: MJPEG视频流

#### 4. 控制机械臂抓取物体

- **URL**: `/api/arm/catch_object`
- **方法**: POST
- **请求体 (JSON)**:
  ```json
  {
    "image_x": 0.0,  // 物体相对于图像中心的X坐标
    "image_y": 0.0,  // 物体相对于图像中心的Y坐标
    "height_z": 50.0 // 抓取的目标Z高度
  }
  ```
- **成功返回 (200 OK)**:
  ```json
  {
    "status": "success",
    "message": "抓取动作已执行"
  }
  ```
- **失败返回 (例如 400, 500, 503)**:
  ```json
  {
    "status": "error",
    "message": "错误描述"
  }
  ```

#### 5. 控制机械臂放置物体

- **URL**: `/api/arm/put_object`
- **方法**: POST
- **请求体 (JSON)**:
  ```json
  {
    "image_x": 0.0,  // 目标放置位置相对于图像中心的X坐标
    "image_y": 0.0,  // 目标放置位置相对于图像中心的Y坐标
    "height_z": 60.0 // 放置的目标Z高度
  }
  ```
- **成功返回 (200 OK)**:
  ```json
  {
    "status": "success",
    "message": "放置动作已执行"
  }
  ```
- **失败返回 (例如 400, 500, 503)**:
  ```json
  {
    "status": "error",
    "message": "错误描述"
  }
  ```

### 坐标系统和测量功能

本系统提供以下测量功能：

1. 坐标系统：
   - 在画面中心绘制XY坐标轴
   - 坐标系以右上为第一象限，顺时针增大象限
   - X轴从左到右为正，Y轴从下到上为正（注意：与图像坐标系相反）
   - 原点位于画面中心
   - 显示象限标识（Ⅰ、Ⅱ、Ⅲ、Ⅳ）

2. 距离测量：
   - 计算检测框中心到坐标原点的距离
   - 显示X方向距离、Y方向距离、所在象限和直线距离（单位：像素）
   - 在检测框和原点之间绘制连接线

3. 尺寸测量：
   - 计算并显示每个检测框的宽度和高度（单位：像素）

这些测量功能可以通过`config/default.yaml`配置文件进行配置：

```yaml
measurement:
  enable_coordinate_system: true    # 启用/禁用坐标系统显示
  enable_distance_measurement: true # 启用/禁用距离测量
  enable_dimension_measurement: true # 启用/禁用尺寸测量
```

### API客户端

系统附带一个命令行API客户端，可以在终端中显示检测结果和系统状态：

```bash
python api_client.py --host localhost --port 5000
```

客户端支持以下命令行参数：

- `--host`: 服务器主机地址，默认为localhost
- `--port`: 服务器端口，默认为5000
- `--interval`: 更新间隔（秒），默认为1.0秒
- `--verbose`: 显示详细信息
- `--object-id`: 获取特定物体ID的信息
- `--status-only`: 只显示系统状态信息
- `--test-catch`: 测试机械臂抓取API (需要配合 --catch-x, --catch-y, --catch-z)
- `--catch-x`: 抓取目标的X坐标 (预期为相对于图像中心的坐标)
- `--catch-y`: 抓取目标的Y坐标 (预期为相对于图像中心的坐标)
- `--catch-z`: 抓取目标的Z高度
- `--test-put`: 测试机械臂放置API (可配合 --put-x, --put-y, --put-z)
- `--put-x`: 放置目标的X坐标 (预期为相对于图像中心的坐标)
- `--put-y`: 放置目标的Y坐标 (预期为相对于图像中心的坐标)
- `--put-z`: 放置目标的Z高度
- `--interactive-arm-test`: 进入交互式视觉引导的机械臂抓取与放置测试流程

示例：

1. 监控所有检测物体：
   ```bash
   python api_client.py
   ```

2. 每0.5秒更新一次，显示详细信息：
   ```bash
   python api_client.py --interval 0.5 --verbose
   ```

3. 只显示ID为0的物体信息：
   ```bash
   python api_client.py --object-id 0
   ```

4. 只显示系统状态：
   ```bash
   python api_client.py --status-only
   ```

客户端将使用彩色输出显示检测结果，包括物体的类别、置信度、位置和尺寸信息。

## 项目架构

### 新架构目录结构 (v2.0.0)

```
Proj_Intelligent-Vision-Arm-Control/
├── vision_arm_mcp/         # 主要源代码目录
│   ├── __init__.py         # 包初始化
│   ├── main.py             # 统一入口点
│   ├── config/             # 配置管理
│   │   ├── __init__.py
│   │   ├── settings.py     # 配置数据模型
│   │   └── config_loader.py # 配置加载器
│   ├── core/               # 核心业务逻辑
│   │   ├── __init__.py
│   │   ├── detection/      # 物体检测模块
│   │   │   ├── __init__.py
│   │   │   ├── base_model.py
│   │   │   └── yolo_detect.py
│   │   ├── arm_control/    # 机械臂控制模块
│   │   │   ├── __init__.py
│   │   │   ├── arm_move.py
│   │   │   ├── user_arm.py
│   │   │   ├── serial_section.py
│   │   │   └── servo_section.py
│   │   └── utils/          # 工具模块
│   │       ├── __init__.py
│   │       ├── measurement.py
│   │       └── visualization.py
│   ├── services/           # 服务层
│   │   ├── __init__.py
│   │   ├── web_service.py  # Web服务
│   │   ├── mcp_service.py  # MCP服务
│   │   └── video_service.py # 视频流服务
│   └── api/                # API层
│       ├── __init__.py
│       ├── routes.py       # 路由定义
│       └── schemas.py      # 数据模型
├── config/                 # 配置文件
│   └── default.yaml        # 默认配置
├── bin/                    # 脚本目录
│   ├── start-server.sh     # 启动脚本
│   └── stop-server.sh      # 停止脚本
├── tests/                  # 测试代码
│   └── test_unified_server.py
├── docs/                   # 文档目录
├── logs/                   # 日志目录
├── models/                 # 模型文件目录
├── requirements.txt        # 依赖包列表
├── README.md               # 项目说明文档
└── README_NEW_ARCHITECTURE.md # 新架构说明
```

### 架构特点

1. **统一服务器**: 单一入口点管理所有服务
2. **分层设计**: 清晰的核心逻辑、服务层、API层分离
3. **配置管理**: YAML配置文件，支持多环境
4. **类型安全**: 使用Pydantic进行数据验证
5. **异步协调**: 优雅的服务生命周期管理

## MCP协议集成

本系统现已支持MCP (Model Context Protocol) 协议，可以与AI助手无缝集成。

### MCP工具

系统提供以下MCP工具：

- `get_measurements()` - 获取所有测量数据
- `get_measurement_by_id(object_id)` - 获取特定物体数据
- `catch_object(x, y, z)` - 抓取物体
- `put_object(x, y, z)` - 放置物体
- `get_system_status()` - 获取系统状态
- `get_available_tools()` - 获取可用工具列表

### 与AI助手集成

1. 启动统一服务器：

```bash
./bin/start-server.sh
```

2. MCP服务将在 http://localhost:8002 上提供服务

3. AI助手可以通过MCP协议调用系统功能

### 语音命令示例

通过MCP集成，用户可以使用自然语言与系统交互：

- "当前画面中有哪些物体？"
- "一共检测到几个物体？"
- "有多少个红色物体？"
- "物体都在哪些位置？"
- "哪个象限有物体？"
- "抓取位置在(100, 50)的物体"
- "将物体放置到(200, 100)位置"

## 架构说明

本项目采用统一的MCP服务器架构，详细说明请参考：

- `README_NEW_ARCHITECTURE.md` - 新架构详细说明

## 开发与测试

### 运行测试

```bash
# 运行所有测试
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_unified_server.py -v
```

### 代码格式化

```bash
# 格式化代码
black vision_arm_mcp/

# 类型检查
mypy vision_arm_mcp/
```

## 许可证

本项目使用MIT许可证。详情见LICENSE文件。