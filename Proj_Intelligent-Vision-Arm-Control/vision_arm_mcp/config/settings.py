#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置数据模型
Configuration Data Models

定义系统的所有配置项，使用Pydantic进行数据验证。
"""

import os
from pathlib import Path
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

class LoggingConfig(BaseModel):
    """日志配置"""
    file_level: str = Field(default="DEBUG", description="文件日志级别")
    console_level: str = Field(default="WARNING", description="控制台日志级别")
    format: str = Field(
        default="[%(name)s] [%(asctime)s.%(msecs)03d] [%(levelname)s] %(message)s",
        description="日志格式"
    )
    datefmt: str = Field(default="%H:%M:%S", description="时间格式")
    file_path: Optional[str] = Field(default=None, description="日志文件路径")
    max_file_size: int = Field(default=10485760, description="日志文件最大大小(字节)")
    backup_count: int = Field(default=5, description="日志文件备份数量")

class ModelConfig(BaseModel):
    """模型配置"""
    path: str = Field(
        default=str(PROJECT_ROOT / "models" / "yolo11n_shape_detect_bayese_320x320_nv12_fix_forward.bin"),
        description="模型文件路径"
    )
    confidence_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="置信度阈值")
    iou_threshold: float = Field(default=0.45, ge=0.0, le=1.0, description="IoU阈值")
    
    @validator('path')
    def validate_model_path(cls, v):
        if not os.path.exists(v):
            raise ValueError(f"模型文件不存在: {v}")
        return v

class CameraConfig(BaseModel):
    """摄像头配置"""
    device_path: str = Field(default="/dev/video0", description="摄像头设备路径")
    width: int = Field(default=640, gt=0, description="图像宽度")
    height: int = Field(default=480, gt=0, description="图像高度")
    fps: int = Field(default=30, gt=0, description="帧率")

class WebConfig(BaseModel):
    """Web服务配置"""
    host: str = Field(default="0.0.0.0", description="监听地址")
    port: int = Field(default=5002, ge=1, le=65535, description="监听端口")
    debug: bool = Field(default=False, description="调试模式")
    threaded: bool = Field(default=True, description="多线程模式")

class MCPConfig(BaseModel):
    """MCP服务配置"""
    host: str = Field(default="0.0.0.0", description="监听地址")
    port: int = Field(default=8002, ge=1, le=65535, description="监听端口")
    name: str = Field(default="VisionArmServer", description="服务名称")
    description: str = Field(
        default="智能视觉机械臂控制系统MCP服务器",
        description="服务描述"
    )

class ArmCalibrationConfig(BaseModel):
    """机械臂标定配置"""
    x_coefficients: List[float] = Field(
        default=[0.000004, 0.9879, -2.9465],
        description="X轴标定系数"
    )
    y_coefficients: List[float] = Field(
        default=[0.0005, 0.9638, 228.29],
        description="Y轴标定系数"
    )

class WorkspaceLimits(BaseModel):
    """工作空间限制"""
    x_range: List[float] = Field(default=[-200, 200], description="X轴范围")
    y_range: List[float] = Field(default=[100, 400], description="Y轴范围")
    z_range: List[float] = Field(default=[30, 150], description="Z轴范围")

class ArmConfig(BaseModel):
    """机械臂配置"""
    serial_port: str = Field(default="/dev/ttyS1", description="串口设备")
    baudrate: int = Field(default=115200, description="波特率")
    calibration: ArmCalibrationConfig = Field(default_factory=ArmCalibrationConfig)
    workspace_limits: WorkspaceLimits = Field(default_factory=WorkspaceLimits)

class DetectionConfig(BaseModel):
    """检测配置"""
    class_names: List[str] = Field(
        default=["orange", "green", "red"],
        description="检测类别名称"
    )
    colors: List[List[int]] = Field(
        default=[[255, 165, 0], [0, 255, 0], [255, 0, 0]],
        description="检测框颜色"
    )
    font_scale: float = Field(default=1.0, description="字体缩放比例")
    line_thickness: int = Field(default=2, description="检测框线条粗细")

class MeasurementConfig(BaseModel):
    """测量配置"""
    enable_coordinate_system: bool = Field(default=True, description="启用坐标系统")
    enable_distance_measurement: bool = Field(default=True, description="启用距离测量")
    enable_dimension_measurement: bool = Field(default=True, description="启用尺寸测量")
    axis_color: List[int] = Field(default=[255, 255, 255], description="坐标轴颜色")
    measurement_color: List[int] = Field(default=[255, 255, 0], description="测量数据颜色")
    axis_length_percentage: float = Field(default=0.8, description="坐标轴长度比例")
    axis_thickness: int = Field(default=2, description="坐标轴线条粗细")
    measurement_line_thickness: int = Field(default=2, description="测量线条粗细")

class Settings(BaseModel):
    """主配置类"""
    # 基本信息
    app_name: str = Field(default="智能视觉机械臂控制系统", description="应用名称")
    version: str = Field(default="2.0.0", description="版本号")
    environment: str = Field(default="development", description="运行环境")
    
    # 各模块配置
    logging: LoggingConfig = Field(default_factory=LoggingConfig)
    model: ModelConfig = Field(default_factory=ModelConfig)
    camera: CameraConfig = Field(default_factory=CameraConfig)
    web: WebConfig = Field(default_factory=WebConfig)
    mcp: MCPConfig = Field(default_factory=MCPConfig)
    arm: ArmConfig = Field(default_factory=ArmConfig)
    detection: DetectionConfig = Field(default_factory=DetectionConfig)
    measurement: MeasurementConfig = Field(default_factory=MeasurementConfig)
    
    class Config:
        """Pydantic配置"""
        env_prefix = "VISION_ARM_"  # 环境变量前缀
        case_sensitive = False
        
    def dict_for_logging(self) -> Dict[str, Any]:
        """返回适合日志记录的配置字典（隐藏敏感信息）"""
        config_dict = self.dict()
        # 这里可以添加敏感信息过滤逻辑
        return config_dict
