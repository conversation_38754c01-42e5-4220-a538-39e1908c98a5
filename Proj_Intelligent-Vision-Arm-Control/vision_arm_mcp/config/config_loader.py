#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置加载器
Configuration Loader

负责从配置文件、环境变量等来源加载配置，
并提供日志系统的初始化功能。
"""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any

import yaml
from pydantic import ValidationError

from .settings import Settings, LoggingConfig

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

def load_yaml_config(config_path: str) -> Dict[str, Any]:
    """
    从YAML文件加载配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置字典
        
    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML解析错误
    """
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f) or {}



def load_config(config_path: Optional[str] = None) -> Settings:
    """
    加载系统配置

    Args:
        config_path: 配置文件路径，如果为None则使用默认配置

    Returns:
        Settings实例

    Raises:
        ValidationError: 配置验证失败
    """
    # 确定要使用的配置文件路径
    if config_path:
        # 使用用户指定的配置文件
        config_file_path = config_path
    else:
        # 使用默认配置文件
        config_file_path = str(PROJECT_ROOT / "config" / "default.yaml")

    # 加载配置文件
    try:
        config_data = load_yaml_config(config_file_path)
    except Exception as e:
        print(f"错误: 无法加载配置文件 {config_file_path}: {e}")
        raise

    # 创建Settings实例（会自动从环境变量读取配置）
    try:
        settings = Settings(**config_data)
        return settings
    except ValidationError as e:
        print(f"配置验证失败: {e}")
        raise

def setup_logging(logging_config: LoggingConfig):
    """
    设置日志系统

    Args:
        logging_config: 日志配置
    """
    # 创建日志目录
    if logging_config.file_path:
        log_file = Path(logging_config.file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)

    # 配置根日志器 - 设置为最低级别以便所有日志都能被处理器接收
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)

    # 清除现有处理器
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # 创建格式器
    formatter = logging.Formatter(
        fmt=logging_config.format,
        datefmt=logging_config.datefmt
    )

    # 控制台处理器 - 只输出重要信息
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, logging_config.console_level.upper()))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

    # 文件处理器（如果配置了文件路径）- 记录全部等级的日志
    if logging_config.file_path:
        # 清空现有日志文件内容（每次启动时重新开始）
        try:
            with open(logging_config.file_path, 'w', encoding='utf-8') as f:
                f.write('')  # 清空文件内容
        except Exception as e:
            print(f"警告: 无法清空日志文件 {logging_config.file_path}: {e}")

        file_handler = logging.handlers.RotatingFileHandler(
            filename=logging_config.file_path,
            maxBytes=logging_config.max_file_size,
            backupCount=logging_config.backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(getattr(logging, logging_config.file_level.upper()))
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)

    # 设置第三方库的日志级别
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("werkzeug").setLevel(logging.WARNING)

    # 记录日志系统初始化完成
    logger = logging.getLogger("ConfigLoader")
    logger.info("日志系统初始化完成")
    logger.info(f"文件日志级别: {logging_config.file_level}")
    logger.info(f"控制台日志级别: {logging_config.console_level}")
    if logging_config.file_path:
        logger.info(f"日志文件: {logging_config.file_path} (已清空重新开始)")

def get_config_summary(settings: Settings) -> str:
    """
    获取配置摘要信息
    
    Args:
        settings: 配置实例
        
    Returns:
        配置摘要字符串
    """
    summary = f"""
配置摘要:
  应用名称: {settings.app_name}
  版本: {settings.version}
  环境: {settings.environment}
  Web服务: {settings.web.host}:{settings.web.port}
  MCP服务: {settings.mcp.host}:{settings.mcp.port}
  模型文件: {settings.model.path}
  摄像头: {settings.camera.device_path}
  机械臂串口: {settings.arm.serial_port}
"""
    return summary.strip()
