#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API路由定义
API Routes Definition

定义系统的所有API路由和处理逻辑。
"""

import logging
from flask import Blueprint, jsonify, request
from typing import Dict, Any

from .schemas import (
    SystemStatusResponse,
    MeasurementResponse,
    ArmActionRequest,
    ArmActionResponse,
    ErrorResponse,
    MCPToolsResponse,
    ConfigSummaryResponse
)

logger = logging.getLogger("APIRoutes")

# 创建API蓝图
api_bp = Blueprint('api', __name__, url_prefix='/api')

def create_api_routes(web_service):
    """
    创建API路由
    
    Args:
        web_service: Web服务实例
    """
    
    @api_bp.route('/status', methods=['GET'])
    def get_system_status():
        """获取系统状态"""
        try:
            status_data = {
                "status": "running" if web_service.running else "stopped",
                "fps": web_service.fps_counter.fps,
                "detection_count": len(web_service.measurement_collection.measurements),
                "inference_time": getattr(web_service, '_last_inference_time', 0),
                "camera_available": web_service.camera is not None and web_service.camera.isOpened(),
                "arm_available": web_service.robot_arm is not None
            }
            
            # 验证响应数据
            response = SystemStatusResponse(**status_data)
            return jsonify(response.dict())
            
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            error = ErrorResponse(message=f"获取系统状态失败: {str(e)}")
            return jsonify(error.dict()), 500
    
    @api_bp.route('/measurements', methods=['GET'])
    def get_measurements():
        """获取所有测量数据"""
        try:
            measurements = []
            for m in web_service.measurement_collection.measurements:
                measurement_data = {
                    "object_id": m.object_id,
                    "class_name": m.class_name,
                    "confidence": m.confidence,
                    "bbox": m.bbox,
                    "center_pixel": m.center_pixel,
                    "center_world": m.center_world,
                    "dimensions_pixel": m.dimensions_pixel,
                    "dimensions_world": m.dimensions_world,
                    "area_pixel": m.area_pixel,
                    "area_world": m.area_world,
                    "timestamp": m.timestamp
                }
                measurements.append(measurement_data)
            
            response_data = {
                "measurements": measurements,
                "count": len(measurements),
                "timestamp": web_service.measurement_collection.last_update_time
            }
            
            response = MeasurementResponse(**response_data)
            return jsonify(response.dict())
            
        except Exception as e:
            logger.error(f"获取测量数据失败: {e}")
            error = ErrorResponse(message=f"获取测量数据失败: {str(e)}")
            return jsonify(error.dict()), 500
    
    @api_bp.route('/measurements/<int:object_id>', methods=['GET'])
    def get_measurement_by_id(object_id: int):
        """获取特定物体的测量数据"""
        try:
            measurement = web_service.measurement_collection.get_by_id(object_id)
            if measurement:
                measurement_data = {
                    "object_id": measurement.object_id,
                    "class_name": measurement.class_name,
                    "confidence": measurement.confidence,
                    "bbox": measurement.bbox,
                    "center_pixel": measurement.center_pixel,
                    "center_world": measurement.center_world,
                    "dimensions_pixel": measurement.dimensions_pixel,
                    "dimensions_world": measurement.dimensions_world,
                    "area_pixel": measurement.area_pixel,
                    "area_world": measurement.area_world,
                    "timestamp": measurement.timestamp
                }
                return jsonify(measurement_data)
            else:
                error = ErrorResponse(message="Object not found", error_code="OBJECT_NOT_FOUND")
                return jsonify(error.dict()), 404
                
        except Exception as e:
            logger.error(f"获取物体 {object_id} 测量数据失败: {e}")
            error = ErrorResponse(message=f"获取测量数据失败: {str(e)}")
            return jsonify(error.dict()), 500
    
    @api_bp.route('/arm/catch_object', methods=['POST'])
    def arm_catch_object():
        """机械臂抓取物体"""
        if web_service.robot_arm is None:
            error = ErrorResponse(message="机械臂未初始化", error_code="ARM_NOT_AVAILABLE")
            return jsonify(error.dict()), 503
        
        try:
            # 验证请求数据
            request_data = request.get_json()
            if not request_data:
                error = ErrorResponse(message="请求数据为空", error_code="INVALID_REQUEST")
                return jsonify(error.dict()), 400
            
            arm_request = ArmActionRequest(**request_data)
            
            # 执行抓取动作
            import time
            start_time = time.time()
            
            web_service.robot_arm.arm_catch_sth(
                arm_request.image_x,
                arm_request.image_y,
                arm_request.height_z
            )
            
            execution_time = (time.time() - start_time) * 1000
            
            response_data = {
                "status": "success",
                "message": "抓取动作已执行",
                "action_type": "catch",
                "coordinates": {
                    "image_x": arm_request.image_x,
                    "image_y": arm_request.image_y,
                    "height_z": arm_request.height_z
                },
                "execution_time": execution_time
            }
            
            response = ArmActionResponse(**response_data)
            return jsonify(response.dict())
            
        except Exception as e:
            logger.error(f"执行抓取动作失败: {str(e)}")
            error = ErrorResponse(message=f"执行失败: {str(e)}", error_code="EXECUTION_FAILED")
            return jsonify(error.dict()), 500
    
    @api_bp.route('/arm/put_object', methods=['POST'])
    def arm_put_object():
        """机械臂放置物体"""
        if web_service.robot_arm is None:
            error = ErrorResponse(message="机械臂未初始化", error_code="ARM_NOT_AVAILABLE")
            return jsonify(error.dict()), 503
        
        try:
            # 验证请求数据
            request_data = request.get_json()
            if not request_data:
                error = ErrorResponse(message="请求数据为空", error_code="INVALID_REQUEST")
                return jsonify(error.dict()), 400
            
            arm_request = ArmActionRequest(**request_data)
            
            # 执行放置动作
            import time
            start_time = time.time()
            
            web_service.robot_arm.arm_put_sth(
                arm_request.image_x,
                arm_request.image_y,
                arm_request.height_z
            )
            
            execution_time = (time.time() - start_time) * 1000
            
            response_data = {
                "status": "success",
                "message": "放置动作已执行",
                "action_type": "put",
                "coordinates": {
                    "image_x": arm_request.image_x,
                    "image_y": arm_request.image_y,
                    "height_z": arm_request.height_z
                },
                "execution_time": execution_time
            }
            
            response = ArmActionResponse(**response_data)
            return jsonify(response.dict())
            
        except Exception as e:
            logger.error(f"执行放置动作失败: {str(e)}")
            error = ErrorResponse(message=f"执行失败: {str(e)}", error_code="EXECUTION_FAILED")
            return jsonify(error.dict()), 500
    
    @api_bp.route('/config', methods=['GET'])
    def get_config_summary():
        """获取配置摘要"""
        try:
            config = web_service.config
            
            summary_data = {
                "app_name": config.app_name,
                "version": config.version,
                "environment": config.environment,
                "web_service": f"{config.web.host}:{config.web.port}",
                "mcp_service": f"{config.mcp.host}:{config.mcp.port}",
                "model_path": config.model.path,
                "camera_device": config.camera.device_path,
                "arm_serial_port": config.arm.serial_port
            }
            
            response = ConfigSummaryResponse(**summary_data)
            return jsonify(response.dict())
            
        except Exception as e:
            logger.error(f"获取配置摘要失败: {e}")
            error = ErrorResponse(message=f"获取配置摘要失败: {str(e)}")
            return jsonify(error.dict()), 500
    
    logger.info("API路由注册完成")
    
    return api_bp
