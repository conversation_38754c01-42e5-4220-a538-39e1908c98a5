#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
API数据模型
API Data Schemas

定义API接口的请求和响应数据模型。
"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

class SystemStatusResponse(BaseModel):
    """系统状态响应模型"""
    status: str = Field(description="系统状态")
    fps: float = Field(description="当前FPS")
    detection_count: int = Field(description="检测到的物体数量")
    inference_time: float = Field(description="推理时间(毫秒)")
    camera_available: bool = Field(description="摄像头是否可用")
    arm_available: bool = Field(description="机械臂是否可用")

class DetectionMeasurementData(BaseModel):
    """检测测量数据模型"""
    object_id: int = Field(description="物体ID")
    class_name: str = Field(description="类别名称")
    confidence: float = Field(description="置信度")
    bbox: List[float] = Field(description="边界框 [x1, y1, x2, y2]")
    center_pixel: List[float] = Field(description="中心点像素坐标 [x, y]")
    center_world: Optional[List[float]] = Field(description="中心点世界坐标 [x, y, z]")
    dimensions_pixel: List[float] = Field(description="像素尺寸 [width, height]")
    dimensions_world: Optional[List[float]] = Field(description="世界尺寸 [width, height]")
    area_pixel: float = Field(description="像素面积")
    area_world: Optional[float] = Field(description="世界面积")
    timestamp: float = Field(description="时间戳")

class MeasurementResponse(BaseModel):
    """测量数据响应模型"""
    measurements: List[DetectionMeasurementData] = Field(description="测量数据列表")
    count: int = Field(description="测量数据数量")
    timestamp: Optional[float] = Field(description="最后更新时间戳")

class ArmActionRequest(BaseModel):
    """机械臂动作请求模型"""
    image_x: float = Field(description="图像坐标X(像素)")
    image_y: float = Field(description="图像坐标Y(像素)")
    height_z: float = Field(description="高度Z(毫米)")
    
    class Config:
        schema_extra = {
            "example": {
                "image_x": 320.0,
                "image_y": 240.0,
                "height_z": 50.0
            }
        }

class ArmActionResponse(BaseModel):
    """机械臂动作响应模型"""
    status: str = Field(description="执行状态")
    message: str = Field(description="执行消息")
    action_type: Optional[str] = Field(description="动作类型")
    coordinates: Optional[Dict[str, float]] = Field(description="执行坐标")
    execution_time: Optional[float] = Field(description="执行时间(毫秒)")

class ErrorResponse(BaseModel):
    """错误响应模型"""
    status: str = Field(default="error", description="状态")
    message: str = Field(description="错误消息")
    error_code: Optional[str] = Field(description="错误代码")
    details: Optional[Dict[str, Any]] = Field(description="错误详情")

class ToolInfo(BaseModel):
    """MCP工具信息模型"""
    name: str = Field(description="工具名称")
    description: str = Field(description="工具描述")
    parameters: List[str] = Field(description="参数列表")

class MCPToolsResponse(BaseModel):
    """MCP工具列表响应模型"""
    tools: List[ToolInfo] = Field(description="工具列表")
    count: int = Field(description="工具数量")
    server_name: str = Field(description="服务器名称")
    server_description: str = Field(description="服务器描述")

class ConfigSummaryResponse(BaseModel):
    """配置摘要响应模型"""
    app_name: str = Field(description="应用名称")
    version: str = Field(description="版本号")
    environment: str = Field(description="运行环境")
    web_service: str = Field(description="Web服务地址")
    mcp_service: str = Field(description="MCP服务地址")
    model_path: str = Field(description="模型文件路径")
    camera_device: str = Field(description="摄像头设备")
    arm_serial_port: str = Field(description="机械臂串口")
