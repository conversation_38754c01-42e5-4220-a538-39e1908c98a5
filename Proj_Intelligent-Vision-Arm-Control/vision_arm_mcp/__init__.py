#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能视觉机械臂控制系统 - MCP服务器
Intelligent Vision Arm Control System - MCP Server

这是一个集成了实时物体检测与四自由度机械臂控制功能的智能系统，
通过MCP协议对外提供服务，支持与AI助手和其他系统的集成。

主要功能：
- 基于YOLO11的实时物体检测
- 四自由度机械臂控制
- Web界面和API服务
- MCP协议支持
- 坐标系测量和可视化

作者: Vision Arm Team
版本: 2.0.0
许可: MIT License
"""

__version__ = "2.0.0"
__author__ = "Vision Arm Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 导出主要组件
from .main import main, create_unified_server
from .core.detection import YOLO11_Detect, BaseModel
from .core.arm_control import RobotArm
from .services import WebService, MCPService, VideoService
from .config import Settings, load_config

__all__ = [
    "main",
    "create_unified_server", 
    "YOLO11_Detect",
    "BaseModel",
    "RobotArm",
    "WebService",
    "MCPService", 
    "VideoService",
    "Settings",
    "load_config",
    "__version__",
    "__author__",
    "__email__",
    "__license__"
]
