#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
视频流服务
Video Stream Service

专门处理视频流的服务模块，提供：
- 多线程视频处理
- 帧缓冲管理
- 性能优化
- 资源管理
"""

import logging
import threading
import queue
import time
from typing import Optional, Tuple, Any
import cv2
import numpy as np

from ..core.detection import YOLO11_Detect
from ..core.utils import FPSCounter, draw_detections, draw_coordinate_system
from ..config.settings import Settings

logger = logging.getLogger("VideoService")

class VideoService:
    """视频流服务类"""
    
    def __init__(self, model: YOLO11_Detect, config: Settings):
        """
        初始化视频流服务
        
        Args:
            model: YOLO检测模型实例
            config: 系统配置
        """
        self.model = model
        self.config = config
        
        # 摄像头
        self.camera: Optional[cv2.VideoCapture] = None
        
        # 线程间通信队列
        self.frame_queue = queue.Queue(maxsize=2)
        self.result_queue = queue.Queue(maxsize=2)
        
        # 控制标志
        self.running = False
        self.threads = []
        
        # 性能监控
        self.fps_counter = FPSCounter()
        self.last_inference_time = 0
        
        # 初始化摄像头
        self._initialize_camera()
        
        logger.info("视频流服务初始化完成")
    
    def _initialize_camera(self):
        """初始化摄像头"""
        try:
            self.camera = cv2.VideoCapture(self.config.camera.device_path)
            if self.camera.isOpened():
                # 设置摄像头参数
                self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.camera.width)
                self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.camera.height)
                self.camera.set(cv2.CAP_PROP_FPS, self.config.camera.fps)
                
                # 设置缓冲区大小
                self.camera.set(cv2.CAP_PROP_BUFFERSIZE, 1)
                
                logger.info(f"摄像头初始化成功: {self.config.camera.device_path}")
            else:
                logger.error(f"无法打开摄像头: {self.config.camera.device_path}")
                self.camera = None
        except Exception as e:
            logger.error(f"摄像头初始化失败: {e}")
            self.camera = None
    
    def _camera_thread(self):
        """摄像头读取线程"""
        logger.info("摄像头读取线程启动")
        
        while self.running and self.camera and self.camera.isOpened():
            try:
                ret, frame = self.camera.read()
                if ret:
                    # 非阻塞放入队列
                    try:
                        self.frame_queue.put_nowait(frame)
                    except queue.Full:
                        # 队列满时丢弃旧帧
                        try:
                            self.frame_queue.get_nowait()
                            self.frame_queue.put_nowait(frame)
                        except queue.Empty:
                            pass
                else:
                    logger.warning("无法读取摄像头帧")
                    time.sleep(0.1)
            except Exception as e:
                logger.error(f"摄像头读取错误: {e}")
                time.sleep(0.1)
        
        logger.info("摄像头读取线程结束")
    
    def _inference_thread(self):
        """AI推理线程"""
        logger.info("AI推理线程启动")
        
        while self.running:
            try:
                # 获取帧（阻塞等待）
                frame = self.frame_queue.get(timeout=1.0)
                
                # 执行YOLO检测
                start_time = time.time()
                ids, scores, bboxes, inference_time = self.model.process_frame(frame)
                total_time = (time.time() - start_time) * 1000
                
                self.last_inference_time = inference_time
                
                # 准备结果数据
                result = {
                    'frame': frame,
                    'ids': ids,
                    'scores': scores, 
                    'bboxes': bboxes,
                    'inference_time': inference_time,
                    'total_time': total_time,
                    'timestamp': time.time()
                }
                
                # 非阻塞放入结果队列
                try:
                    self.result_queue.put_nowait(result)
                except queue.Full:
                    # 队列满时丢弃旧结果
                    try:
                        self.result_queue.get_nowait()
                        self.result_queue.put_nowait(result)
                    except queue.Empty:
                        pass
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"AI推理错误: {e}")
                continue
        
        logger.info("AI推理线程结束")
    
    def start(self):
        """启动视频流服务"""
        if not self.camera or not self.camera.isOpened():
            logger.error("摄像头不可用，无法启动视频流服务")
            return False
        
        self.running = True
        
        # 启动摄像头读取线程
        camera_thread = threading.Thread(target=self._camera_thread, daemon=True)
        camera_thread.start()
        self.threads.append(camera_thread)
        
        # 启动AI推理线程
        inference_thread = threading.Thread(target=self._inference_thread, daemon=True)
        inference_thread.start()
        self.threads.append(inference_thread)
        
        logger.info("视频流服务已启动")
        return True
    
    def stop(self):
        """停止视频流服务"""
        self.running = False
        
        # 等待线程结束
        for thread in self.threads:
            if thread.is_alive():
                thread.join(timeout=2.0)
        
        # 释放摄像头资源
        if self.camera:
            self.camera.release()
            logger.info("摄像头资源已释放")
        
        logger.info("视频流服务已停止")
    
    def get_latest_result(self) -> Optional[dict]:
        """获取最新的处理结果"""
        try:
            return self.result_queue.get_nowait()
        except queue.Empty:
            return None
    
    def generate_frames(self):
        """生成视频帧（用于Web流）"""
        while self.running:
            result = self.get_latest_result()
            if result is None:
                time.sleep(0.033)  # ~30fps
                continue
            
            try:
                frame = result['frame']
                ids = result['ids']
                scores = result['scores']
                bboxes = result['bboxes']
                inference_time = result['inference_time']
                
                # 绘制检测结果
                detection_config = {
                    'class_names': self.config.detection.class_names,
                    'colors': self.config.detection.colors,
                    'font_scale': self.config.detection.font_scale,
                    'line_thickness': self.config.detection.line_thickness
                }
                measurement_config = {
                    'enable_coordinate_system': self.config.measurement.enable_coordinate_system,
                    'enable_distance_measurement': self.config.measurement.enable_distance_measurement,
                    'enable_dimension_measurement': self.config.measurement.enable_dimension_measurement,
                    'axis_color': self.config.measurement.axis_color,
                    'measurement_color': self.config.measurement.measurement_color,
                    'axis_length_percentage': self.config.measurement.axis_length_percentage,
                    'axis_thickness': self.config.measurement.axis_thickness,
                    'measurement_line_thickness': self.config.measurement.measurement_line_thickness
                }
                drawn_frame = draw_detections(
                    frame, ids, scores, bboxes,
                    fps=self.fps_counter.update(),
                    inference_time=inference_time,
                    detection_config=detection_config,
                    measurement_config=measurement_config
                )
                
                # 编码为JPEG
                ret, buffer = cv2.imencode('.jpg', drawn_frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                if ret:
                    frame_bytes = buffer.tobytes()
                    yield (b'--frame\r\n'
                           b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                
            except Exception as e:
                logger.error(f"视频帧生成错误: {e}")
                continue
    
    def get_status(self) -> dict:
        """获取视频服务状态"""
        return {
            "running": self.running,
            "camera_available": self.camera is not None and self.camera.isOpened(),
            "fps": self.fps_counter.fps,
            "last_inference_time": self.last_inference_time,
            "frame_queue_size": self.frame_queue.qsize(),
            "result_queue_size": self.result_queue.qsize(),
            "threads_count": len([t for t in self.threads if t.is_alive()])
        }
