import cv2
import numpy as np
import logging
from time import time
from hobot_dnn import pyeasy_dnn as dnn  # BSP Python API

logger = logging.getLogger("ShapeDetection")

class BaseModel:
    """
    基础模型类，提供通用的模型加载和预处理功能
    """
    def __init__(self, model_file: str) -> None:
        """
        初始化模型
        
        Args:
            model_file: BPU量化模型文件路径
        """
        # 加载BPU的bin模型
        try:
            begin_time = time()
            self.quantize_model = dnn.load(model_file)
            logger.info(f"加载模型耗时: {1000*(time() - begin_time):.2f} ms")
        except Exception as e:
            logger.error(f"❌ 无法加载模型: {model_file}")
            logger.error(f"错误信息: {str(e)}")
            raise

        # 打印模型信息
        logger.info("模型输入张量:")
        for i, quantize_input in enumerate(self.quantize_model[0].inputs):
            logger.info(f"输入[{i}], 名称={quantize_input.name}, 类型={quantize_input.properties.dtype}, 形状={quantize_input.properties.shape}")

        logger.info("模型输出张量:")
        for i, quantize_output in enumerate(self.quantize_model[0].outputs):
            logger.info(f"输出[{i}], 名称={quantize_output.name}, 类型={quantize_output.properties.dtype}, 形状={quantize_output.properties.shape}")

        # 获取模型输入尺寸
        self.model_input_height, self.model_input_width = self.quantize_model[0].inputs[0].properties.shape[2:4]
        logger.info(f"模型输入尺寸: {self.model_input_width}x{self.model_input_height}")

    def resizer(self, img: np.ndarray) -> np.ndarray:
        """
        调整图像大小以匹配模型输入要求
        
        Args:
            img: 输入图像
            
        Returns:
            调整大小后的图像
        """
        img_h, img_w = img.shape[0:2]
        # 保存缩放比例，用于后处理时还原坐标
        self.y_scale, self.x_scale = img_h/self.model_input_height, img_w/self.model_input_width
        return cv2.resize(img, (self.model_input_width, self.model_input_height), interpolation=cv2.INTER_NEAREST)

    def preprocess(self, img: np.ndarray) -> np.ndarray:
        """
        对图像进行预处理，准备模型输入
        
        Args:
            img: BGR格式的输入图像
            
        Returns:
            NCHW格式的预处理图像
        """
        begin_time = time()

        # 调整大小
        input_tensor = self.resizer(img)
        # BGR转RGB
        input_tensor = cv2.cvtColor(input_tensor, cv2.COLOR_BGR2RGB)
        # 转换为NCHW格式
        input_tensor = np.transpose(input_tensor, (2, 0, 1))
        input_tensor = np.expand_dims(input_tensor, axis=0).astype(np.uint8)

        logger.debug(f"预处理耗时: {1000*(time() - begin_time):.2f} ms")
        return input_tensor

    def bgr2nv12(self, bgr_img: np.ndarray) -> np.ndarray:
        """
        将BGR图像转换为NV12格式
        
        Args:
            bgr_img: BGR格式的输入图像
            
        Returns:
            NV12格式的图像
        """
        begin_time = time()
        
        # 调整大小
        bgr_img = self.resizer(bgr_img)
        height, width = bgr_img.shape[0], bgr_img.shape[1]
        area = height * width
        
        # BGR转YUV 4:2:0平面格式，然后重新排列为NV12格式
        yuv420p = cv2.cvtColor(bgr_img, cv2.COLOR_BGR2YUV_I420).reshape((area * 3 // 2,))
        y = yuv420p[:area]
        uv_planar = yuv420p[area:].reshape((2, area // 4))
        uv_packed = uv_planar.transpose((1, 0)).reshape((area // 2,))
        
        nv12 = np.zeros_like(yuv420p)
        nv12[:height * width] = y
        nv12[height * width:] = uv_packed

        logger.debug(f"BGR转NV12耗时: {1000*(time() - begin_time):.2f} ms")
        return nv12

    def forward(self, input_tensor: np.ndarray) -> list:
        """
        执行模型推理
        
        Args:
            input_tensor: 预处理后的输入张量
            
        Returns:
            模型输出张量列表
        """
        begin_time = time()
        
        quantize_outputs = self.quantize_model[0].forward(input_tensor)
        
        logger.debug(f"模型推理耗时: {1000*(time() - begin_time):.2f} ms")
        return quantize_outputs

    def c2numpy(self, outputs) -> list:
        """
        将C类型输出转换为NumPy数组
        
        Args:
            outputs: 模型输出对象
            
        Returns:
            NumPy数组格式的输出列表
        """
        begin_time = time()
        
        outputs = [dnnTensor.buffer for dnnTensor in outputs]
        
        logger.debug(f"C转NumPy耗时: {1000*(time() - begin_time):.2f} ms")
        return outputs
