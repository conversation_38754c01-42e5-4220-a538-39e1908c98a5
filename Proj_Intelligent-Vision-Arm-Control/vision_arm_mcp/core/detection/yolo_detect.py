import cv2
import numpy as np
from scipy.special import softmax
import logging
from time import time
from .base_model import BaseModel

logger = logging.getLogger("ShapeDetection")

class YOLO11_Detect(BaseModel):
    """
    YOLO11检测模型类，实现三分类目标检测
    """
    def __init__(self, 
                model_file: str, 
                conf: float, 
                iou: float
                ):
        """
        初始化YOLO11检测模型
        
        Args:
            model_file: 模型文件路径
            conf: 置信度阈值
            iou: IoU阈值
        """
        super().__init__(model_file)
        
        # 获取反量化系数
        self.s_bboxes_scale = self.quantize_model[0].outputs[0].properties.scale_data[np.newaxis, :]
        self.m_bboxes_scale = self.quantize_model[0].outputs[1].properties.scale_data[np.newaxis, :]
        self.l_bboxes_scale = self.quantize_model[0].outputs[2].properties.scale_data[np.newaxis, :]
        logger.debug(f"{self.s_bboxes_scale.shape=}, {self.m_bboxes_scale.shape=}, {self.l_bboxes_scale.shape=}")

        # DFL期望系数
        self.weights_static = np.array([i for i in range(16)]).astype(np.float32)[np.newaxis, np.newaxis, :]
        logger.debug(f"{self.weights_static.shape = }")

        # 初始化anchors
        self.s_anchor = np.stack([np.tile(np.linspace(0.5, 39.5, 40), reps=40), 
                            np.repeat(np.arange(0.5, 40.5, 1), 40)], axis=0).transpose(1,0)
        self.m_anchor = np.stack([np.tile(np.linspace(0.5, 19.5, 20), reps=20), 
                            np.repeat(np.arange(0.5, 20.5, 1), 20)], axis=0).transpose(1,0)
        self.l_anchor = np.stack([np.tile(np.linspace(0.5, 9.5, 10), reps=10), 
                            np.repeat(np.arange(0.5, 10.5, 1), 10)], axis=0).transpose(1,0)
        logger.debug(f"{self.s_anchor.shape = }, {self.m_anchor.shape = }, {self.l_anchor.shape = }")

        # 设置参数
        self.input_image_size = 320  # 输入尺寸
        self.conf = conf            # 置信度阈值
        self.iou = iou              # IoU阈值
        self.conf_inverse = -np.log(1/conf - 1)  # Sigmoid反函数阈值
        
        # 类别名称
        self.class_names = ['orange', 'green', 'red']  # 默认类别名称
        
        logger.info(f"模型初始化完成: 输入尺寸={self.input_image_size}, IoU阈值={iou}, 置信度阈值={conf}")

    def process_frame(self, frame: np.ndarray) -> tuple:
        """
        处理单帧图像，执行检测
        
        Args:
            frame: BGR格式的输入图像
            
        Returns:
            (ids, scores, bboxes): 检测结果，包含类别ID、置信度和边界框坐标
        """
        begin_time = time()
        
        # 预处理
        input_tensor = self.bgr2nv12(frame)
        
        # 推理
        outputs = self.c2numpy(self.forward(input_tensor))
        
        # 后处理
        ids, scores, bboxes = self.postProcess(outputs)
        
        inference_time = time() - begin_time
        logger.debug(f"总推理时间: {1000*inference_time:.2f} ms, 检测到 {len(ids)} 个目标")
        
        return ids, scores, bboxes, inference_time

    def postProcess(self, outputs: list[np.ndarray]) -> tuple[list]:
        """
        对模型输出进行后处理
        
        Args:
            outputs: 模型输出张量列表
            
        Returns:
            (ids, scores, bboxes): 检测结果，包含类别ID、置信度和边界框坐标
        """
        begin_time = time()
        
        # reshape处理
        s_bboxes = outputs[0].reshape(-1, 64)
        m_bboxes = outputs[1].reshape(-1, 64)
        l_bboxes = outputs[2].reshape(-1, 64)
        s_clses = outputs[3].reshape(-1, 3)
        m_clses = outputs[4].reshape(-1, 3)
        l_clses = outputs[5].reshape(-1, 3)

        # 获取置信度最高的索引和分数
        s_max_scores = np.max(s_clses, axis=1)
        s_valid_indices = np.flatnonzero(s_max_scores >= self.conf_inverse)
        s_ids = np.argmax(s_clses[s_valid_indices, :], axis=1)
        s_scores = s_max_scores[s_valid_indices]

        m_max_scores = np.max(m_clses, axis=1)
        m_valid_indices = np.flatnonzero(m_max_scores >= self.conf_inverse)
        m_ids = np.argmax(m_clses[m_valid_indices, :], axis=1)
        m_scores = m_max_scores[m_valid_indices]

        l_max_scores = np.max(l_clses, axis=1)
        l_valid_indices = np.flatnonzero(l_max_scores >= self.conf_inverse)
        l_ids = np.argmax(l_clses[l_valid_indices, :], axis=1)
        l_scores = l_max_scores[l_valid_indices]

        # Sigmoid激活函数计算置信度
        s_scores = 1 / (1 + np.exp(-s_scores))
        m_scores = 1 / (1 + np.exp(-m_scores))
        l_scores = 1 / (1 + np.exp(-l_scores))

        # 边界框处理
        s_bboxes_float32 = s_bboxes[s_valid_indices,:]
        m_bboxes_float32 = m_bboxes[m_valid_indices,:]
        l_bboxes_float32 = l_bboxes[l_valid_indices,:]

        # 小特征图边界框解码
        s_ltrb_indices = np.sum(softmax(s_bboxes_float32.reshape(-1, 4, 16), axis=2) * self.weights_static, axis=2)
        s_anchor_indices = self.s_anchor[s_valid_indices, :]
        s_x1y1 = s_anchor_indices - s_ltrb_indices[:, 0:2]
        s_x2y2 = s_anchor_indices + s_ltrb_indices[:, 2:4]
        s_dbboxes = np.hstack([s_x1y1, s_x2y2])*8

        # 中特征图边界框解码
        m_ltrb_indices = np.sum(softmax(m_bboxes_float32.reshape(-1, 4, 16), axis=2) * self.weights_static, axis=2)
        m_anchor_indices = self.m_anchor[m_valid_indices, :]
        m_x1y1 = m_anchor_indices - m_ltrb_indices[:, 0:2]
        m_x2y2 = m_anchor_indices + m_ltrb_indices[:, 2:4]
        m_dbboxes = np.hstack([m_x1y1, m_x2y2])*16

        # 大特征图边界框解码
        l_ltrb_indices = np.sum(softmax(l_bboxes_float32.reshape(-1, 4, 16), axis=2) * self.weights_static, axis=2)
        l_anchor_indices = self.l_anchor[l_valid_indices,:]
        l_x1y1 = l_anchor_indices - l_ltrb_indices[:, 0:2]
        l_x2y2 = l_anchor_indices + l_ltrb_indices[:, 2:4]
        l_dbboxes = np.hstack([l_x1y1, l_x2y2])*32

        # 合并结果
        if len(s_dbboxes) == 0 and len(m_dbboxes) == 0 and len(l_dbboxes) == 0:
            return [], [], []
            
        dbboxes = np.concatenate((s_dbboxes, m_dbboxes, l_dbboxes), axis=0) if len(s_dbboxes) > 0 and len(m_dbboxes) > 0 and len(l_dbboxes) > 0 else \
                 np.concatenate((s_dbboxes, m_dbboxes), axis=0) if len(s_dbboxes) > 0 and len(m_dbboxes) > 0 else \
                 np.concatenate((s_dbboxes, l_dbboxes), axis=0) if len(s_dbboxes) > 0 and len(l_dbboxes) > 0 else \
                 np.concatenate((m_dbboxes, l_dbboxes), axis=0) if len(m_dbboxes) > 0 and len(l_dbboxes) > 0 else \
                 s_dbboxes if len(s_dbboxes) > 0 else m_dbboxes if len(m_dbboxes) > 0 else l_dbboxes
        
        scores = np.concatenate((s_scores, m_scores, l_scores), axis=0) if len(s_scores) > 0 and len(m_scores) > 0 and len(l_scores) > 0 else \
                np.concatenate((s_scores, m_scores), axis=0) if len(s_scores) > 0 and len(m_scores) > 0 else \
                np.concatenate((s_scores, l_scores), axis=0) if len(s_scores) > 0 and len(l_scores) > 0 else \
                np.concatenate((m_scores, l_scores), axis=0) if len(m_scores) > 0 and len(l_scores) > 0 else \
                s_scores if len(s_scores) > 0 else m_scores if len(m_scores) > 0 else l_scores
        
        ids = np.concatenate((s_ids, m_ids, l_ids), axis=0) if len(s_ids) > 0 and len(m_ids) > 0 and len(l_ids) > 0 else \
              np.concatenate((s_ids, m_ids), axis=0) if len(s_ids) > 0 and len(m_ids) > 0 else \
              np.concatenate((s_ids, l_ids), axis=0) if len(s_ids) > 0 and len(l_ids) > 0 else \
              np.concatenate((m_ids, l_ids), axis=0) if len(m_ids) > 0 and len(l_ids) > 0 else \
              s_ids if len(s_ids) > 0 else m_ids if len(m_ids) > 0 else l_ids

        # 非极大值抑制
        indices = cv2.dnn.NMSBoxes(dbboxes, scores, self.conf, self.iou)
        
        # 如果没有检测结果，返回空列表
        if len(indices) == 0:
            return [], [], []

        # 还原到原始图像尺度
        bboxes = dbboxes[indices] * np.array([self.x_scale, self.y_scale, self.x_scale, self.y_scale])
        bboxes = bboxes.astype(np.int32)
        
        # 限制边界框在图像范围内
        bboxes[:, 0] = np.maximum(0, bboxes[:, 0])
        bboxes[:, 1] = np.maximum(0, bboxes[:, 1])
        
        logger.debug(f"后处理耗时: {1000*(time() - begin_time):.2f} ms")

        return ids[indices], scores[indices], bboxes
