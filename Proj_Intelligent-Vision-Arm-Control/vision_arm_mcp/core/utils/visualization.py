import cv2
import numpy as np
import time
from collections import deque
from PIL import Image, ImageDraw, ImageFont
import os
import logging
from .measurement import (
    calculate_frame_center,
    calculate_distance_to_center,
    calculate_box_dimensions,
    format_measurement_text
)

# 获取logger
logger = logging.getLogger("ShapeDetection")

# 获取当前脚本路径
current_dir = os.path.dirname(os.path.abspath(__file__))
# 获取项目根目录
base_dir = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
# 字体文件路径 - 直接使用系统中可用的微米黑中文字体
font_path = "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
# 如果上面的字体找不到，则尝试使用其他常见字体
if not os.path.exists(font_path):
    logger.warning(f"找不到字体文件 {font_path}")
    # 尝试其他可能的字体
    possible_fonts = [
        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf"
    ]
    for possible_font in possible_fonts:
        if os.path.exists(possible_font):
            font_path = possible_font
            logger.info(f"使用替代字体: {font_path}")
            break

class FPSCounter:
    """帧率计数器，用于计算和显示FPS"""
    def __init__(self, avg_frames=30):
        self.frame_times = deque(maxlen=avg_frames)
        self.last_time = time.time()
        self.fps = 0
        
    def update(self) -> float:
        """更新帧率计算"""
        current_time = time.time()
        delta = current_time - self.last_time
        self.last_time = current_time
        self.frame_times.append(delta)
        
        if len(self.frame_times) > 0:
            self.fps = len(self.frame_times) / sum(self.frame_times)
        return self.fps
    
    def get_fps(self) -> float:
        """获取当前帧率"""
        return self.fps

def draw_coordinate_system(image: np.ndarray, measurement_config: dict = None) -> np.ndarray:
    """
    在图像上绘制坐标系统
    使用右上为第一象限，顺时针增大象限的坐标系统

    Args:
        image: 输入图像
        measurement_config: 测量配置字典

    Returns:
        带有坐标系统的图像
    """
    # 默认配置
    if measurement_config is None:
        measurement_config = {
            'enable_coordinate_system': True,
            'axis_length_percentage': 0.8,
            'axis_color': [255, 255, 255],
            'axis_thickness': 2
        }

    if not measurement_config.get('enable_coordinate_system', True):
        return image
    
    # 创建图像副本
    result_image = image.copy()
    
    # 获取图像尺寸
    height, width = result_image.shape[:2]
    
    # 计算中心点
    center_x, center_y = calculate_frame_center(width, height)
    
    # 计算坐标轴长度
    axis_length = int(min(width, height) * measurement_config.get('axis_length_percentage', 0.8) / 2)
    axis_color = measurement_config.get('axis_color', [255, 255, 255])
    axis_thickness = measurement_config.get('axis_thickness', 2)

    # 绘制X轴（水平方向，从左到右）
    cv2.line(result_image,
            (center_x - axis_length, center_y),
            (center_x + axis_length, center_y),
            axis_color,
            axis_thickness)

    # 绘制Y轴（垂直方向，从下到上 - 注意这与图像坐标系相反）
    cv2.line(result_image,
            (center_x, center_y - axis_length),
            (center_x, center_y + axis_length),
            axis_color,
            axis_thickness)
    
    # 绘制坐标轴箭头
    # X轴箭头指向右
    cv2.arrowedLine(result_image,
                   (center_x, center_y),
                   (center_x + axis_length, center_y),
                   axis_color,
                   axis_thickness)

    # Y轴箭头指向上（图像坐标系中的负方向）
    cv2.arrowedLine(result_image,
                   (center_x, center_y),
                   (center_x, center_y - axis_length),
                   axis_color,
                   axis_thickness)

    # 绘制原点标记
    cv2.circle(result_image,
              (center_x, center_y),
              3,
              axis_color,
              -1)
    
    # 绘制X轴标签
    cv2.putText(result_image,
               "X",
               (center_x + axis_length + 5, center_y + 15),
               cv2.FONT_HERSHEY_SIMPLEX,
               0.5,
               axis_color,
               1)

    # 绘制Y轴标签
    cv2.putText(result_image,
               "Y",
               (center_x + 5, center_y - axis_length - 5),
               cv2.FONT_HERSHEY_SIMPLEX,
               0.5,
               axis_color,
               1)
    
    # 绘制象限标识
    # 第一象限：右上
    cv2.putText(result_image,
               "1",
               (center_x + axis_length//2, center_y - axis_length//2),
               cv2.FONT_HERSHEY_SIMPLEX,
               0.5,
               axis_color,
               1)

    # 第二象限：左上
    cv2.putText(result_image,
               "2",
               (center_x - axis_length//2 - 10, center_y - axis_length//2),
               cv2.FONT_HERSHEY_SIMPLEX,
               0.5,
               axis_color,
               1)

    # 第三象限：左下
    cv2.putText(result_image,
               "3",
               (center_x - axis_length//2 - 10, center_y + axis_length//2 + 10),
               cv2.FONT_HERSHEY_SIMPLEX,
               0.5,
               axis_color,
               1)

    # 第四象限：右下
    cv2.putText(result_image,
               "4",
               (center_x + axis_length//2, center_y + axis_length//2 + 10),
               cv2.FONT_HERSHEY_SIMPLEX,
               0.5,
               axis_color,
               1)
    
    return result_image

def draw_detections(image: np.ndarray,
                   class_ids: list,
                   scores: list,
                   bboxes: list,
                   fps: float = None,
                   inference_time: float = None,
                   detection_config: dict = None,
                   measurement_config: dict = None) -> np.ndarray:
    """
    在图像上绘制检测结果和性能指标

    Args:
        image: 输入图像
        class_ids: 类别ID列表
        scores: 置信度列表
        bboxes: 边界框坐标列表
        fps: 帧率
        inference_time: 推理时间(秒)
        detection_config: 检测配置字典
        measurement_config: 测量配置字典

    Returns:
        标注后的图像
    """
    # 默认配置
    if detection_config is None:
        detection_config = {
            'class_names': ['橙色', '绿色', '红色'],
            'colors': [[255, 165, 0], [0, 255, 0], [255, 0, 0]],
            'font_scale': 1.0,
            'line_thickness': 2
        }

    if measurement_config is None:
        measurement_config = {
            'enable_coordinate_system': True,
            'enable_distance_measurement': True,
            'enable_dimension_measurement': True,
            'measurement_color': [255, 255, 0],
            'measurement_line_thickness': 2
        }

    # 创建图像副本
    result_image = image.copy()

    # 如果启用坐标系统，则绘制坐标系统
    if measurement_config.get('enable_coordinate_system', True):
        result_image = draw_coordinate_system(result_image, measurement_config)
    
    # 获取图像尺寸
    height, width = result_image.shape[:2]
    
    # 计算中心点
    center_x, center_y = calculate_frame_center(width, height)
    
    # 将OpenCV图像转换为PIL Image以便绘制中文
    pil_img = Image.fromarray(cv2.cvtColor(result_image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(pil_img)
    
    # 尝试加载字体，如果失败则使用默认字体
    try:
        # 检测框标签使用较小字体，增加字体大小使中文更清晰
        label_font_size = int(24 * detection_config.get('font_scale', 1.0))  # 从16增加到24
        logger.debug(f"尝试加载中文字体: {font_path}, 字体大小: {label_font_size}")
        label_font = ImageFont.truetype(font_path, label_font_size)

        # 性能指标使用较大字体
        metrics_font_size = 25
        metrics_font = ImageFont.truetype(font_path, metrics_font_size)
        logger.debug(f"字体加载成功: {font_path}")
    except Exception as e:
        logger.warning(f"加载字体失败: {e}，使用默认字体")
        label_font = ImageFont.load_default()
        metrics_font = ImageFont.load_default()
    
    # 绘制检测结果
    for class_id, score, bbox in zip(class_ids, scores, bboxes):
        x1, y1, x2, y2 = bbox
        
        # 获取颜色和类别名称
        # 从配置中获取BGR颜色，但我们使用类名称来决定正确的颜色
        class_names = detection_config.get('class_names', ['橙色', '绿色', '红色'])
        class_name = class_names[int(class_id)] if int(class_id) < len(class_names) else f"类别{class_id}"

        # 根据类别名称设置正确的RGB颜色（不再使用BGR到RGB的转换）
        if class_name == "橙色":
            color_rgb = (255, 165, 0)  # RGB格式的橙色
        elif class_name == "绿色":
            color_rgb = (0, 255, 0)    # RGB格式的绿色
        elif class_name == "红色":
            color_rgb = (255, 0, 0)    # RGB格式的红色
        else:
            # 如果是其他类别，使用BGR转RGB的方式
            colors = detection_config.get('colors', [[255, 165, 0], [0, 255, 0], [255, 0, 0]])
            color_bgr = colors[int(class_id) % len(colors)]
            color_rgb = (color_bgr[2], color_bgr[1], color_bgr[0])

        # 不要在这里绘制边界框，而是在PIL图像上绘制
        # 在PIL图像上绘制边界框
        line_thickness = detection_config.get('line_thickness', 2)
        draw.rectangle([(x1, y1), (x2, y2)], outline=color_rgb, width=line_thickness)
        
        # 计算距离和尺寸测量
        enable_distance = measurement_config.get('enable_distance_measurement', True)
        enable_dimension = measurement_config.get('enable_dimension_measurement', True)

        if enable_distance or enable_dimension:
            # 计算检测框的中心点和尺寸
            box_center_x, box_center_y, box_width, box_height = calculate_box_dimensions(x1, y1, x2, y2)

            # 计算距离原点的距离
            if enable_distance:
                distance_x, distance_y, distance, quadrant = calculate_distance_to_center(
                    box_center_x, box_center_y, center_x, center_y
                )

                # 绘制从框中心到原点的线
                measurement_color = measurement_config.get('measurement_color', [255, 255, 0])
                measurement_line_thickness = measurement_config.get('measurement_line_thickness', 2)
                draw.line(
                    [(box_center_x, box_center_y), (center_x, center_y)],
                    fill=tuple(measurement_color),
                    width=measurement_line_thickness
                )
            else:
                distance_x, distance_y, distance, quadrant = None, None, None, None

            # 格式化测量文本
            measurement_text = format_measurement_text(
                distance_x=distance_x,
                distance_y=distance_y,
                distance=distance,
                width=box_width if enable_dimension else None,
                height=box_height if enable_dimension else None,
                quadrant=quadrant
            )
            
            # 在框下方显示测量结果
            measurement_y = y2 + 5
            # 如果测量文本存在，绘制文本背景和文本
            if measurement_text:
                text_bbox = draw.textbbox((0, 0), measurement_text, font=label_font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                # 计算文本位置，确保在图像内
                text_x = max(0, min(x1, width - text_width))
                
                # 绘制文本背景
                measurement_color = measurement_config.get('measurement_color', [255, 255, 0])
                draw.rectangle(
                    [(text_x - 4, measurement_y - 3),
                     (text_x + text_width + 4, measurement_y + text_height + 3)],
                    fill=tuple(measurement_color)
                )
                
                # 绘制文本
                draw.text(
                    (text_x, measurement_y), 
                    measurement_text, 
                    font=label_font, 
                    fill=(0, 0, 0)  # 黑色文字，与黄色背景对比
                )
        
        # 绘制标签
        label = f"{class_name}: {score:.2f}"
        
        # 计算标签文本大小
        text_size = draw.textbbox((0, 0), label, font=label_font)
        text_width = text_size[2] - text_size[0]
        text_height = text_size[3] - text_size[1]
        
        # 增加一些边距使文本看起来更好看
        padding_x = 4
        padding_y = 3
        
        # 确定标签位置
        label_x = x1
        label_y = y1 - text_height - 10 if y1 - text_height - 10 > 0 else y1 + 10
        
        # 绘制标签背景（带边距）
        draw.rectangle(
            [(label_x - padding_x, label_y - padding_y), 
             (label_x + text_width + padding_x, label_y + text_height + padding_y)],
            fill=color_rgb
        )
        
        # 绘制标签文字 - 使用textbbox确保文字位置正确对齐
        text_bbox = draw.textbbox((0, 0), label, font=label_font)
        text_offset_x = label_x
        text_offset_y = label_y
        # 在深色背景上使用白色文字，增加可读性
        draw.text((text_offset_x, text_offset_y), label, font=label_font, fill=(255, 255, 255))
    
    # 将PIL图像转换回OpenCV格式
    result_image = cv2.cvtColor(np.array(pil_img), cv2.COLOR_RGB2BGR)
    
    return result_image 