#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测量模块，提供坐标系统和物体测量功能
"""

import numpy as np
import logging
import time
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple, Any

logger = logging.getLogger("ShapeDetection")

def calculate_frame_center(frame_width: int, frame_height: int) -> tuple:
    """
    计算画面中心点坐标
    
    Args:
        frame_width: 画面宽度
        frame_height: 画面高度
        
    Returns:
        (center_x, center_y): 中心点坐标
    """
    center_x = frame_width // 2
    center_y = frame_height // 2
    return (center_x, center_y)

def get_quadrant(x: int, y: int, center_x: int, center_y: int) -> int:
    """
    确定点(x,y)相对于中心点(center_x,center_y)所在的象限
    坐标系为：右上为第一象限，顺时针旋转
    
    Args:
        x: 点的X坐标
        y: 点的Y坐标
        center_x: 中心点X坐标
        center_y: 中心点Y坐标
        
    Returns:
        象限编号(1-4)
    """
    dx = x - center_x
    # 在图像坐标系中，y轴向下为正，所以需要取反使向上为正
    dy = center_y - y
    
    if dx >= 0 and dy >= 0:
        return 1  # 右上，第一象限
    elif dx < 0 and dy >= 0:
        return 2  # 左上，第二象限
    elif dx < 0 and dy < 0:
        return 3  # 左下，第三象限
    else:  # dx >= 0 and dy < 0
        return 4  # 右下，第四象限

def calculate_distance_to_center(box_center_x: int, box_center_y: int, frame_center_x: int, frame_center_y: int) -> tuple:
    """
    计算物体中心点相对于画面中心点的距离
    使用右上为第一象限的坐标系
    
    Args:
        box_center_x: 物体中心点X坐标
        box_center_y: 物体中心点Y坐标
        frame_center_x: 画面中心点X坐标
        frame_center_y: 画面中心点Y坐标
        
    Returns:
        (dx, dy, distance, quadrant): X方向距离, Y方向距离(向上为正), 欧氏距离, 象限
    """
    dx = box_center_x - frame_center_x
    # 在图像坐标系中，y轴向下为正，所以需要取反使向上为正
    dy = frame_center_y - box_center_y
    distance = np.sqrt(dx**2 + dy**2)
    quadrant = get_quadrant(box_center_x, box_center_y, frame_center_x, frame_center_y)
    return (dx, dy, distance, quadrant)

def calculate_box_dimensions(x1: int, y1: int, x2: int, y2: int) -> tuple:
    """
    计算检测框的尺寸和中心点
    
    Args:
        x1, y1: 左上角坐标
        x2, y2: 右下角坐标
        
    Returns:
        (center_x, center_y, width, height): 中心点X坐标, 中心点Y坐标, 宽度, 高度
    """
    width = abs(x2 - x1)
    height = abs(y2 - y1)
    center_x = (x1 + x2) // 2
    center_y = (y1 + y2) // 2
    return (center_x, center_y, width, height)

def format_measurement_text(distance_x=None, distance_y=None, distance=None, width=None, height=None, quadrant=None) -> str:
    """
    格式化测量结果为可显示文本
    
    Args:
        distance_x: X方向距离
        distance_y: Y方向距离(向上为正)
        distance: 欧氏距离
        width: 物体宽度
        height: 物体高度
        quadrant: 象限(1-4)
        
    Returns:
        格式化的文本字符串
    """
    result_parts = []
    
    # 添加距离信息
    if distance_x is not None and distance_y is not None and distance is not None:
        # 添加象限信息
        quadrant_info = f"象限:{quadrant}" if quadrant is not None else ""
        result_parts.append(f"距离: ({distance_x}, {distance_y}) {quadrant_info} {distance:.1f}px")
    
    # 添加尺寸信息
    if width is not None and height is not None:
        result_parts.append(f"尺寸: {width}×{height}px")
    
    # 组合结果
    return " | ".join(result_parts) if result_parts else ""

@dataclass
class DetectionMeasurement:
    """单个检测物体的测量数据"""
    id: int                  # 检测ID (每帧重新计数)
    timestamp: float         # 时间戳
    class_id: int            # 类别ID
    class_name: str          # 类别名称
    confidence: float        # 置信度
    bbox: List[int]          # 边界框坐标 [x1, y1, x2, y2]
    center_x: int            # 物体中心X坐标
    center_y: int            # 物体中心Y坐标
    width: int               # 物体宽度
    height: int              # 物体高度
    distance_x: int          # X方向距离(相对于画面中心)
    distance_y: int          # Y方向距离(相对于画面中心，向上为正)
    distance: float          # 欧氏距离
    quadrant: int            # 所在象限(1-4)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        try:
            return {
                "id": int(self.id),
                "timestamp": float(self.timestamp),
                "class_id": int(self.class_id),
                "class_name": str(self.class_name),
                "confidence": float(self.confidence),
                "bbox": [int(x) for x in self.bbox],
                "position": {
                    "center_x": int(self.center_x),
                    "center_y": int(self.center_y),
                    "quadrant": int(self.quadrant),
                    "distance_x": int(self.distance_x),
                    "distance_y": int(self.distance_y),
                    "distance": float(self.distance)
                },
                "dimensions": {
                    "width": int(self.width),
                    "height": int(self.height)
                }
            }
        except Exception as e:
            logger.error(f"转换测量数据为字典时出错: {str(e)}")
            # 返回基本数据以避免完全失败
            return {
                "id": self.id,
                "class_name": str(self.class_name) if hasattr(self, 'class_name') else "未知",
                "error": f"数据转换失败: {str(e)}"
            }

class MeasurementCollection:
    """管理当前帧的所有测量数据"""
    
    def __init__(self):
        self.measurements: List[DetectionMeasurement] = []
        self.last_update = 0
    
    def update(self, 
              class_ids: List[int], 
              class_names: List[str],
              scores: List[float], 
              bboxes: List[List[int]],
              frame_width: int,
              frame_height: int) -> None:
        """
        更新当前帧的测量数据
        
        Args:
            class_ids: 类别ID列表
            class_names: 类别名称列表
            scores: 置信度列表
            bboxes: 边界框坐标列表 [[x1, y1, x2, y2], ...]
            frame_width: 帧宽度
            frame_height: 帧高度
        """
        # 清除之前的测量数据
        self.measurements.clear()
        
        # 计算帧中心点
        center_x, center_y = calculate_frame_center(frame_width, frame_height)
        
        # 当前时间戳
        current_time = time.time()
        self.last_update = current_time
        
        # 为每个检测到的物体创建测量数据
        for i, (class_id, class_name, score, bbox) in enumerate(zip(class_ids, class_names, scores, bboxes)):
            # 计算物体尺寸和中心点
            x1, y1, x2, y2 = bbox
            obj_center_x, obj_center_y, width, height = calculate_box_dimensions(x1, y1, x2, y2)
            
            # 计算距离画面中心的距离
            dx, dy, distance, quadrant = calculate_distance_to_center(
                obj_center_x, obj_center_y, center_x, center_y
            )
            
            # 创建测量数据
            measurement = DetectionMeasurement(
                id=i,
                timestamp=current_time,
                class_id=class_id,
                class_name=class_name,
                confidence=score,
                bbox=bbox,
                center_x=obj_center_x,
                center_y=obj_center_y,
                width=width,
                height=height,
                distance_x=dx,
                distance_y=dy,
                distance=distance,
                quadrant=quadrant
            )
            
            self.measurements.append(measurement)
    
    def get_all(self) -> List[Dict[str, Any]]:
        """获取所有测量数据的字典列表"""
        return [m.to_dict() for m in self.measurements]
    
    def get_by_id(self, id: int) -> Optional[Dict[str, Any]]:
        """根据ID获取特定测量数据"""
        for m in self.measurements:
            if m.id == id:
                return m.to_dict()
        return None
    
    def count(self) -> int:
        """获取当前检测到的物体数量"""
        return len(self.measurements) 