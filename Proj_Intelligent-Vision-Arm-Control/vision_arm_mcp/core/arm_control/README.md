# 机械臂控制模块 (`arm_control`)

本模块负责控制四自由度机械臂的全部相关逻辑，包括与下位机的通信、舵机控制协议的实现、逆运动学计算以及高层级的动作序列（如抓取和放置）。

## 文件结构与说明

*   **`__init__.py`**:
    *   包初始化文件。
    *   主要作用是导出 `RobotArm` 类，使得可以直接通过 `from shape_detection_stream.src.arm_control import RobotArm` 来使用。

*   **`arm_move.py`**:
    *   定义了 `RobotArm` 类，这是与机械臂交互的主要高级接口。
    *   **主要功能**：
        *   初始化与下位机的串口通信 (`SerialPort`) 和舵机控制协议 (`ServoProtocol`)。
        *   初始化机械臂的逆运动学模型 (`FourDofArm`)。
        *   `calculate_arm_pos(self, x, y)`: 将输入的图像坐标（预期为相对于图像中心的坐标）通过标定公式转换为机械臂自身坐标系下的目标X, Y坐标。
        *   `arm_move_to_position(...)`: 实现机械臂末端从起始点到目标点的直线插补运动，并在每个插值点调用逆运动学计算舵机角度，然后发送控制指令。
        *   `arm_catch_sth(...)`: 执行完整的抓取动作序列，包括移动到物体上方、下降、闭合手爪、抬升等步骤。
        *   `arm_put_sth(...)`: 执行完整的放置动作序列。
        *   `catch_sth()` / `release_sth()`: 控制手爪张开和闭合的底层指令。

*   **`user_arm.py`**:
    *   定义了 `FourDofArm` 类，封装了四自由度机械臂的运动学模型。
    *   **主要功能**：
        *   `__init__()`: 初始化机械臂的连杆长度 (`L2`, `L3`)。
        *   `get_coordinate(self, x1, y1, z1)`: 核心的逆运动学计算函数。根据给定的末端目标三维坐标 (`x1, y1, z1`，在机械臂基座坐标系下)，计算出四个舵机（基座旋转、大臂、小臂、手腕）分别需要旋转到的角度。此方法内包含了对 `math.acos` 参数的裁剪和详细日志记录，以增强鲁棒性。
        *   `linear_interpolation(...)`: （在当前 `RobotArm` 类中未直接使用，但提供了直线插值能力）根据起止点和插值点数，逐步调用 `get_coordinate`。
        *   `arc_interpolation(...)`: （未使用）提供圆弧插值能力。
        *   `calculate_circle_center(...)`: （未使用）计算圆弧插值的圆心。

*   **`serial_section.py`**:
    *   定义了 `SerialPort` 类，用于管理与硬件（如 Arduino 或其他微控制器）的串口通信。
    *   **主要功能**：
        *   `open()` / `close()`: 打开和关闭串口。
        *   `serial_send(self, data)`: 向串口发送字节数据。
        *   `serial_recv(self, expected_bytes=None)`: 从串口接收数据。

*   **`servo_section.py`**:
    *   定义了 `ServoProtocol` 类，实现了与下位机通信的特定舵机控制协议。
    *   **主要功能**：
        *   定义了通信数据帧的帧头、帧尾、最大舵机数量和支持的舵机引脚。
        *   `calculate_checksum(data)`: 计算数据帧的校验和。
        *   `angle_to_bytes(angle)`: 将浮点角度值转换为协议要求的字节表示（例如，放大10倍后转为两个字节）。
        *   `servo_control(self, servo_num, servo_id, servo_angle)`: 根据输入的舵机数量、ID列表和角度列表，生成符合协议的完整字节串数据帧。
        *   `_generate_protocol(self, servo_data)`: 内部方法，用于组装最终的协议数据。

## 使用流程简述

通常，外部模块（如 `web/app.py` 中的API处理函数）会：
1.  创建一个 `RobotArm` 实例 (在 `arm_move.py` 中定义)。
2.  当需要执行动作时（如抓取），调用 `RobotArm` 实例的方法，例如 `arm_catch_sth(image_relative_x, image_relative_y, height_z)`。
3.  `RobotArm` 内部会：
    a.  使用 `calculate_arm_pos` 将图像相对坐标转换为机械臂坐标。
    b.  调用 `arm_move_to_position` 来规划并执行到达目标位置（或中间点）的路径。
    c.  `arm_move_to_position` 在其插值循环中，会为每个路径点调用 `FourDofArm` 实例的 `get_coordinate` 方法来获取四个舵机的目标角度。
    d.  获取到角度后，使用 `ServoProtocol` 实例的 `servo_control` 方法将这些角度打包成符合协议的字节数据。
    e.  最后，使用 `SerialPort` 实例的 `serial_send` 方法将该字节数据通过串口发送给下位机，从而驱动舵机转动。