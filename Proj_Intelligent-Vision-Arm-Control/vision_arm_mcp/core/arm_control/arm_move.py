#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
功能: 控制机械臂运动api接口函数
Api接口: 
        1. catch_sth()  机械臂手爪打开
        2. release_sth() 机械臂手爪闭合
        3. arm_catch_sth(); 机械臂执行抓取动作
        4. arm_normal_put_sth(); 机械臂执行放置物体的动作（不是堆叠放置的方式）
        5. arm_stack_put_sth(); 机械臂放置堆叠物体时的动作组（堆叠放置的方式）
        6. arm_init_action(); 机械臂初始化动作
"""

import sys
import time
import math
from . import user_arm
from . import servo_section
from . import serial_section
# import user_arm
# import servo_section
# import serial_section

class RobotArm:
    def __init__(self):
        #创建并打开和下位机通信串口对象
        self.serial = serial_section.SerialPort(port="/dev/ttyS1", baudrate=115200)
        self.serial.open()
        time.sleep(1)
        #创建舵机协议对象
        self.servos = servo_section.ServoProtocol()
        time.sleep(1)
        #创建机械臂对象
        self.arm = user_arm.FourDofArm()

        #定义四自由度机械臂参数
        self.angle = [0.0, 0.0, 0.0, 0.0]
        self.start_ang = [60.0, 25.0, 140.0]  #机械臂逆运动学初始化参数
        self.save_ang = [0.0, 0.0, 0.0]  #实时保存四自由度机械臂逆运动学角度
        for i in range(3):
            self.save_ang[i] = self.start_ang[i]

        #机械臂执行初始动作
        self.release_sth() #打开爪子
        time.sleep(0.6)
        #机械臂执行初始化动作
        self.arm_init_action()
        time.sleep(0.6)


    """
    通过线性方程将物体坐标转换为机械臂抓取的逆运动学坐标
    """
    def calculate_arm_pos(self, x, y):
        position_x = 0.000004*(x*x) + 0.9879*x - 2.9465
        position_y = 0.0005*y*y + 0.9638*y + 228.29
        position_x = round(position_x, 2)
        position_y = round(position_y, 2)
        return position_x, position_y

    """
    机械臂手爪打开
    """
    def catch_sth(self):
        data = self.servos.servo_control(
            servo_num=1,
            servo_id=[6],
            servo_angle=[110]
        )
        self.serial.serial_send(data)
        time.sleep(0.05)

    """
    机械臂手爪闭合
    """
    def release_sth(self):
        data = self.servos.servo_control(
            servo_num=1,
            servo_id=[6],
            servo_angle=[1.01]
        )
        self.serial.serial_send(data)
        time.sleep(0.05)

    """
    机械臂运动函数
    """
    def arm_move_to_position(self, start_x, start_y, start_z, end_x, end_y, end_z):
        save_x = 0
        save_y = 0
        save_z = 0
        delta_x = start_x - end_x
        delta_y = start_y - end_y
        #防止数据出错
        try:
            num_points = int((math.fabs(delta_x) + math.fabs(delta_y)) / 9.0)
        except:
            num_points = 1
        if num_points <= 8:
            num_points = 8
        if num_points >= 25:
            num_points = 25
        for i in range(num_points + 1):
            t = i / num_points
            x = start_x + t * (end_x - start_x)
            y = start_y + t * (end_y - start_y)
            z = start_z + t * (end_z - start_z)
            save_x = x
            save_y = y
            save_z = z

            self.angle[0],self.angle[1],self.angle[2],self.angle[3] =  self.arm.get_coordinate(x, y, z)
            data = self.servos.servo_control(
                servo_num=4,
                servo_id=[44, 45, 46, 7],
                servo_angle=[self.angle[3], self.angle[2], self.angle[1], self.angle[0]]
            )
            self.serial.serial_send(data)
            time.sleep(0.03)
        self.save_ang = [save_x, save_y, save_z]


    """
    机械臂初始化动作
    机械臂从上一次的角度转动到初始化动作
    """
    def arm_init_action(self):
        self.arm_move_to_position(self.save_ang[0], self.save_ang[1], self.save_ang[2], self.start_ang[0], self.start_ang[1], self.start_ang[2])
        time.sleep(0.6)


    """
    机械臂执行抓取动作
    sth_position_x: 传入视觉检测到的待测物体的 x 坐标
    sth_position_y: 传入视觉检测到的待测物体的 y 坐标
    sth_position_z: 传入需要抓取物体的高度距离。
                    如果抓取的物体没有堆叠的情况, 那么该参数默认为50.0
                    如果抓取的物体出现堆叠的情况(比如说两个物体重叠放置到一起), 那么该参数需要测试（待测高度）
    """
    def arm_catch_sth(self, sth_position_x, sth_position_y, sth_position_z):
        y, x = self.calculate_arm_pos(sth_position_x, sth_position_y)
        # 机械臂转动到抓取位置
        self.arm_move_to_position(self.start_ang[0], self.start_ang[1], self.start_ang[2], x, y, 100.0)
        time.sleep(0.6)
        # 机械臂准备抓取物体
        self.arm_move_to_position(x, y, 100.0, x, y, sth_position_z)
        time.sleep(0.6)
        # 机械臂手爪闭合，抓取物体
        self.catch_sth()
        time.sleep(0.6)
        # 机械臂抬升，准备离开抓取区域
        self.arm_move_to_position(x, y, sth_position_z, x, y, 100.0)
        time.sleep(0.6)
        # 机械臂转动到初始位置
        self.arm_move_to_position(x, y, 100.0, self.start_ang[0], self.start_ang[1], self.start_ang[2])
        time.sleep(0.6)

    
    """
    机械臂执行放置物体的动作（不是堆叠放置的方式）
    sth_position_x: 传入物体需要放置到图像中的 x 坐标
    sth_position_y: 传入物体需要放置到图像中的 y 坐标
    sth_position_z: 传入物体需要放置到图像中的 z 高度
    哈哈哈哈哈, 通过多次测试, 针对目前的物体, 放置高度设置为60就好了
    注意, 注意, 注意, 通过多次测试, 针对目前的物体, 放置高度设置为60就好了
    """
    def arm_normal_put_sth(self, sth_position_x, sth_position_y, sth_position_z):
        y, x = self.calculate_arm_pos(sth_position_x, sth_position_y)
        # 机械臂转动到放置位置
        self.arm_move_to_position(self.start_ang[0], self.start_ang[1], self.start_ang[2], x, y, 100.0)
        time.sleep(0.6)

        # 机械臂下降，准备释放物体
        self.arm_move_to_position(x, y, 100.0, x, y, sth_position_z)
        time.sleep(0.6)

        # 机械臂手爪闭合，抓取物体
        self.release_sth()
        time.sleep(0.6)

        # 机械臂抬升，准备离开放置区域
        self.arm_move_to_position(x, y, sth_position_z, x, y, 120.0)
        time.sleep(0.6)

        # 机械臂转动到初始位置
        self.arm_move_to_position(x, y, 120, self.start_ang[0], self.start_ang[1], self.start_ang[2])
        time.sleep(0.6)
        
        
    
    """
    机械臂放置堆叠物体时的动作组（堆叠放置的方式）
    sth_position_x: 传入物体需要放置到图像中的 x 坐标
    sth_position_y: 传入物体需要放置到图像中的 y 坐标
    sth_position_z: 传入物体需要放置到图像中的 z 高度
    哈哈哈哈哈, 通过多次测试, 针对目前的物体, 放置高度设置为140就好了。
    注意, 注意, 注意, 通过多次测试, 针对目前的物体, 放置高度设置为140就好了
    """
    def arm_stack_put_sth(self, sth_position_x, sth_position_y, sth_position_z):
        y, x = self.calculate_arm_pos(sth_position_x, sth_position_y)
        # 机械臂转动到放置位置
        self.arm_move_to_position(self.start_ang[0], self.start_ang[1], self.start_ang[2], x, y, sth_position_z)
        time.sleep(1.2)
        
        #机械臂稍微下降一点，方便准备释放物体
        self.arm_move_to_position(x, y, sth_position_z, x, y, sth_position_z-1)
        time.sleep(1.2)

        # 机械臂手爪闭合，抓取物体
        self.release_sth()
        time.sleep(0.6)

        # 机械臂抬升，准备离开放置区域
        self.arm_move_to_position(x, y, sth_position_z-1, x, y, sth_position_z+6.0)
        time.sleep(0.6)

        # 机械臂转动到初始位置
        self.arm_move_to_position(x, y, sth_position_z+6.0, self.start_ang[0], self.start_ang[1], self.start_ang[2])
        time.sleep(0.6)


def main():
    arm = RobotArm()
    #机械臂执行初始化动作-动作测试成功
    arm.arm_init_action()

    #机械臂执行抓取动作测试-动作测试成功
    arm.arm_catch_sth(-48, -70, 60)
    time.sleep(2)

    #机械臂执行放置动作测试-动作测试成功
    arm.arm_normal_put_sth(62, -77, 60)
    time.sleep(2)

    #机械臂执行抓取动作测试-动作测试成功
    arm.arm_catch_sth(-48, -70, 50)
    time.sleep(2)

    #机械臂执行堆叠放置动作测试-动作测试成功
    arm.arm_stack_put_sth(52, -77, 140)
    time.sleep(2)

if __name__ == '__main__':
    main()