import time
import math
import cmath
import logging

logger = logging.getLogger("ShapeDetection.ArmControl")

class FourDofArm:
    def __init__(self):
        self.L2 = 109
        self.L3 = 126

    def angle_to_duty_cycle(self, angle):
        return (angle/270) * 20 + 2.5

    def grap(self, grap_value):
        duty_cycle = self.angle_to_duty_cycle(grap_value)
        duty_cycle = round(duty_cycle, 2)
        print("raw:", duty_cycle)

    # 运动学计算函数
    def get_coordinate(self, x1, y1, z1):
        logger.debug(f"get_coordinate called with x1={x1}, y1={y1}, z1={z1}")
        # Arm lengths, should be from self.L2, self.L3 for consistency if they can change
        # However, original code hardcoded them here after init from self.L2, self.L3
        # Using fixed values as per original logic inside this function for now.
        # Consider refactoring to use self.L2, self.L3 directly if they are meant to be configurable post-init.
        l2 = 109 # Corresponds to self.L2
        l3 = 126 # Corresponds to self.L3

        x = float(x1) # Ensure float type
        y = float(y1)
        z = float(z1)

        alpha_angle = 0.0
        theta_angle = 0.0
        beta_angle = 0.0
        gamma_angle = 0.0

        # Check for x=0 to prevent math errors in atan(y/x) for alpha_angle calculation
        if x == 0:
            if y > 0: alpha_angle = 90.0
            elif y < 0: alpha_angle = -90.0
            else: alpha_angle = 0.0 # Origin, alpha can be undefined or 0
            logger.debug(f"x is zero, alpha_angle calculated as: {alpha_angle}")
        else:
            alpha_angle = 90 - math.degrees(math.atan(y / x))
            logger.debug(f"Calculated alpha_angle (degrees): {alpha_angle}")

        # Pre-calculate sqrt(x*x + y*y) as it's used multiple times
        sqrt_x_sq_y_sq = math.sqrt(x * x + y * y)

        if sqrt_x_sq_y_sq == 0:
            # This case implies x=0 and y=0.
            # If z is also 0, it's the base of the arm. Inverse kinematics might be ill-defined or have multiple solutions.
            # If z is not 0, it's directly above/below the base.
            # theta2 calculation would involve atan(z/0) ->
            # For now, if x and y are 0, alpha is set, theta2 would be +/-90 or 0 if z is also 0.
            # This specific configuration might need special handling based on arm design.
            if z == 0: # at (0,0,0)
                 logger.warning("Input (x,y,z) is (0,0,0). Angles are ill-defined. Returning (0,0,0,0).")
                 return (0.0, 0.0, 0.0, 0.0)
            theta2_rad = math.pi / 2 if z > 0 else (-math.pi / 2 if z < 0 else 0)
            theta2_val = math.degrees(theta2_rad)
            logger.debug(f"sqrt_x_sq_y_sq is zero. theta2_val calculated as: {theta2_val}")
        else:
            theta2_val = math.degrees(math.atan(z / sqrt_x_sq_y_sq))
            logger.debug(f"Calculated theta2_val (degrees): {theta2_val}")

        c_dist = math.sqrt(x * x + y * y + z * z) # Distance from origin to target
        logger.debug(f"Calculated c_dist (distance to target): {c_dist}")

        if c_dist == 0: # Target is at the origin (base of the arm)
            logger.warning("Target coordinate is at the origin (0,0,0). Inverse kinematics may be ambiguous. Returning default angles or error.")
            # Depending on desired "home" or "zero" pose, return appropriate angles.
            # For now, returning 0s, but this might need specific arm "home" pose angles.
            return (alpha_angle, 0.0, 0.0, 0.0) # alpha might be somewhat defined by approach if x,y were not 0 before becoming 0

        # Denominators for acos arguments
        den_theta1 = 2 * l2 * c_dist
        den_beta1 = 2 * l2 * l3

        if den_theta1 == 0:
            logger.error(f"Division by zero in arg_theta1 calculation (2*l2*c_dist is zero where l2={l2}, c_dist={c_dist}). Target likely at origin or l2 is zero. Returning error angles.")
            return (alpha_angle, 0.0, 180.0, 0.0) # Example error/default pose
        
        # This should not happen if l2 and l3 are non-zero constants.
        if den_beta1 == 0:
             logger.error(f"Division by zero in arg_beta1 calculation (2*l2*l3 is zero where l2={l2}, l3={l3}). Arm lengths invalid. Returning error angles.")
             return (alpha_angle, 0.0, 180.0, 0.0)


        # Calculate theta1 (angle for L2 relative to the vector to c_dist in the vertical plane)
        original_arg_theta1 = (l2*l2 + c_dist*c_dist - l3*l3) / den_theta1
        arg_theta1_to_use = original_arg_theta1
        logger.debug(f"Original arg_theta1: {original_arg_theta1}")
        if original_arg_theta1 > 1.0:
            arg_theta1_to_use = 1.0
            logger.warning(f"Clipped arg_theta1 to 1.0 (was {original_arg_theta1}) for c_dist={c_dist}, l2={l2}, l3={l3}")
        elif original_arg_theta1 < -1.0:
            arg_theta1_to_use = -1.0
            logger.warning(f"Clipped arg_theta1 to -1.0 (was {original_arg_theta1}) for c_dist={c_dist}, l2={l2}, l3={l3}")
        
        theta1_val_rad = math.acos(arg_theta1_to_use)
        theta1_val = math.degrees(theta1_val_rad)
        logger.debug(f"Calculated theta1_val (degrees): {theta1_val}")
        
        # Calculate beta1 (internal angle at the "elbow" joint between L2 and L3)
        original_arg_beta1 = (l2*l2 + l3*l3 - c_dist*c_dist) / den_beta1
        arg_beta1_to_use = original_arg_beta1
        logger.debug(f"Original arg_beta1: {original_arg_beta1}")
        if original_arg_beta1 > 1.0:
            arg_beta1_to_use = 1.0
            logger.warning(f"Clipped arg_beta1 to 1.0 (was {original_arg_beta1}) for c_dist={c_dist}, l2={l2}, l3={l3}")
        elif original_arg_beta1 < -1.0:
            arg_beta1_to_use = -1.0
            logger.warning(f"Clipped arg_beta1 to -1.0 (was {original_arg_beta1}) for c_dist={c_dist}, l2={l2}, l3={l3}")

        beta1_val_rad = math.acos(arg_beta1_to_use)
        beta1_val = math.degrees(beta1_val_rad) # This is typically 0-180 for the internal angle
        logger.debug(f"Calculated beta1_val (degrees, internal elbow angle): {beta1_val}")

        # Servo angles based on kinematic solution (these formulas seem specific to this arm's setup)
        # theta_angle: Base servo (controls overall elevation of L2)
        # beta_angle: Elbow servo (controls L3 relative to L2)
        # gamma_angle: Wrist servo (maintains orientation, often related to sum of other angles)
        
        # Original formulas:
        # theta = 100.9 - (90 - (theta1 + theta2))
        # beta = 95 - (90 - beta1)
        # gamma = 137 - (180 - (beta1 + (theta1 + theta2)))
        # These map the kinematic angles (theta1_val, theta2_val, beta1_val) to servo angles with offsets.

        theta_angle = 100.9 - (90.0 - (theta1_val + theta2_val))
        logger.debug(f"Calculated theta_angle (servo base elevation): {theta_angle}")
        
        beta_angle = 95.0 - (90.0 - beta1_val) # Note: beta1_val is 0-180. If beta1_val is large (e.g. >90), (90-beta1_val) is negative.
        logger.debug(f"Calculated beta_angle (servo elbow): {beta_angle}")
        
        gamma_angle = 137.0 - (180.0 - (beta1_val + (theta1_val + theta2_val)))
        logger.debug(f"Calculated gamma_angle (servo wrist): {gamma_angle}")

        # Round final servo angles
        alpha_final = round(alpha_angle, 2)
        theta_final = round(theta_angle, 2)
        beta_final  = round(beta_angle, 2)
        gamma_final = round(gamma_angle, 2)
        
        logger.debug(f"Final rounded servo angles (alpha, theta, beta, gamma): {alpha_final}, {theta_final}, {beta_final}, {gamma_final}")
        return (alpha_final, theta_final, beta_final, gamma_final)

    # 直线插补函数
    def linear_interpolation(self, start_x, start_y, start_z, end_x, end_y, end_z, num_points):
        for i in range(num_points + 1):
            t = i / num_points
            x = start_x + t * (end_x - start_x)
            y = start_y + t * (end_y - start_y)
            z = start_z + t * (end_z - start_z)
            self.get_coordinate(x, y, z)
            time.sleep(0.005)

    # 圆弧插补函数
    def arc_interpolation(self, start_x, start_y, end_x, end_y, radius, num_points, grap_value):
        center_x, center_y = self.calculate_circle_center(start_x, start_y, end_x, end_y, radius)
        if center_x is None:
            return

        start_angle = math.atan2(start_y - center_y, start_x - center_x)
        end_angle = math.atan2(end_y - center_y, end_x - center_x)
        delta_angle = (end_angle - start_angle) / num_points

        for i in range(num_points + 1):
            current_angle = start_angle + i * delta_angle
            x = center_x + radius * math.cos(current_angle)
            y = center_y + radius * math.sin(current_angle)
            z = 10.0
            self.get_coordinate(x, y, z)
            time.sleep(0.005)

    # 计算圆心函数
    def calculate_circle_center(self, x1, y1, x2, y2, radius):
        dx = x2 - x1
        dy = y2 - y1
        distance = math.sqrt(dx * dx + dy * dy)

        if distance > 2 * radius:
            return None, None

        mid_x = (x1 + x2) / 2
        mid_y = (y1 + y2) / 2
        chord_length = math.sqrt(radius * radius - (distance / 2) * (distance / 2))

        center_x = mid_x + chord_length * (-dy / distance)
        center_y = mid_y + chord_length * (dx / distance)

        return center_x, center_y

# def main():
#     arm = FourDofArm()
#     arm.grap(11)
#     arm.get_coordinate(50, 5, 140)
#     # arm.linear_interpolation(50, 5, 140, 180, 10, 90, 60)

# if __name__ == '__main__':
#     main()