#!/bin/bash

# 智能视觉机械臂控制系统 - 统一启动脚本
# Intelligent Vision Arm Control System - Unified Start Script

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 切换到项目根目录
cd "$PROJECT_ROOT"

# 打印横幅
print_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  智能视觉机械臂控制系统 - MCP服务器"
    echo "  Intelligent Vision Arm Control System"
    echo "  Version: 2.0.0"
    echo "=================================================="
    echo -e "${NC}"
}

# 打印使用说明
print_usage() {
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -c, --config FILE      指定配置文件路径 [默认: config/default.yaml]"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                     # 使用默认配置启动"
    echo "  $0 -c custom.yaml      # 使用自定义配置文件启动"
    echo ""
}

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查系统依赖...${NC}"
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}错误: 未找到 python3${NC}"
        exit 1
    fi
    
    # 检查pip
    if ! command -v pip3 &> /dev/null; then
        echo -e "${RED}错误: 未找到 pip3${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 系统依赖检查通过${NC}"
}

# 检查Python依赖
check_python_dependencies() {
    echo -e "${YELLOW}检查Python依赖...${NC}"
    
    # 检查requirements.txt是否存在
    if [ ! -f "requirements.txt" ]; then
        echo -e "${RED}错误: 未找到 requirements.txt${NC}"
        exit 1
    fi
    
    # 检查关键依赖
    local missing_deps=()
    
    if ! python3 -c "import cv2" &> /dev/null; then
        missing_deps+=("opencv-python")
    fi
    
    if ! python3 -c "import numpy" &> /dev/null; then
        missing_deps+=("numpy")
    fi
    
    if ! python3 -c "import flask" &> /dev/null; then
        missing_deps+=("flask")
    fi
    
    if ! python3 -c "import fastmcp" &> /dev/null; then
        missing_deps+=("fastmcp")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        echo -e "${YELLOW}警告: 发现缺失的Python依赖: ${missing_deps[*]}${NC}"
        echo -e "${YELLOW}正在安装依赖...${NC}"
        pip3 install -r requirements.txt
    fi
    
    echo -e "${GREEN}✓ Python依赖检查通过${NC}"
}

# 检查硬件设备
check_hardware() {
    echo -e "${YELLOW}检查硬件设备...${NC}"
    
    # 检查摄像头设备
    if [ ! -e "/dev/video0" ]; then
        echo -e "${YELLOW}警告: 未找到摄像头设备 /dev/video0${NC}"
    else
        echo -e "${GREEN}✓ 摄像头设备可用${NC}"
    fi
    
    # 检查串口设备
    if [ ! -e "/dev/ttyS1" ]; then
        echo -e "${YELLOW}警告: 未找到串口设备 /dev/ttyS1${NC}"
    else
        echo -e "${GREEN}✓ 串口设备可用${NC}"
    fi
}

# 检查模型文件
check_model() {
    echo -e "${YELLOW}检查模型文件...${NC}"
    
    local model_file="models/yolo11n_shape_detect_bayese_320x320_nv12_fix_forward.bin"
    
    if [ ! -f "$model_file" ]; then
        echo -e "${RED}错误: 未找到模型文件 $model_file${NC}"
        echo -e "${YELLOW}请确保模型文件存在于正确位置${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 模型文件检查通过${NC}"
}

# 创建必要的目录
create_directories() {
    echo -e "${YELLOW}创建必要的目录...${NC}"
    
    mkdir -p logs
    mkdir -p models
    
    echo -e "${GREEN}✓ 目录创建完成${NC}"
}

# 启动服务
start_server() {
    local config_file="$1"

    echo -e "${GREEN}启动智能视觉机械臂控制系统...${NC}"

    # 构建启动命令
    local cmd="python3 -m vision_arm_mcp.main"

    if [ -n "$config_file" ]; then
        cmd="$cmd --config $config_file"
        echo -e "${BLUE}配置文件: $config_file${NC}"
    else
        echo -e "${BLUE}配置文件: config/default.yaml (默认)${NC}"
    fi

    echo -e "${BLUE}启动命令: $cmd${NC}"
    echo ""

    # 启动服务
    exec $cmd
}

# 主函数
main() {
    local config_file=""

    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -c|--config)
                config_file="$2"
                shift 2
                ;;
            -h|--help)
                print_banner
                print_usage
                exit 0
                ;;
            *)
                echo -e "${RED}错误: 未知参数 $1${NC}"
                print_usage
                exit 1
                ;;
        esac
    done

    # 验证配置文件
    if [ -n "$config_file" ] && [ ! -f "$config_file" ]; then
        echo -e "${RED}错误: 配置文件不存在 '$config_file'${NC}"
        exit 1
    fi

    # 执行启动流程
    print_banner
    check_dependencies
    check_python_dependencies
    check_hardware
    check_model
    create_directories
    start_server "$config_file"
}

# 信号处理
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; exit 0' INT TERM

# 执行主函数
main "$@"
