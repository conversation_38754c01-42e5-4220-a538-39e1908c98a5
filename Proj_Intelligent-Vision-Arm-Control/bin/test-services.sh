#!/bin/bash

# 服务连通性快速测试脚本
# Quick Service Connectivity Test Script

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印横幅
print_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    智能视觉机械臂控制系统 - 服务连通性测试"
    echo "    Intelligent Vision Arm Control - Service Test"
    echo "=================================================="
    echo -e "${NC}"
}

# 测试HTTP端点
test_http_endpoint() {
    local url=$1
    local name=$2
    local timeout=${3:-5}
    local show_response=${4:-false}

    echo -n "  测试 $name ... "

    if command -v curl &> /dev/null; then
        # 使用curl测试，对于视频流只检查HTTP状态码
        if [[ "$url" == *"video_feed"* ]]; then
            local http_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time $timeout --connect-timeout $timeout "$url" 2>/dev/null)
            if [ "$http_code" = "200" ]; then
                echo -e "${GREEN}✓ 可用${NC}"
                return 0
            else
                echo -e "${RED}✗ 不可用 (HTTP $http_code)${NC}"
                return 1
            fi
        else
            local response=$(curl -s --max-time $timeout --connect-timeout $timeout "$url" 2>/dev/null)
            local exit_code=$?

            if [ $exit_code -eq 0 ]; then
                echo -e "${GREEN}✓ 可用${NC}"
                if [ "$show_response" = "true" ] && [ -n "$response" ]; then
                    # 显示响应的前100个字符
                    local preview=$(echo "$response" | head -c 100 | tr '\n' ' ')
                    echo "    响应预览: $preview..."
                fi
                return 0
            else
                echo -e "${RED}✗ 不可用${NC}"
                return 1
            fi
        fi
    elif command -v wget &> /dev/null; then
        # 使用wget测试
        if wget -q --timeout=$timeout --tries=1 --spider "$url" > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 可用${NC}"
            return 0
        else
            echo -e "${RED}✗ 不可用${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}? 无法测试 (缺少curl或wget)${NC}"
        return 2
    fi
}

# 测试API端点并解析JSON
test_api_endpoint() {
    local url=$1
    local name=$2
    local timeout=${3:-5}

    echo -n "  测试 $name ... "

    if command -v curl &> /dev/null; then
        local response=$(curl -s --max-time $timeout --connect-timeout $timeout "$url" 2>/dev/null)
        local exit_code=$?

        if [ $exit_code -eq 0 ]; then
            # 检查是否是有效的JSON
            if echo "$response" | python3 -m json.tool > /dev/null 2>&1; then
                echo -e "${GREEN}✓ 可用 (JSON)${NC}"
                # 尝试提取一些有用信息
                if echo "$response" | grep -q '"status"'; then
                    local status=$(echo "$response" | python3 -c "import sys, json; data=json.load(sys.stdin); print(data.get('status', 'unknown'))" 2>/dev/null)
                    echo "    状态: $status"
                fi
            else
                echo -e "${GREEN}✓ 可用 (非JSON)${NC}"
            fi
            return 0
        else
            echo -e "${RED}✗ 不可用${NC}"
            return 1
        fi
    else
        echo -e "${YELLOW}? 无法测试 (缺少curl)${NC}"
        return 2
    fi
}

# 测试端口是否开放
test_port() {
    local host=$1
    local port=$2
    local name=$3
    local timeout=${4:-3}
    
    echo -n "  测试 $name (端口 $port) ... "
    
    if command -v nc &> /dev/null; then
        # 使用netcat测试
        if nc -z -w$timeout "$host" "$port" 2>/dev/null; then
            echo -e "${GREEN}✓ 开放${NC}"
            return 0
        else
            echo -e "${RED}✗ 关闭${NC}"
            return 1
        fi
    elif command -v telnet &> /dev/null; then
        # 使用telnet测试
        if timeout $timeout bash -c "echo >/dev/tcp/$host/$port" 2>/dev/null; then
            echo -e "${GREEN}✓ 开放${NC}"
            return 0
        else
            echo -e "${RED}✗ 关闭${NC}"
            return 1
        fi
    else
        # 使用bash内置的TCP连接测试
        if timeout $timeout bash -c "exec 3<>/dev/tcp/$host/$port && exec 3<&- && exec 3>&-" 2>/dev/null; then
            echo -e "${GREEN}✓ 开放${NC}"
            return 0
        else
            echo -e "${RED}✗ 关闭${NC}"
            return 1
        fi
    fi
}

# 获取配置信息
get_config_info() {
    local config_file="config/default.yaml"
    
    if [ -f "$config_file" ]; then
        # 从YAML文件中提取端口信息
        web_port=$(grep -A 5 "^web:" "$config_file" | grep "port:" | awk '{print $2}')
        mcp_port=$(grep -A 5 "^mcp:" "$config_file" | grep "port:" | awk '{print $2}')
        
        # 设置默认值
        web_port=${web_port:-5002}
        mcp_port=${mcp_port:-8002}
    else
        echo -e "${YELLOW}警告: 配置文件不存在，使用默认端口${NC}"
        web_port=5002
        mcp_port=8002
    fi
}

# 检测进程
check_processes() {
    echo -e "${BLUE}🔍 检查服务进程${NC}"
    echo "----------------------------------------"
    
    if pgrep -f "vision_arm_mcp" > /dev/null; then
        echo -e "  ${GREEN}✓ 发现vision_arm_mcp进程${NC}"
        echo "    进程ID: $(pgrep -f vision_arm_mcp | tr '\n' ' ')"
    else
        echo -e "  ${RED}✗ 未发现vision_arm_mcp进程${NC}"
    fi
    
    if pgrep -f "python.*main.py" > /dev/null; then
        echo -e "  ${GREEN}✓ 发现Python主进程${NC}"
    else
        echo -e "  ${YELLOW}? 未发现Python主进程${NC}"
    fi
    
    echo ""
}

# 主测试函数
main() {
    print_banner
    
    # 获取配置
    get_config_info
    
    echo -e "${BLUE}📋 配置信息${NC}"
    echo "----------------------------------------"
    echo "  Web服务端口: $web_port"
    echo "  MCP服务端口: $mcp_port"
    echo ""
    
    # 检查进程
    check_processes
    
    # 测试端口连通性
    echo -e "${BLUE}🌐 测试端口连通性${NC}"
    echo "----------------------------------------"
    
    local port_success=0
    local port_total=2
    
    if test_port "127.0.0.1" "$web_port" "Web服务"; then
        ((port_success++))
    fi
    
    if test_port "127.0.0.1" "$mcp_port" "MCP服务"; then
        ((port_success++))
    fi
    
    echo ""
    
    # 测试HTTP端点
    echo -e "${BLUE}🔗 测试HTTP端点${NC}"
    echo "----------------------------------------"

    local http_success=0
    local http_total=6

    # Web API端点
    if test_http_endpoint "http://127.0.0.1:$web_port/" "主页"; then
        ((http_success++))
    fi

    if test_api_endpoint "http://127.0.0.1:$web_port/api/status" "系统状态API"; then
        ((http_success++))
    fi

    if test_api_endpoint "http://127.0.0.1:$web_port/api/measurements" "测量数据API"; then
        ((http_success++))
    fi

    if test_api_endpoint "http://127.0.0.1:$web_port/api/config" "配置信息API"; then
        ((http_success++))
    fi

    if test_http_endpoint "http://127.0.0.1:$web_port/video_feed" "视频流"; then
        ((http_success++))
    fi

    # MCP端点 - FastMCP默认使用/mcp/路径
    if test_http_endpoint "http://127.0.0.1:$mcp_port/mcp/" "MCP服务"; then
        ((http_success++))
    fi
    
    echo ""

    # 测试MCP工具（如果MCP服务可用）
    if [ $mcp_port -gt 0 ] && command -v python3 &> /dev/null; then
        echo -e "${BLUE}🛠️  测试MCP工具${NC}"
        echo "----------------------------------------"

        # 使用Python测试MCP工具
        python3 -c "
import json
import sys
try:
    import requests

    # 测试MCP服务基本连接
    try:
        # 测试MCP根路径 - FastMCP使用/mcp/作为默认路径
        response = requests.get('http://127.0.0.1:$mcp_port/mcp/', timeout=5)
        if response.status_code == 200:
            print('  ✓ MCP服务连接正常')

            # 尝试获取服务器信息
            try:
                data = response.json()
                if 'capabilities' in data:
                    caps = data['capabilities']
                    tools_cap = caps.get('tools', {})
                    if tools_cap:
                        print('  ✓ MCP服务支持工具调用')
                    else:
                        print('  ⚠ MCP服务不支持工具调用')
                else:
                    print('  ⚠ MCP服务响应格式异常')
            except json.JSONDecodeError:
                print('  ⚠ MCP服务响应不是JSON格式')

        elif response.status_code == 404:
            print('  ✗ MCP端点不存在 (可能使用不同路径)')
        else:
            print(f'  ⚠ MCP服务响应异常 (HTTP {response.status_code})')

        # 简单测试：尝试发送一个基本的MCP消息
        try:
            mcp_message = {
                'jsonrpc': '2.0',
                'id': 1,
                'method': 'tools/list',
                'params': {}
            }

            response = requests.post('http://127.0.0.1:$mcp_port/mcp/',
                                   json=mcp_message,
                                   headers={'Content-Type': 'application/json'},
                                   timeout=5)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'result' in result and 'tools' in result['result']:
                        tools = result['result']['tools']
                        print(f'  ✓ 发现 {len(tools)} 个MCP工具')
                        for tool in tools[:3]:  # 只显示前3个工具
                            name = tool.get('name', '未知')
                            desc = tool.get('description', '无描述')[:50]
                            print(f'    - {name}: {desc}')
                        if len(tools) > 3:
                            print(f'    ... 还有 {len(tools) - 3} 个工具')
                    else:
                        print('  ⚠ MCP工具列表响应格式异常')
                except json.JSONDecodeError:
                    print('  ⚠ MCP工具列表响应不是JSON格式')
            else:
                print(f'  ⚠ MCP工具列表获取失败 (HTTP {response.status_code})')
                print('  ℹ 这可能是正常的，MCP协议较为复杂')

        except Exception as e:
            print(f'  ⚠ MCP协议测试失败: {str(e)[:50]}')
            print('  ℹ MCP服务可能需要特殊的客户端连接')

    except Exception as e:
        print(f'  ✗ MCP连接测试失败: {str(e)[:50]}')

except ImportError:
    print('  ? 无法测试MCP工具 (缺少requests库)')
except Exception as e:
    print(f'  ✗ MCP工具测试错误: {str(e)[:50]}')
" 2>/dev/null

        echo ""
    fi

    # 测试结果总结
    echo -e "${BLUE}📊 测试结果总结${NC}"
    echo "----------------------------------------"
    echo "  端口连通性: $port_success/$port_total"
    echo "  HTTP端点: $http_success/$http_total"
    
    if [ $port_success -eq $port_total ] && [ $http_success -eq $http_total ]; then
        echo -e "  ${GREEN}🎉 所有测试通过！服务运行正常${NC}"
        exit_code=0
    elif [ $port_success -gt 0 ] || [ $http_success -gt 0 ]; then
        echo -e "  ${YELLOW}⚠️  部分服务可用，请检查未通过的项目${NC}"
        exit_code=1
    else
        echo -e "  ${RED}❌ 所有服务都不可用${NC}"
        echo -e "  ${YELLOW}💡 建议: 请先启动服务${NC}"
        echo -e "     启动命令: ./bin/start-server.sh"
        exit_code=2
    fi
    
    echo ""
    echo -e "${BLUE}🏁 测试完成${NC}"
    
    exit $exit_code
}

# 检查是否在正确的目录
if [ ! -f "config/default.yaml" ]; then
    echo -e "${RED}错误: 请在项目根目录下运行此脚本${NC}"
    echo "当前目录: $(pwd)"
    exit 1
fi

# 运行主函数
main "$@"
