#!/bin/bash

# 智能视觉机械臂控制系统 - 停止脚本
# Intelligent Vision Arm Control System - Stop Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印横幅
print_banner() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  停止智能视觉机械臂控制系统"
    echo "  Stopping Vision Arm Control System"
    echo "=================================================="
    echo -e "${NC}"
}

# 查找并停止进程
stop_processes() {
    echo -e "${YELLOW}查找运行中的服务进程...${NC}"
    
    # 查找Python进程
    local pids=$(pgrep -f "vision_arm_mcp.main" 2>/dev/null || true)
    
    if [ -z "$pids" ]; then
        echo -e "${YELLOW}未找到运行中的服务进程${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}找到进程: $pids${NC}"
    
    # 发送SIGTERM信号
    echo -e "${YELLOW}发送停止信号...${NC}"
    for pid in $pids; do
        if kill -TERM "$pid" 2>/dev/null; then
            echo -e "${GREEN}✓ 已向进程 $pid 发送停止信号${NC}"
        fi
    done
    
    # 等待进程优雅退出
    echo -e "${YELLOW}等待进程优雅退出...${NC}"
    local timeout=10
    local count=0
    
    while [ $count -lt $timeout ]; do
        local running_pids=$(pgrep -f "vision_arm_mcp.main" 2>/dev/null || true)
        if [ -z "$running_pids" ]; then
            echo -e "${GREEN}✓ 所有进程已优雅退出${NC}"
            return 0
        fi
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    echo ""
    
    # 强制终止仍在运行的进程
    local remaining_pids=$(pgrep -f "vision_arm_mcp.main" 2>/dev/null || true)
    if [ -n "$remaining_pids" ]; then
        echo -e "${YELLOW}强制终止剩余进程...${NC}"
        for pid in $remaining_pids; do
            if kill -KILL "$pid" 2>/dev/null; then
                echo -e "${GREEN}✓ 已强制终止进程 $pid${NC}"
            fi
        done
    fi
}

# 清理资源
cleanup_resources() {
    echo -e "${YELLOW}清理系统资源...${NC}"
    
    # 检查并释放摄像头设备
    local camera_users=$(lsof /dev/video* 2>/dev/null | grep -v COMMAND || true)
    if [ -n "$camera_users" ]; then
        echo -e "${YELLOW}发现摄像头设备被占用，尝试释放...${NC}"
        # 这里可以添加更多的清理逻辑
    fi
    
    # 检查并释放串口设备
    local serial_users=$(lsof /dev/ttyS* 2>/dev/null | grep -v COMMAND || true)
    if [ -n "$serial_users" ]; then
        echo -e "${YELLOW}发现串口设备被占用，尝试释放...${NC}"
        # 这里可以添加更多的清理逻辑
    fi
    
    echo -e "${GREEN}✓ 资源清理完成${NC}"
}

# 检查端口占用
check_ports() {
    echo -e "${YELLOW}检查端口占用情况...${NC}"
    
    # 检查Web服务端口 (5002)
    local web_port_users=$(lsof -i :5002 2>/dev/null | grep -v COMMAND || true)
    if [ -n "$web_port_users" ]; then
        echo -e "${YELLOW}端口 5002 仍被占用:${NC}"
        echo "$web_port_users"
    else
        echo -e "${GREEN}✓ 端口 5002 已释放${NC}"
    fi
    
    # 检查MCP服务端口 (8002)
    local mcp_port_users=$(lsof -i :8002 2>/dev/null | grep -v COMMAND || true)
    if [ -n "$mcp_port_users" ]; then
        echo -e "${YELLOW}端口 8002 仍被占用:${NC}"
        echo "$mcp_port_users"
    else
        echo -e "${GREEN}✓ 端口 8002 已释放${NC}"
    fi
}

# 显示系统状态
show_status() {
    echo -e "${BLUE}系统状态:${NC}"
    
    # 检查进程状态
    local running_processes=$(pgrep -f "vision_arm_mcp.main" 2>/dev/null || true)
    if [ -z "$running_processes" ]; then
        echo -e "${GREEN}✓ 服务进程: 已停止${NC}"
    else
        echo -e "${RED}✗ 服务进程: 仍在运行 (PIDs: $running_processes)${NC}"
    fi
    
    # 检查端口状态
    if ! lsof -i :5002 >/dev/null 2>&1; then
        echo -e "${GREEN}✓ Web服务端口 (5002): 已释放${NC}"
    else
        echo -e "${RED}✗ Web服务端口 (5002): 仍被占用${NC}"
    fi
    
    if ! lsof -i :8002 >/dev/null 2>&1; then
        echo -e "${GREEN}✓ MCP服务端口 (8002): 已释放${NC}"
    else
        echo -e "${RED}✗ MCP服务端口 (8002): 仍被占用${NC}"
    fi
}

# 主函数
main() {
    print_banner
    stop_processes
    cleanup_resources
    sleep 2  # 等待资源完全释放
    check_ports
    echo ""
    show_status
    echo ""
    echo -e "${GREEN}智能视觉机械臂控制系统已停止${NC}"
}

# 执行主函数
main "$@"
