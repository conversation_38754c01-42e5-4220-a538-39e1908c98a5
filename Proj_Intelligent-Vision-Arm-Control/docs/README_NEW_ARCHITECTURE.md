# 智能视觉机械臂控制系统 - 新架构说明

## 概述

本项目已重构为统一的MCP服务器架构，将原有的主服务和MCP服务整合为一个统一的入口点。新架构提供了更好的代码组织、配置管理和服务协调。

## 新架构特点

### 1. 统一服务器入口
- **单一启动点**: 通过 `vision_arm_mcp.main` 启动整个系统
- **服务协调**: 自动管理Web服务和MCP服务的生命周期
- **优雅关闭**: 支持信号处理和资源清理

### 2. 模块化架构
```
vision_arm_mcp/
├── __init__.py              # 包初始化
├── main.py                  # 统一入口点
├── config/                  # 配置管理
│   ├── __init__.py
│   ├── settings.py          # 配置数据模型
│   └── config_loader.py     # 配置加载器
├── core/                    # 核心业务逻辑
│   ├── __init__.py
│   ├── detection/           # 物体检测模块
│   ├── arm_control/         # 机械臂控制模块
│   └── utils/               # 工具模块
├── services/                # 服务层
│   ├── __init__.py
│   ├── web_service.py       # Web服务
│   ├── mcp_service.py       # MCP服务
│   └── video_service.py     # 视频流服务
└── api/                     # API层
    ├── __init__.py
    ├── routes.py            # 路由定义
    └── schemas.py           # 数据模型
```

### 3. 配置管理
- **YAML配置**: 使用YAML格式的配置文件
- **环境支持**: 支持开发、生产等不同环境配置
- **数据验证**: 使用Pydantic进行配置验证
- **环境变量**: 支持通过环境变量覆盖配置

### 4. 服务架构
- **Web服务**: Flask应用，提供RESTful API和Web界面
- **MCP服务**: FastMCP服务器，提供AI助手集成接口
- **视频服务**: 专门的视频流处理服务
- **异步协调**: 使用asyncio协调多个服务

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 启动服务
```bash
# 使用启动脚本（推荐）
./bin/start-server.sh

# 或者直接运行Python模块
python -m vision_arm_mcp.main

# 指定配置文件
python -m vision_arm_mcp.main --config config/custom.yaml
```

### 3. 访问服务
- **Web界面**: http://localhost:5002
- **MCP服务**: http://localhost:8002
- **API文档**: http://localhost:5002/api/status

### 4. 停止服务
```bash
# 使用停止脚本
./bin/stop-server.sh

# 或者按 Ctrl+C
```

## 配置说明

### 配置文件位置
- `config/default.yaml`: 默认配置文件

### 环境变量
可以通过环境变量覆盖配置，前缀为 `VISION_ARM_`：
```bash
export VISION_ARM_WEB_PORT=8080
export VISION_ARM_MCP_PORT=9090
export VISION_ARM_LOGGING_CONSOLE_LEVEL=WARNING
```

### 主要配置项
```yaml
# 应用信息
app_name: "智能视觉机械臂控制系统"
version: "2.0.0"
environment: "development"

# Web服务
web:
  host: "0.0.0.0"
  port: 5002

# MCP服务
mcp:
  host: "0.0.0.0"
  port: 8002

# 模型配置
model:
  path: "models/yolo11n_shape_detect_bayese_320x320_nv12_fix_forward.bin"
  confidence_threshold: 0.7

# 摄像头配置
camera:
  device_path: "/dev/video0"
  width: 640
  height: 480

# 机械臂配置
arm:
  serial_port: "/dev/ttyS1"
  baudrate: 115200
```

## API接口

### Web API
- `GET /api/status` - 获取系统状态
- `GET /api/measurements` - 获取所有测量数据
- `GET /api/measurements/<id>` - 获取特定物体测量数据
- `POST /api/arm/catch_object` - 控制机械臂抓取物体
- `POST /api/arm/put_object` - 控制机械臂放置物体
- `GET /api/config` - 获取配置摘要

### MCP工具
- `get_measurements()` - 获取所有测量数据
- `get_measurement_by_id(object_id)` - 获取特定物体数据
- `catch_object(x, y, z)` - 抓取物体
- `put_object(x, y, z)` - 放置物体
- `get_system_status()` - 获取系统状态
- `get_available_tools()` - 获取可用工具列表

## 开发指南

### 添加新功能
1. 在 `core/` 目录下添加核心业务逻辑
2. 在 `services/` 目录下添加服务层代码
3. 在 `api/` 目录下添加API接口
4. 更新配置模型和文档

### 测试
```bash
# 运行测试
pytest tests/

# 代码格式化
black vision_arm_mcp/

# 类型检查
mypy vision_arm_mcp/
```

### 日志
系统使用分级日志管理，支持文件和控制台不同级别的日志输出：

#### 日志级别配置
- **文件日志**: 记录全部等级的日志（DEBUG及以上）
- **控制台日志**: 只输出重要信息（可配置级别）

#### 日志级别配置
- **默认配置**: 文件记录DEBUG级别，控制台显示INFO级别
- **安静模式**: 设置控制台为WARNING级别，只显示警告和错误
- **生产模式**: 设置文件为INFO级别，控制台为ERROR级别

#### 日志文件位置
- `logs/app.log` - 默认日志文件
- `logs/dev.log` - 开发环境日志
- `logs/prod.log` - 生产环境日志

#### 配置示例
```yaml
logging:
  file_level: "DEBUG"      # 文件记录全部等级的日志
  console_level: "INFO"    # 终端输出INFO及以上级别信息
  file_path: "logs/app.log"
```

## 迁移指南

### 从旧架构迁移
1. **配置文件**: 将 `src/config/settings.py` 的配置迁移到YAML格式
2. **启动方式**: 使用新的启动脚本替代原有的启动方式
3. **API调用**: API接口保持兼容，但建议使用新的数据模型
4. **MCP集成**: MCP服务现在自动启动，无需单独启动

### 兼容性说明
- **API兼容**: 现有的API接口保持兼容
- **配置兼容**: 支持通过环境变量使用原有配置
- **功能兼容**: 所有原有功能都已迁移到新架构

## 故障排除

### 常见问题
1. **端口占用**: 检查5002和8002端口是否被占用
2. **摄像头权限**: 确保有访问 `/dev/video0` 的权限
3. **串口权限**: 确保有访问 `/dev/ttyS1` 的权限
4. **模型文件**: 确保模型文件存在于指定路径

### 日志查看
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log
```

### 服务状态检查
```bash
# 检查进程
ps aux | grep vision_arm_mcp

# 检查端口
netstat -tlnp | grep -E ':(5002|8002)'
```

## 更新日志

### v2.0.0 (当前版本)
- 重构为统一MCP服务器架构
- 新增模块化代码组织
- 新增YAML配置管理
- 新增环境支持
- 新增API数据验证
- 新增启动/停止脚本
- 改进日志系统
- 改进错误处理
