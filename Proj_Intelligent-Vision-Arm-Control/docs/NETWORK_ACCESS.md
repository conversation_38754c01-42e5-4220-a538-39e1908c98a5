# 网络访问配置说明

## 概述

智能视觉机械臂控制系统支持通过局域网访问，允许其他设备连接到系统的Web界面和MCP服务。

## 服务端口

- **Web服务**: 端口 5002
- **MCP服务**: 端口 8002

## 访问地址

系统启动时会自动检测并显示所有可用的访问地址：

### 本地访问
- Web界面: http://127.0.0.1:5002
- MCP服务: http://127.0.0.1:8002/mcp/

### 局域网访问
系统会自动检测所有网络接口的IP地址，并标记主要IP（用于路由到外网的IP）：

```
局域网访问地址:
  Web界面: http://**************:5002
  MCP服务: http://**************:8002/mcp/
  Web界面: http://************:5002 (主要)
  MCP服务: http://************:8002/mcp/ (主要)
  Web界面: http://**************:5002
  MCP服务: http://**************:8002/mcp/
```

## 配置说明

### 启用局域网访问

系统默认配置为支持局域网访问。在配置文件中，Web服务绑定到 `0.0.0.0`：

```yaml
# config/default.yaml
web:
  host: "0.0.0.0"  # 监听所有网络接口
  port: 5002
  debug: false

mcp:
  host: "0.0.0.0"  # 监听所有网络接口  
  port: 8002
```

### 仅本地访问

如果只需要本地访问，可以修改配置文件：

```yaml
web:
  host: "127.0.0.1"  # 仅本地访问
  port: 5002
```

## 网络接口说明

系统可能检测到多个网络接口：

1. **主要网络接口** (标记为"主要")
   - 通常是WiFi或以太网连接
   - 用于访问外网的默认路由
   - 推荐使用此IP进行局域网访问

2. **虚拟网络接口**
   - Docker网络 (通常是 192.168.x.x)
   - 虚拟机网络
   - VPN连接

## 故障排除

### 无法通过局域网访问

1. **检查IP地址**
   - 确保使用正确的IP地址（系统启动时显示的地址）
   - 使用标记为"主要"的IP地址

2. **检查网络连接**
   ```bash
   # 测试网络连通性
   ping ************
   
   # 测试端口连接
   telnet ************ 5002
   ```

3. **检查防火墙**
   ```bash
   # 检查端口是否被监听
   netstat -tlnp | grep :5002
   
   # 应该显示: tcp 0 0 0.0.0.0:5002 0.0.0.0:* LISTEN
   ```

4. **检查服务状态**
   - 确保服务正常启动
   - 查看启动日志中的"服务访问地址"部分

### 常见问题

1. **IP地址变化**
   - 系统会动态检测IP地址
   - 重启服务后会显示最新的IP地址
   - DHCP可能导致IP地址变化

2. **多个网络接口**
   - 选择标记为"主要"的IP地址
   - 或者尝试所有显示的IP地址

3. **端口冲突**
   - 确保端口5002和8002未被其他程序占用
   - 可以在配置文件中修改端口号

## 安全注意事项

1. **局域网访问**
   - 仅在可信的局域网环境中启用
   - 确保网络安全

2. **防火墙配置**
   - 根据需要配置防火墙规则
   - 限制访问来源

3. **访问控制**
   - 考虑添加身份验证机制
   - 监控访问日志

## 使用示例

### 从其他设备访问

1. **Web界面访问**
   ```
   在浏览器中打开: http://************:5002
   ```

2. **API访问**
   ```bash
   # 获取系统状态
   curl http://************:5002/api/status
   
   # 获取检测结果
   curl http://************:5002/api/detection
   ```

3. **MCP服务访问**
   ```
   MCP客户端连接: http://************:8002/mcp/
   ```

### 移动设备访问

在移动设备的浏览器中输入：
```
http://************:5002
```

即可访问实时视频流和控制界面。
