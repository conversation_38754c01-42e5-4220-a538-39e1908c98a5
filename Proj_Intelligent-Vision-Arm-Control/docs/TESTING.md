# 测试指南
# Testing Guide

本文档介绍如何测试智能视觉机械臂控制系统的各项功能。

## 快速测试

### 服务连通性测试

使用内置的测试脚本快速检查所有服务是否正常运行：

```bash
./bin/test-services.sh
```

该脚本会自动检测：

- **进程状态**: 检查服务进程是否运行
- **端口连通性**: 测试Web服务(5002)和MCP服务(8002)端口
- **HTTP端点**: 验证所有API端点是否可访问
- **MCP协议**: 基本的MCP服务连通性测试

### 测试结果解读

#### ✅ 成功状态
- `✓ 可用` - 服务正常运行
- `✓ 开放` - 端口正常监听
- `✓ 可用 (JSON)` - API返回有效JSON数据

#### ⚠️ 警告状态
- `⚠ MCP服务响应异常 (HTTP 406)` - 正常现象，MCP协议需要特殊客户端
- `? 未发现Python主进程` - 可能正常，取决于启动方式

#### ❌ 错误状态
- `✗ 不可用` - 服务无法访问
- `✗ 关闭` - 端口未开放

## 详细测试

### 1. Web服务测试

#### 主页访问
```bash
curl http://localhost:5002/
```

#### API端点测试
```bash
# 系统状态
curl http://localhost:5002/api/status

# 测量数据
curl http://localhost:5002/api/measurements

# 配置信息
curl http://localhost:5002/api/config
```

#### 视频流测试
在浏览器中访问：
```
http://localhost:5002/video_feed
```

### 2. MCP服务测试

MCP服务使用特殊的协议格式，需要专门的客户端进行测试。

#### 基本连通性
```bash
curl http://localhost:8002/mcp/
```

#### 使用Python客户端测试
```python
from fastmcp import Client
import asyncio

async def test_mcp():
    async with Client("http://localhost:8002/mcp/") as client:
        # 列出可用工具
        tools = await client.list_tools()
        print(f"可用工具: {len(tools.tools)}")
        
        # 调用工具
        result = await client.call_tool("get_system_status")
        print(f"系统状态: {result}")

asyncio.run(test_mcp())
```

### 3. 集成测试

#### 完整工作流测试
1. 启动服务
2. 检查摄像头连接
3. 验证物体检测
4. 测试机械臂控制
5. 验证MCP工具调用

```bash
# 1. 启动服务
./bin/start-server.sh

# 2. 等待服务启动
sleep 5

# 3. 运行连通性测试
./bin/test-services.sh

# 4. 检查日志
tail -f logs/vision_arm_mcp.log
```

## 故障排除

### 常见问题

#### 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep :5002
netstat -tulpn | grep :8002

# 终止占用进程
sudo kill -9 <PID>
```

#### 服务无法启动
```bash
# 检查配置文件
cat config/default.yaml

# 查看详细日志
python -m vision_arm_mcp.main --config config/default.yaml
```

#### 摄像头无法访问
```bash
# 检查摄像头设备
ls /dev/video*

# 测试摄像头
v4l2-ctl --list-devices
```

#### 机械臂连接失败
```bash
# 检查串口设备
ls /dev/ttyUSB* /dev/ttyACM*

# 检查串口权限
sudo chmod 666 /dev/ttyUSB0
```

### 日志分析

#### 日志文件位置
- 完整日志: `logs/vision_arm_mcp.log`
- 错误日志: 终端输出

#### 常见日志信息
```
INFO - Web服务启动: http://0.0.0.0:5002
INFO - MCP服务启动: http://0.0.0.0:8002
ERROR - 摄像头初始化失败
WARNING - 机械臂连接超时
```

## 性能测试

### 检测性能
```bash
# 监控FPS和推理时间
watch -n 1 "curl -s http://localhost:5002/api/status | jq '.fps, .inference_time'"
```

### 内存使用
```bash
# 监控内存使用
ps aux | grep vision_arm_mcp
```

### CPU使用率
```bash
# 监控CPU使用
top -p $(pgrep -f vision_arm_mcp)
```

## 自动化测试

### 持续集成测试
```bash
#!/bin/bash
# ci-test.sh

set -e

echo "启动服务..."
./bin/start-server.sh &
SERVER_PID=$!

echo "等待服务启动..."
sleep 10

echo "运行测试..."
./bin/test-services.sh

echo "清理..."
kill $SERVER_PID
```

### 定期健康检查
```bash
#!/bin/bash
# health-check.sh

# 添加到crontab: */5 * * * * /path/to/health-check.sh

if ! ./bin/test-services.sh > /dev/null 2>&1; then
    echo "服务健康检查失败，尝试重启..." | logger
    ./bin/stop-server.sh
    sleep 5
    ./bin/start-server.sh
fi
```

## 测试最佳实践

1. **定期测试**: 每次代码更改后运行测试
2. **环境隔离**: 在独立环境中测试
3. **日志监控**: 关注错误和警告信息
4. **性能基准**: 建立性能基准线
5. **自动化**: 使用脚本自动化测试流程

## 相关文档

- [部署指南](DEPLOYMENT.md)
- [配置说明](CONFIGURATION.md)
- [故障排除](TROUBLESHOOTING.md)
- [API文档](API.md)
