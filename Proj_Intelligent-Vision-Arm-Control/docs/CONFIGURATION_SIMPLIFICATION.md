# 配置文件简化说明
# Configuration Simplification Guide

## 简化概述

为了简化项目配置管理，我们将多个环境配置文件合并为单一的默认配置文件，使配置更加简洁和易于维护。

## 变更内容

### 1. 删除的文件
- `config/development.yaml` - 开发环境配置文件
- `config/production.yaml` - 生产环境配置文件

### 2. 保留的文件
- `config/default.yaml` - 唯一的配置文件

### 3. 代码变更

#### 配置加载逻辑简化
**文件**: `vision_arm_mcp/config/config_loader.py`

**变更前**:
- 支持多环境配置文件加载
- 复杂的配置合并逻辑
- 环境变量 `VISION_ARM_ENVIRONMENT` 控制

**变更后**:
- 只加载单一配置文件
- 简化的配置加载逻辑
- 移除环境相关代码

#### 启动脚本简化
**文件**: `bin/start-server.sh`

**变更前**:
```bash
./bin/start-server.sh -e production  # 指定环境
./bin/start-server.sh -e development # 指定环境
```

**变更后**:
```bash
./bin/start-server.sh                    # 使用默认配置
./bin/start-server.sh -c custom.yaml     # 使用自定义配置
```

### 4. 日志配置优化

#### 默认日志级别调整
**变更前**:
```yaml
logging:
  console_level: "WARNING"  # 控制台只显示警告和错误
```

**变更后**:
```yaml
logging:
  console_level: "INFO"     # 控制台显示重要启动信息
```

**原因**: 用户反馈重要的启动信息（如服务地址、初始化状态）应该在终端显示。

## 新的配置方式

### 1. 默认使用
```bash
# 直接启动，使用 config/default.yaml
./bin/start-server.sh
python -m vision_arm_mcp.main
```

### 2. 自定义配置文件
```bash
# 使用自定义配置文件
./bin/start-server.sh -c my-config.yaml
python -m vision_arm_mcp.main --config my-config.yaml
```

### 3. 环境变量覆盖
```bash
# 通过环境变量调整特定配置
export VISION_ARM_WEB_PORT=8080
export VISION_ARM_MCP_PORT=9090
export VISION_ARM_LOGGING_CONSOLE_LEVEL=WARNING

# 然后正常启动
./bin/start-server.sh
```

## 配置示例

### 默认配置 (config/default.yaml)
```yaml
# 智能视觉机械臂控制系统 - 默认配置
app_name: "智能视觉机械臂控制系统"
version: "2.0.0"

# 日志配置
logging:
  file_level: "DEBUG"      # 文件记录全部等级的日志
  console_level: "INFO"    # 终端输出INFO及以上级别信息
  file_path: "logs/app.log"

# Web服务配置
web:
  host: "0.0.0.0"
  port: 5002
  debug: false

# MCP服务配置
mcp:
  host: "0.0.0.0"
  port: 8002

# 模型配置
model:
  path: "models/yolo11n_shape_detect_bayese_320x320_nv12_fix_forward.bin"
  confidence_threshold: 0.7

# 摄像头配置
camera:
  device_path: "/dev/video0"
  width: 640
  height: 480

# 机械臂配置
arm:
  serial_port: "/dev/ttyS1"
  baudrate: 115200
```

### 自定义配置示例
```yaml
# 生产环境配置示例
logging:
  file_level: "INFO"       # 减少文件日志详细程度
  console_level: "ERROR"   # 控制台只显示错误
  file_path: "logs/prod.log"

web:
  port: 8080              # 自定义端口

mcp:
  port: 9090              # 自定义端口

model:
  confidence_threshold: 0.8  # 提高检测阈值
```

## 优势

### 1. 简化维护
- 只需维护一个配置文件
- 减少配置文件间的不一致性
- 降低配置管理复杂度

### 2. 灵活配置
- 通过环境变量灵活调整
- 支持完全自定义配置文件
- 保持向后兼容性

### 3. 清晰明了
- 配置结构更加清晰
- 减少用户困惑
- 降低学习成本

### 4. 易于部署
- 简化部署流程
- 减少环境相关错误
- 统一的启动方式

## 迁移指南

### 如果你之前使用开发环境配置
**之前**:
```bash
./bin/start-server.sh -e development
```

**现在**:
```bash
# 直接使用默认配置（已包含开发友好的设置）
./bin/start-server.sh

# 或者如果需要更详细的控制台输出
export VISION_ARM_LOGGING_CONSOLE_LEVEL=DEBUG
./bin/start-server.sh
```

### 如果你之前使用生产环境配置
**之前**:
```bash
./bin/start-server.sh -e production
```

**现在**:
```bash
# 通过环境变量调整为生产模式
export VISION_ARM_LOGGING_FILE_LEVEL=INFO
export VISION_ARM_LOGGING_CONSOLE_LEVEL=ERROR
./bin/start-server.sh

# 或者创建生产配置文件
./bin/start-server.sh -c production.yaml
```

## 总结

配置文件简化使系统更加易用和易维护，同时保持了足够的灵活性来满足不同的使用场景。通过环境变量和自定义配置文件，用户仍然可以根据需要进行精细的配置调整。

这种简化符合"约定优于配置"的设计原则，为大多数用户提供了开箱即用的体验，同时为高级用户保留了完全的自定义能力。
