# 日志配置说明
# Logging Configuration Guide

## 概述

智能视觉机械臂控制系统采用分级日志管理策略，实现了文件和控制台不同级别的日志输出，既保证了完整的日志记录，又避免了控制台信息过载。

## 配置原理

### 双级别日志系统
- **文件日志级别** (`file_level`): 控制写入日志文件的最低级别
- **控制台日志级别** (`console_level`): 控制终端显示的最低级别

### 日志级别优先级
```
DEBUG < INFO < WARNING < ERROR < CRITICAL
```

## 配置说明

### 默认配置 (default.yaml)
```yaml
logging:
  file_level: "DEBUG"      # 文件记录全部等级的日志
  console_level: "INFO"    # 终端输出INFO及以上级别信息
  file_path: "logs/app.log"
```

**特点**:
- 文件记录完整日志，便于问题排查
- 控制台显示重要的启动信息、警告和错误

### 自定义配置
可以通过环境变量或自定义配置文件调整日志级别：

**通过环境变量**:
```bash
# 安静模式 - 控制台只显示警告和错误
export VISION_ARM_LOGGING_CONSOLE_LEVEL=WARNING

# 生产模式 - 减少文件日志，控制台只显示错误
export VISION_ARM_LOGGING_FILE_LEVEL=INFO
export VISION_ARM_LOGGING_CONSOLE_LEVEL=ERROR
```

**通过自定义配置文件**:
```yaml
logging:
  file_level: "INFO"       # 减少文件日志详细程度
  console_level: "WARNING" # 控制台只显示警告和错误
  file_path: "logs/custom.log"
```

## 配置参数详解

### 完整配置示例
```yaml
logging:
  file_level: "DEBUG"                    # 文件日志级别
  console_level: "INFO"                  # 控制台日志级别
  format: "[%(name)s] [%(asctime)s.%(msecs)03d] [%(levelname)s] %(message)s"
  datefmt: "%H:%M:%S"                   # 时间格式
  file_path: "logs/app.log"             # 日志文件路径
  max_file_size: 10485760               # 日志文件最大大小(10MB)
  backup_count: 5                       # 日志文件备份数量
```

### 参数说明
- `file_level`: 文件日志记录的最低级别
- `console_level`: 控制台显示的最低级别
- `format`: 日志格式模板
- `datefmt`: 时间戳格式
- `file_path`: 日志文件保存路径
- `max_file_size`: 单个日志文件最大大小（字节）
- `backup_count`: 日志轮转时保留的备份文件数量

## 使用示例

### 代码中使用日志
```python
import logging

# 获取日志器
logger = logging.getLogger("MyModule")

# 不同级别的日志
logger.debug("详细的调试信息")      # 只在文件中记录
logger.info("一般信息")           # 根据配置决定是否在控制台显示
logger.warning("警告信息")        # 通常在控制台和文件中都显示
logger.error("错误信息")          # 在控制台和文件中都显示
logger.critical("严重错误")       # 在控制台和文件中都显示
```

### 测试日志配置
```python
from vision_arm_mcp.config import load_config, setup_logging
import logging

# 加载配置
config = load_config()
setup_logging(config.logging)

# 测试不同级别的日志
logger = logging.getLogger('TestLogger')
logger.debug('DEBUG级别测试')
logger.info('INFO级别测试')
logger.warning('WARNING级别测试')
logger.error('ERROR级别测试')
```

## 日志文件管理

### 自动轮转
系统使用 `RotatingFileHandler` 实现日志文件自动轮转：
- 当日志文件达到最大大小时自动创建新文件
- 保留指定数量的备份文件
- 自动删除过期的备份文件

### 文件命名规则
```
logs/app.log        # 当前日志文件
logs/app.log.1      # 第一个备份文件
logs/app.log.2      # 第二个备份文件
...
```

## 最佳实践

### 1. 选择合适的日志级别
- **DEBUG**: 详细的程序执行信息，仅用于开发调试
- **INFO**: 程序正常运行的关键信息
- **WARNING**: 可能的问题或异常情况
- **ERROR**: 错误信息，但程序可以继续运行
- **CRITICAL**: 严重错误，可能导致程序终止

### 2. 配置建议
- **开发调试**: 使用DEBUG文件级别和INFO控制台级别（默认）
- **安静模式**: 使用DEBUG文件级别和WARNING控制台级别
- **生产部署**: 使用INFO文件级别和ERROR控制台级别

### 3. 性能考虑
- 避免在生产环境使用DEBUG级别的文件日志
- 合理设置日志文件大小和备份数量
- 定期清理过期的日志文件

## 故障排除

### 常见问题

1. **控制台没有日志输出**
   - 检查 `console_level` 配置是否过高
   - 确认日志级别设置正确

2. **日志文件没有生成**
   - 检查 `file_path` 路径是否正确
   - 确认程序有写入权限

3. **日志文件过大**
   - 调整 `max_file_size` 参数
   - 增加 `backup_count` 数量

### 调试命令
```bash
# 查看实时日志
tail -f logs/app.log

# 查看错误日志
grep ERROR logs/app.log

# 查看最近的日志
tail -100 logs/app.log
```

## 环境变量覆盖

可以通过环境变量覆盖日志配置：
```bash
export VISION_ARM_LOGGING_FILE_LEVEL=INFO
export VISION_ARM_LOGGING_CONSOLE_LEVEL=ERROR
export VISION_ARM_LOGGING_FILE_PATH=/custom/path/app.log
```

## 总结

新的分级日志配置实现了：
- ✅ 文件记录完整日志信息，便于问题排查
- ✅ 控制台只显示重要信息，避免信息过载
- ✅ 不同环境使用不同的日志策略
- ✅ 灵活的配置选项，满足各种需求
- ✅ 自动日志轮转，避免文件过大

这种设计既保证了日志的完整性，又提升了用户体验，是生产级应用的最佳实践。
