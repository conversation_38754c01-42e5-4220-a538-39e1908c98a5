#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
统一服务器测试
Unified Server Tests

测试新架构的统一服务器功能。
"""

import pytest
import asyncio
import tempfile
import yaml
from pathlib import Path

# 添加项目根目录到Python路径
import sys
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from vision_arm_mcp.config import load_config, setup_logging
from vision_arm_mcp.main import UnifiedServer

class TestUnifiedServer:
    """统一服务器测试类"""
    
    def test_config_loading(self):
        """测试配置加载"""
        # 创建临时配置文件
        config_data = {
            "app_name": "Test App",
            "version": "1.0.0",
            "environment": "test",
            "logging": {
                "file_level": "DEBUG",
                "console_level": "ERROR"
            },
            "web": {
                "host": "127.0.0.1",
                "port": 5003
            },
            "mcp": {
                "host": "127.0.0.1", 
                "port": 8003
            },
            "model": {
                "path": "/tmp/test_model.bin",
                "confidence_threshold": 0.5
            },
            "camera": {
                "device_path": "/dev/null",
                "width": 320,
                "height": 240
            },
            "arm": {
                "serial_port": "/dev/null",
                "baudrate": 9600
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            # 加载配置
            config = load_config(config_path)
            
            # 验证配置
            assert config.app_name == "Test App"
            assert config.version == "1.0.0"
            assert config.environment == "test"
            assert config.web.port == 5003
            assert config.mcp.port == 8003
            assert config.model.confidence_threshold == 0.5
            
        finally:
            # 清理临时文件
            Path(config_path).unlink()
    
    def test_server_initialization(self):
        """测试服务器初始化"""
        # 创建临时配置
        config_data = {
            "app_name": "Test Server",
            "logging": {"file_level": "ERROR", "console_level": "ERROR"},  # 减少日志输出
            "model": {"path": "/tmp/nonexistent_model.bin"},
            "web": {"port": 5004},
            "mcp": {"port": 8004}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            # 初始化服务器（不启动）
            server = UnifiedServer(config_path)
            
            # 验证初始化
            assert server.config.app_name == "Test Server"
            assert server.config.web.port == 5004
            assert server.config.mcp.port == 8004
            assert server.running == False
            assert server.model is None
            assert server.web_service is None
            assert server.mcp_service is None
            
        finally:
            Path(config_path).unlink()
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试无效配置
        invalid_config = {
            "web": {"port": "invalid_port"},  # 端口应该是数字
            "model": {"confidence_threshold": 1.5}  # 阈值应该在0-1之间
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(invalid_config, f)
            config_path = f.name
        
        try:
            # 应该抛出验证错误
            with pytest.raises(Exception):
                load_config(config_path)
                
        finally:
            Path(config_path).unlink()
    
    def test_default_config(self):
        """测试默认配置"""
        config = load_config()
        
        # 验证默认值
        assert config.app_name == "智能视觉机械臂控制系统"
        assert config.version == "2.0.0"
        assert config.web.port == 5002
        assert config.mcp.port == 8002
        assert config.model.confidence_threshold == 0.7
        assert config.camera.width == 640
        assert config.camera.height == 480

@pytest.mark.asyncio
class TestAsyncFunctions:
    """异步函数测试类"""
    
    async def test_server_lifecycle(self):
        """测试服务器生命周期（模拟）"""
        # 创建测试配置
        config_data = {
            "logging": {"level": "ERROR"},
            "model": {"path": "/tmp/nonexistent_model.bin"},
            "web": {"port": 5005},
            "mcp": {"port": 8005}
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        try:
            server = UnifiedServer(config_path)
            
            # 测试停止方法（不启动服务器）
            await server.stop()
            assert server.running == False
            
        finally:
            Path(config_path).unlink()

def test_import_structure():
    """测试导入结构"""
    # 测试主要模块可以正常导入
    from vision_arm_mcp.config import load_config, setup_logging
    from vision_arm_mcp.main import UnifiedServer
    
    # 测试服务模块
    from vision_arm_mcp.services import WebService, MCPService, VideoService
    
    # 测试API模块
    from vision_arm_mcp.api import schemas, routes
    
    # 测试核心模块
    from vision_arm_mcp.core.detection import YOLO11_Detect, BaseModel
    from vision_arm_mcp.core.arm_control import RobotArm, FourDofArm
    from vision_arm_mcp.core.utils import MeasurementCollection, FPSCounter

if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
