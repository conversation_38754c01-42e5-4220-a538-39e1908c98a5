# 智能视觉机械臂控制系统 - 默认配置
# Intelligent Vision Arm Control System - Default Configuration

# 应用基本信息
app_name: "智能视觉机械臂控制系统"
version: "2.0.0"
environment: "development"

# 日志配置
logging:
  file_level: "DEBUG"      # 文件记录全部等级的日志
  console_level: "INFO"    # 终端输出INFO及以上级别信息
  format: "[%(name)s] [%(asctime)s.%(msecs)03d] [%(levelname)s] %(message)s"
  datefmt: "%H:%M:%S"
  file_path: "logs/app.log"
  max_file_size: 10485760  # 10MB
  backup_count: 5

# 模型配置
model:
  path: "models/yolo11n_shape_detect_bayese_320x320_nv12_fix_forward.bin"
  confidence_threshold: 0.7
  iou_threshold: 0.45

# 摄像头配置
camera:
  device_path: "/dev/video0"
  width: 640
  height: 480
  fps: 30

# Web服务配置
web:
  host: "0.0.0.0"
  port: 5002
  debug: false
  threaded: true

# MCP服务配置
mcp:
  host: "0.0.0.0"
  port: 8002
  name: "VisionArmServer"
  description: "智能视觉机械臂控制系统MCP服务器"

# 机械臂配置
arm:
  serial_port: "/dev/ttyS1"
  baudrate: 115200
  calibration:
    x_coefficients: [0.000004, 0.9879, -2.9465]
    y_coefficients: [0.0005, 0.9638, 228.29]
  workspace_limits:
    x_range: [-200, 200]
    y_range: [100, 400]
    z_range: [30, 150]

# 检测配置
detection:
  class_names: ["orange", "green", "red"]
  colors:
    - [255, 165, 0]  # 橙色
    - [0, 255, 0]    # 绿色
    - [255, 0, 0]    # 红色
  font_scale: 1.0    # 字体缩放比例
  line_thickness: 2  # 检测框线条粗细

# 测量配置
measurement:
  enable_coordinate_system: true
  enable_distance_measurement: true
  enable_dimension_measurement: true
  axis_color: [255, 255, 255]
  measurement_color: [255, 255, 0]
  axis_length_percentage: 0.8
  axis_thickness: 2
  measurement_line_thickness: 2
